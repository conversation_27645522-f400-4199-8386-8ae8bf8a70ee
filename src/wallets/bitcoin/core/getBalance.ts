import { getWalletState } from '../state';
import { BigNumber, utils } from 'ethers';

const getBalance = async () => {
  const { connector } = getWalletState();
  if (!connector) return undefined;

  const res = await connector.getBalance();
  if (!res) return undefined;

  const decimals = 8;
  const spendable = BigNumber.from(res.confirmed);

  return {
    decimals,
    symbol: 'BTC',
    value: spendable.toBigInt(),
    formatted: utils.formatUnits(spendable, decimals),
  };
};

export default getBalance;
