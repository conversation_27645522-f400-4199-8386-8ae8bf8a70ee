import { NetworkType } from '@/wallets/config/type';
import {
  BitcoinEvent,
  type BTCWallet,
  EventType,
  type IconType,
  ProviderNotFoundError,
} from '../types';
import { WalletDriver, WalletDriverName } from '@/hooks/wallet/common';
import SafepalIcon from '@/components/icons/SafepalIcon';
import { AbstractConnector } from './abstract';

export class SafepalConnector extends AbstractConnector<BTCWallet> implements WalletDriver {
  id: WalletDriverName = WalletDriverName.SafepalBitcoin;
  name: string = 'SafePal Wallet';
  wallet: BTCWallet | null = null;
  network: NetworkType = NetworkType.btc;
  website: string = 'https://www.safepal.com/download';
  installed: boolean = false;
  icon: IconType = SafepalIcon;
  isVisible = ({ isMobile }: { isMobile: boolean }) => isMobile;

  private accountChangeListener: ((accounts: string[]) => void) | null = null;
  private networkChangeListener: ((chainId: string) => void) | null = null;

  constructor() {
    super();

    const wallet = SafepalConnector.discover();
    if (wallet) {
      this.wallet = wallet;
      this.installed = true;
    }
  }

  get provider() {
    if (this.wallet) {
      return this.wallet;
    }

    const wallet = SafepalConnector.discover();
    if (wallet) {
      this.wallet = wallet;
      this.installed = true;
      return this.wallet;
    }

    throw new ProviderNotFoundError();
  }

  static discover(): BTCWallet | null {
    if (typeof window !== 'undefined' && window.safepalBRC20Provider) {
      return window.safepalBRC20Provider as BTCWallet;
    }
    return null;
  }

  onAccountsChanged(handler: (accounts: string[]) => void) {
    if (!this.accountChangeListener) {
      this.provider.on('accountsChanged', handler);
      this.accountChangeListener = handler;
    }
  }

  onNetworkChanged(handler: (chainId: string) => void) {
    if (!this.networkChangeListener) {
      this.provider.on('networkChanged', handler);
      this.networkChangeListener = handler;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  removeAllListeners<E extends BitcoinEvent>(_event: EventType<E>) {
    // there is no removeAllListeners method in safepal provider
  }
}
