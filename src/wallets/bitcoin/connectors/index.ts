import { UniSatConnector } from './unisat';
import { OKXConnector } from './okx';
import { BitgetConnector } from './bitget';
import { TokenPocketConnector } from './tokenpocket';
// import { GateConnector } from './gate';
import { BybitConnector } from './bybit';
import { XverseConnector } from './xverse';
import { BinanceConnector } from './binance';
import { SafepalConnector } from './safepal';

const unisatConnector = new UniSatConnector();
const okxConnector = new OKXConnector();
const bitgetConnector = new BitgetConnector();
const tokenPocketConnector = new TokenPocketConnector();
// const gateConnector = new GateConnector();
const bybitConnector = new BybitConnector();
const xverseConnector = new XverseConnector();
const binanceConnector = new BinanceConnector();
const safepalConnector = new SafepalConnector();

const commonConnectors = [
  unisatConnector,
  okxConnector,
  safepalConnector,
  bybitConnector,
  bitgetConnector,
  xverseConnector,
];

// PC connectors
const connectors = [...commonConnectors];
// Mobile connectors
const mobileConnectors = [binanceConnector, ...commonConnectors, tokenPocketConnector];

const testnetConnectors = [unisatConnector, safepalConnector];

export { connectors, testnetConnectors, mobileConnectors };
