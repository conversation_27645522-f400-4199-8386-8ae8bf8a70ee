import { WalletDriverName } from '@/hooks/wallet/common';
import { injected } from 'wagmi/connectors';

export const inSafepalApp = () => {
  return window.navigator.userAgent.includes('Safepal');
};

export const safepal = injected({
  target() {
    let provider: any = undefined;
    if (typeof window !== 'undefined') {
      if (window.safepalProvider) {
        provider = window.safepalProvider;
      }
    }
    return {
      id: WalletDriverName.SafepalMobile,
      name: 'Safepal',
      provider,
    };
  },
});
