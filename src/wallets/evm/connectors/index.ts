import { walletConnectConnector } from './wallectConnect';
import { gateWallet } from './gatewallet';
import { tokenPocket } from './token-pocket';
import { deBox } from './deBox';
import { uxuyWallet } from './uxuy';
import { tomoTGWallet } from './tomo';
import { binance } from './binance';
import { safepal } from './safepal';

export {
  walletConnectConnector,
  tokenPocket,
  deBox,
  uxuyWallet,
  gateWallet,
  tomoTGWallet,
  binance,
  safepal,
};
