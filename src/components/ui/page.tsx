import { motion } from 'framer-motion';
import { useNavigationType } from '@remix-run/react';
import { cn } from '@/lib/utils';

export function AnimatePageLayout({ children }: { children: React.ReactNode }) {
  const navigationType = useNavigationType();

  return (
    <motion.div
      initial={{ x: navigationType === 'PUSH' ? 300 : -300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: 300, opacity: 0 }}
      transition={{
        type: 'spring',
        stiffness: 260,
        damping: 20,
      }}
    >
      {children}
    </motion.div>
  );
}

export function PageCard({
  after,
  before,
  children,
  className,
  containerClassName,
}: {
  after?: React.ReactNode;
  before?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
  containerClassName?: string;
}) {
  return (
    <section
      className={cn(
        'bl-container bl-w-screen bl-flex bl-flex-col bl-items-center bl-justify-center bl-py-24 md:bl-py-30 bl-font-body',
        containerClassName,
      )}
    >
      <div className="bl-w-full md:bl-w-[600px] bl-mt-7.5 md:bl-mt-5 bl-space-y-3">
        {before}
        <div
          className={cn(
            'bl-bg-card-background bl-space-y-3 bl-border bl-border-card-border bl-p-2.5 md:bl-p-7.5',
            className,
          )}
        >
          {children}
        </div>
        {after}
      </div>
    </section>
  );
}
