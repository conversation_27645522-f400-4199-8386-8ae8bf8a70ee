import * as React from 'react';
import { Slot, Slottable } from '@radix-ui/react-slot';
import { cva } from 'class-variance-authority';
import type { VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { LoaderIcon } from 'lucide-react';

const buttonVariants = cva(
  'bl-group/button bl-inline-flex bl-items-center bl-justify-center bl-whitespace-nowrap bl-rounded-md bl-text-xl/none bl-font-medium bl-ring-offset-background bl-transition-colors focus-visible:bl-outline-none focus-visible:bl-ring-2 focus-visible:bl-ring-ring focus-visible:bl-ring-offset-2 disabled:bl-pointer-events-none disabled:bl-opacity-50 bl-relative bl-appearance-none *:bl-relative *:bl-z-10 group',
  {
    variants: {
      variant: {
        default: 'btn-mask bl-bg-primary bl-text-black hover:bl-text-white',
        destructive: 'bl-bg-destructive bl-text-destructive-foreground hover:bl-bg-destructive/90',
        outline:
          'btn-mask bl-border bl-border-secondary bl-bg-background bl-text-white hover:bl-text-accent-foreground',
        'outline-2':
          'btn-mask bl-border bl-border-secondary bl-bg-background bl-text-white hover:bl-border-primary hover:bl-text-accent-foreground',
        'outline-3':
          'btn-mask bl-border bl-border-input bl-bg-background bl-text-white hover:bl-text-accent-foreground',
        'outline-4':
          'btn-mask bl-border bl-border-primary bl-bg-background bl-text-white hover:bl-text-accent-foreground',
        'outline-5':
          'bl-overflow-hidden bl-border-r bl-border-transparent bl-bg-transparent bl-text-black',
        'outline-6':
          'bl-overflow-hidden bl-border-r bl-border-transparent bl-bg-transparent bl-text-black',
        secondary: 'btn-mask bl-bg-secondary bl-text-secondary-foreground hover:bl-text-white',
        dark: 'btn-mask bl-bg-black bl-text-white hover:bl-text-black',
        dark2: 'btn-mask bl-bg-black bl-text-white hover:bl-text-primary',
        dark3: 'btn-mask bl-bg-black bl-text-secondary hover:bl-text-primary',
        dark4: 'btn-mask bl-bg-black bl-text-secondary hover:bl-text-primary',
        white: 'btn-mask bl-bg-white bl-text-black hover:bl-text-white',
        ghost: 'bl-text-primary hover:bl-bg-primary hover:bl-text-black',
        link: 'bl-text-primary bl-underline-offset-4 hover:bl-underline',
        white2:
          'btn-mask bl-border bl-border-black bl-bg-white bl-text-black hover:bl-border-primary hover:bl-text-white',
        outlineError:
          'btn-mask bl-border bl-border-button-error bl-bg-background bl-text-button-error hover:bl-text-accent-foreground',
      },
      size: {
        default: 'bl-h-10 bl-px-4 bl-py-2',
        xs: 'bl-h-7 bl-px-3 bl-text-base/none btn-xs',
        sm: 'bl-h-9 bl-rounded-md bl-px-3',
        md: 'bl-h-[30px] bl-min-w-[116px] sm:bl-h-11 sm:bl-min-w-[136px] sm:bl-text-[21px]/none',
        lg: 'bl-h-12 bl-min-w-[196px] md:bl-min-w-[230px] bl-rounded-md bl-px-8',
        icon: 'bl-h-10 bl-w-10',
      },
      overlayFrom: {
        bottom: '',
        top: '',
        left: '',
        right: '',
        none: '',
      },
      overlayVariant: {
        default: '',
        secondary: '',
        outline: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

const buttonOverlayVariants = cva(
  '!bl-absolute !bl-z-0 bl-h-full bl-w-full bl-rounded-xl bl-scale-75 bl-transition-all bl-duration-500 group-hover/button:bl-scale-150 group-disabled:bl-opacity-0',
  {
    variants: {
      variant: {
        default: 'bl-bg-black',
        destructive: 'bl-bg-destructive',
        outline: 'bl-bg-secondary',
        'outline-2': 'bl-bg-primary',
        'outline-3': 'bl-bg-secondary',
        'outline-4': 'bl-bg-primary',
        'outline-5': 'bl-bg-primary',
        'outline-6': 'bl-bg-primary',
        secondary: 'bl-bg-primary',
        dark: 'bl-bg-secondary',
        dark2: 'bl-bg-white',
        dark3: 'bl-bg-[#222222]',
        dark4: 'bl-bg-[#222222] group-hover:bl-bg-primary/15',
        ghost: 'bl-bg-accent',
        link: 'bl-bg-primary',
        white: 'bl-bg-primary',
        white2: 'bl-bg-primary',
        outlineError: 'bl-bg-secondary',
      },
      overlayFrom: {
        bottom: 'bl-translate-y-full group-hover/button:bl-translate-y-0',
        top: '-bl-translate-y-full group-hover/button:bl-translate-y-0',
        left: '-bl-translate-x-full group-hover/button:bl-translate-x-0',
        right: 'bl-translate-x-full group-hover/button:bl-translate-x-0',
        none: 'bl-invisible group-hover/button:bl-invisible',
      },
    },
    defaultVariants: {
      variant: 'default',
      overlayFrom: 'bottom',
    },
  },
);

const ghostBorderVariants = cva(
  'bl-h-1.5 bl-w-1.5 sm:bl-h-2 sm:bl-w-2 bl-border-primary !bl-absolute bl-duration-200 group-hover/button:bl-border-black',
  {
    variants: {
      variant: {
        topLeft:
          'bl-border-t bl-border-l bl-top-0 bl-left-0 group-hover/button:bl-translate-x-1/2 group-hover/button:bl-translate-y-1/2',
        topRight:
          'bl-border-t bl-border-r bl-top-0 bl-right-0 group-hover/button:-bl-translate-x-1/2 group-hover/button:bl-translate-y-1/2',
        bottomLeft:
          'bl-border-b bl-border-l bl-bottom-0 bl-left-0 group-hover/button:bl-translate-x-1/2 group-hover/button:-bl-translate-y-1/2',
        bottomRight:
          'bl-border-b bl-border-r bl-bottom-0 bl-right-0 group-hover/button:-bl-translate-x-1/2 group-hover/button:-bl-translate-y-1/2',
      },
    },
    defaultVariants: {
      variant: 'topLeft',
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  outlineClassName?: string;
  keepOverlay?: boolean;
  noMask?: boolean;
  noOverlay?: boolean;
  loading?: boolean;
}

const BackgroundOverlay = ({ variant, overlayVariant, overlayFrom, keepOverlay }: ButtonProps) => {
  if (variant === 'ghost') return null;
  if (variant === 'outline-5' || variant === 'outline-6') {
    return (
      <div
        className="!bl-absolute bl-top-[1px] bl-left-0 bl-w-full bl-h-full bl-rounded-md bl-overflow-hidden bl-hidden sm:bl-block"
        style={{ clipPath: 'polygon(100% 0, 100% 20%, 100% 41%, 87% 100%, 0 100%, 0 0)' }}
      >
        <div
          className={cn(
            buttonOverlayVariants({
              variant: overlayVariant ? overlayVariant : variant,
              overlayFrom,
            }),
            {
              'bl-translate-y-0 bl-translate-x-0 bl-scale-150': keepOverlay,
            },
          )}
        ></div>
      </div>
    );
  }
  return (
    <div
      className={cn(
        buttonOverlayVariants({
          variant: overlayVariant ? overlayVariant : variant,
          overlayFrom,
        }),
        {
          'bl-translate-y-0 bl-translate-x-0 bl-scale-150': keepOverlay,
        },
      )}
    ></div>
  );
};

const outlineMaskVariants = cva(
  'bl-border-2 bl-border-secondary !bl-absolute bl-rotate-45 bl-duration-200',
  {
    variants: {
      variant: {
        outline: 'bl-border-secondary',
        'outline-2': 'bl-border-secondary group-hover/button:bl-border-primary',
        'outline-3': 'bl-border-input',
        'outline-4': 'bl-border-primary',
        'outline-5': 'bl-border-primary',
        outlineError: 'bl-border-button-error',
        white2: 'bl-border-black group-hover/button:bl-border-primary',
      },
    },
    defaultVariants: {
      variant: 'outline',
    },
  },
);

type OutlineMaskVariant = VariantProps<typeof outlineMaskVariants>['variant'];

const OutlineMask = ({ variant, noMask, outlineClassName, disabled, keepOverlay }: ButtonProps) => {
  if (
    ![
      'outline',
      'outline-2',
      'outline-3',
      'outline-4',
      'outline-5',
      'outline-6',
      'outlineError',
      'white2',
    ].includes(variant as string) ||
    noMask
  )
    return null;

  if (variant === 'outline-5') {
    return (
      <div
        className={cn(
          '!bl-absolute bl-top-0 bl-left-0 bl-w-full bl-h-full bl-bg-[url("/images/btcfi/bg-btn-angle-s.svg")] sm:bl-bg-[url("/images/btcfi/bg-btn-angle.svg")]',
          {
            'bl-bg-[url("/images/btcfi/bg-btn-angle-m.svg")]': keepOverlay,
            'md:bl-bg-[url("/images/btcfi/bg-btn-angle-disabled.svg")] bl-bg-cover bl-bg-[url("/images/btcfi/bg-btn-angle-disabled-m.svg")]':
              disabled,
          },
        )}
      ></div>
    );
  }
  if (variant === 'outline-6') {
    return (
      <div
        className={cn(
          '!bl-absolute bl-top-0 bl-left-0 bl-w-full bl-bg-cover bl-h-full bl-bg-[url("/images/btcfi/bg-btn-angle-min.svg")] sm:bl-bg-[url("/images/btcfi/bg-btn-angle.svg")]',
          {
            'md:bl-bg-[url("/images/btcfi/bg-btn-angle-disabled.svg")] bl-bg-cover bl-bg-[url("/images/btcfi/bg-btn-angle-disabled-min.svg")]':
              disabled,
          },
        )}
      ></div>
    );
  }
  return (
    <>
      <div
        className={cn(
          outlineMaskVariants({ variant: variant as OutlineMaskVariant }),
          'bl-h-5 bl-w-5 -bl-top-[12.2px] -bl-left-[12.2px]',
          'group-[.btn-xs]/button:-bl-top-[13.5px] group-[.btn-xs]/button:-bl-left-[13.5px]',
          'group-[.btn-mini]/button:-bl-top-[14px] group-[.btn-mini]/button:-bl-left-[14px]',
          {
            'bl-border-button-error': ['outlineError'].includes(variant as string),
          },
          outlineClassName,
        )}
      ></div>
      <div
        className={cn(
          outlineMaskVariants({ variant: variant as OutlineMaskVariant }),
          'bl-h-6 bl-w-6 -bl-bottom-[15.218px] -bl-right-[15.218px]',
          'group-[.btn-xs]/button:-bl-bottom-[16.428px] group-[.btn-xs]/button:-bl-right-[16.428px]',
          'group-[.btn-xs]/button:-bl-bottom-[17px] group-[.btn-xs]/button:-bl-right-[17px]',
          {
            'bl-border-button-error': ['outlineError'].includes(variant as string),
          },
          outlineClassName,
        )}
      ></div>
    </>
  );
};

const GhostBorder = ({ variant }: ButtonProps) => {
  if (variant !== 'ghost') return null;
  return (
    <>
      <div className={cn(ghostBorderVariants({ variant: 'topLeft' }))}></div>
      <div className={cn(ghostBorderVariants({ variant: 'topRight' }))}></div>
      <div className={cn(ghostBorderVariants({ variant: 'bottomLeft' }))}></div>
      <div className={cn(ghostBorderVariants({ variant: 'bottomRight' }))}></div>
    </>
  );
};

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      overlayFrom,
      overlayVariant,
      keepOverlay,
      outlineClassName,
      noMask = false,
      asChild = false,
      noOverlay = false,
      children,
      loading,
      ...props
    },
    ref,
  ) => {
    const Comp = asChild ? Slot : 'button';
    const { disabled } = props;
    return (
      <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props}>
        {!noOverlay && (
          <BackgroundOverlay {...{ variant, overlayVariant, overlayFrom, keepOverlay }} />
        )}
        <OutlineMask {...{ variant, outlineClassName, noMask, disabled, keepOverlay }} />
        <GhostBorder {...{ variant }} />
        {loading && <LoaderIcon className="bl-animate-spin bl-size-4.5 bl-mr-1.5" />}
        <Slottable>{children}</Slottable>
      </Comp>
    );
  },
);
Button.displayName = 'Button';

const simpleButtonVariants = cva(
  'bl-flex bl-items-center bl-justify-center bl-whitespace-nowrap bl-rounded-md bl-font-medium bl-ring-offset-background bl-transition-colors focus-visible:bl-outline-none focus-visible:bl-ring-2 focus-visible:bl-ring-ring focus-visible:bl-ring-offset-2 disabled:bl-pointer-events-none disabled:bl-opacity-50 bl-relative bl-duration-500',
  {
    variants: {
      variant: {
        secondary:
          'bl-bg-secondary bl-text-secondary-foreground hover:bl-text-white hover:bl-bg-primary',
      },
      size: {
        icon: 'bl-h-6 bl-w-6',
      },
    },
    defaultVariants: {
      variant: 'secondary',
      size: 'icon',
    },
  },
);

export interface SimpleButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof simpleButtonVariants> {
  asChild?: boolean;
}

const SimpleButton = React.forwardRef<HTMLButtonElement, SimpleButtonProps>(
  ({ className, variant, size, children, asChild, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp className={cn(simpleButtonVariants({ variant, size, className }))} ref={ref} {...props}>
        {children}
      </Comp>
    );
  },
);
SimpleButton.displayName = 'SimpleButton';

export { Button, buttonVariants, SimpleButton };
