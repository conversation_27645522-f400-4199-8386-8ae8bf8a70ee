import React, { ReactNode, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Link } from '@/components/i18n/link';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import MetaMaskIcon from '@/components/icons/MetaMaskIcon';
import { HeaderContext } from '@/hooks/header';
import { Chain } from '@/wallets/config/type';
import { useConnectors } from 'wagmi';
import { useDialog } from '@/hooks/dialog';
import { WalletDriverName } from '@/hooks/wallet/common';
import { WalletNotInstallDialog } from '@/hooks/wallet/connect';
import { useTranslation } from 'react-i18next';
import { useMediaQuery } from '@react-hook/media-query';

export type LinkRendererProps = Pick<LinkData, 'name' | 'link'> & { className?: string };
export type LinkRenderer = (data: LinkRendererProps) => ReactNode;

export interface LinkData {
  name: string;
  hint?: string;
  link?: string;
  anchor?: string;
  image?: string;
  icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  default?: string;
  children?: LinkData[];
  render?: LinkRenderer;
  coming?: boolean;
  showOrder?: boolean;
  hideOrder?: boolean;
  isBlank?: boolean;
  type?: string;
  className?: string;
}

interface LinkButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  link: LinkData;
}

const LinkContent = ({ link }: { link: LinkData }) => {
  const { t } = useTranslation();
  // if (!link.icon) {
  //   return <span>{t(link.name)}</span>;
  // }

  const Icon = link.icon;
  return (
    <div className="bl-flex bl-items-center bl-pr-4 md:bl-pr-0 bl-justify-between bl-w-full bl-text-lg/5 bl-font-normal bl-flex-1 bl-overflow-hidden">
      <div className={cn("bl-flex bl-items-center bl-gap-2 bl-whitespace-nowrap bl-w-[86%]", link.className)}>
        {link.hint ? (
          <div className="bl-space-y-0.5 bl-w-full">
            <div>{t(link.name)}</div>
            <div className="bl-text-sm/none bl-text-[#4A4A4A] bl-w-full bl-overflow-scroll md:bl-overflow-hidden">
              <div>{t(link.hint)}</div>
            </div>
          </div>
        ) : (
          t(link.name)
        )}
        {link.coming && (
          <div className="bl-opacity-0 bl-whitespace-nowrap bl-duration-200 group-hover:bl-opacity-100">
            <span className="bl-text-sm/none bl-text-[#4A4A4A] bl-w-fit">Coming soon</span>
          </div>
        )}
      </div>
      {Icon && <Icon className="bl-size-5 bl-min-w-0 bl-shrink-0" />}
    </div>
  );
};

export const LinkButton = React.forwardRef<HTMLButtonElement, LinkButtonProps>(
  ({ link, ...props }, ref) => {
    const { t } = useTranslation();
    if (link.link) {
      return (
        <Button ref={ref} overlayFrom="left" asChild {...props}>
          {link.link.startsWith('http') ? (
            <a
              className="bl-inline-flex bl-w-full bl-items-center bl-justify-between"
              href={link.link}
              target="_blank"
              rel="noreferrer"
            >
              <LinkContent link={link} />
            </a>
          ) : (
            <Link to={link.link} target={link?.isBlank ? '_blank' : '_self'} rel="noreferrer">
              <LinkContent link={link} />
            </Link>
          )}
        </Button>
      );
    }

    if (link.anchor) {
      return (
        <Button
          ref={ref}
          overlayFrom="left"
          asChild
          {...props}
          onClick={() => {
            setTimeout(() => {
              window.location.href = window.location.origin + link.anchor;
            }, 500);
          }}
        >
          <a
            className="bl-inline-flex bl-w-full bl-items-center bl-justify-between"
            href={link.anchor}
          >
            <LinkContent link={link} />
          </a>
        </Button>
      );
    }

    return (
      <Button ref={ref} overlayFrom="left" asChild onClick={(e) => e.stopPropagation()} {...props}>
        <div className="bl-inline-flex bl-w-full bl-items-center bl-justify-between bl-cursor-pointer">
          <span>{t(link.name)}</span>
          {link.coming && (
            <Text variant="default" size="md" className="bl-ml-2">
              Coming soon
            </Text>
          )}
        </div>
      </Button>
    );
  },
);
LinkButton.displayName = 'LinkButton';

type LinkComponentType = React.ComponentType<{ asChild?: boolean }>;

const AccordionContentLink = ({
  link,
  linkComponent,
}: {
  link: LinkData;
  linkComponent?: LinkComponentType;
}) => {
  const LinkComponent = linkComponent || DefaultLinkComponent;
  const className = cn(
    'bl-w-full bl-flex bl-justify-between bl-text-lg/none md:bl-text-xl/none bl-text-black bl-cursor-pointer',
    'bl-duration-200 hover:bl-text-white bl-group bl-py-4',
    'bl-text-left',
  );
  if (link.render) {
    return (
      <LinkComponent asChild>
        {link.render({ name: link.name, link: link.link, className })}
      </LinkComponent>
    );
  }
  return link.link?.startsWith('http') ? (
    <LinkComponent asChild>
      <a href={link.link} className={className} target="_blank" rel="noreferrer">
        <LinkContent link={link} />
      </a>
    </LinkComponent>
  ) : link.link ? (
    <LinkComponent asChild>
      <Link to={link.link} className={className}>
        <LinkContent link={link} />
      </Link>
    </LinkComponent>
  ) : (
    <button className={className} onClick={(e) => e.stopPropagation()}>
      <LinkContent link={link} />
    </button>
  );
};

interface HeadMenuItemProps {
  index: number;
  link: LinkData;
  isActive?: boolean;
  onMouseHover?: (index: number) => void;
  showOrder?: boolean;
  showSubOrder?: boolean;
  linkComponent?: LinkComponentType;
}

const DefaultLinkComponent = ({ children }: { asChild?: boolean; children: ReactNode }) => (
  <>{children}</>
);

const HeadMenuItem = ({
  isActive,
  index,
  link,
  onMouseHover,
  showOrder = true,
  showSubOrder = false,
  linkComponent,
}: HeadMenuItemProps) => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery('(max-width: 640px)');
  const LinkComponent = linkComponent || DefaultLinkComponent;
  const order = (index + 1).toString().padStart(2, '0');
  const isBitlayerLink = link?.children?.some((item) => item.type);
  const bitlayerLink = link?.children?.filter((item) => item.type);
  const productLink = bitlayerLink?.filter((item) => item.type === 'product');
  const innovationLink = bitlayerLink?.filter((item) => item.type === 'innovation');

  if (!link.children) {
    const className =
      'bl-w-full bl-h-10 md:bl-h-12 bl-justify-start bl-text-black bl-flex-nowrap bl-text-lg/none md:bl-text-xl/none';
    return (
      <LinkComponent asChild>
        <div
          key={index}
          className="bl-border-b bl-border-primary-divider bl-py-2.5 bl-flex bl-gap-4 bl-items-center last:bl-border-b-0"
        >
          {showOrder && !link.hideOrder && (
            <Text variant="dark" size="md" className="w-6">
              {order}
            </Text>
          )}
          {typeof link.render === 'function' ? (
            link.render({ name: link.name, link: link.link, className })
          ) : (
            <LinkButton
              className={className}
              link={link}
              onMouseOver={() => onMouseHover?.(index)}
            />
          )}
        </div>
      </LinkComponent>
    );
  }

  const enableSubAccordion = link.children?.some((child: LinkData) => child.children?.length);

  return (
    <AccordionItem
      value={link.name}
      className="bl-border-b bl-border-primary-divider last:bl-border-b-0"
    >
      <AccordionTrigger
        className="bl-w-full"
        asChild
        onClick={(e) => e.stopPropagation()}
        onMouseOver={() => onMouseHover?.(index)}
      >
        <div className="bl-py-2.5 bl-flex bl-gap-4">
          {showOrder && (
            <Text variant="dark" size="md" className="bl-w-6 bl-font-normal bl-py-2">
              {order}
            </Text>
          )}
          <Button
            className={cn('bl-w-full bl-justify-start bl-text-black bl-flex-nowrap md:bl-px-4', {
              'bl-text-white': isActive,
            })}
            overlayFrom="left"
            keepOverlay={isActive}
            size={isMobile ? 'default' : 'lg'}
          >
            <div className="bl-w-full bl-flex bl-justify-between bl-items-center bl-text-lg/none md:bl-text-xl/none bl-text-left">
              <LinkContent link={link} />
              {/* {isMobile ? <span>{t(link.name)}</span> : <LinkContent link={link} />} */}
              <ChevronDown
                className={cn('bl-w-5 bl-h-5 bl-duration-200', {
                  'bl-rotate-180': isActive,
                })}
              />
            </div>
          </Button>
        </div>
      </AccordionTrigger>
      <AccordionContent className="bl-pb-0">
        <div
          className={cn('bl-w-full bl-flex bl-flex-col bl-pl-6 md:bl-pl-12', {
            'bl-pl-12': showOrder,
          })}
        >
          {enableSubAccordion ? (
            <HeadMenu links={link.children} defaultValue={link.default} showOrder={true} />
          ) : (
            link.children.map((child, index) => {
              const order = (index + 1).toString().padStart(2, '0');
              return (
                <div key={index}>
                  {productLink?.[0]?.name === child.name && (
                    <div className="bl-h-[48px] bl-border-b bl-border-primary-divider bl-flex bl-font-bold bl-items-center bl-justify-start bl-gap-5 bl-text-lg bl-text-black">
                      <span className="bl-font-normal">01</span>
                      <span>{t('navigation.links.product')}</span>
                    </div>
                  )}
                  {innovationLink?.[0]?.name === child.name && (
                    <div className="bl-h-[48px] bl-border-b bl-border-primary-divider bl-font-bold bl-flex bl-items-center bl-justify-start bl-gap-5 bl-text-lg bl-text-black">
                      <span className="bl-font-normal">02</span>
                      <span>{t('navigation.links.innovation')}</span>
                    </div>
                  )}
                  <div
                    className={cn(
                      'bl-w-full bl-flex bl-items-center bl-gap-3 bl-border-b bl-border-primary-divider last:bl-border-b-0 md:bl-pl-2 md:bl-pr-4 bl-text-left',
                      { 'bl-pl-10': isBitlayerLink && isMobile },
                    )}
                  >
                    {showSubOrder && !child.hideOrder && (
                      <Text variant="dark" size="md" className="w-6">
                        {order}
                      </Text>
                    )}
                    <AccordionContentLink link={child} linkComponent={linkComponent} />
                  </div>
                </div>
              );
            })
          )}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

interface HeadMenuProps {
  links: LinkData[];

  value?: string;
  defaultValue?: string;
  onValueChange?: (value: string) => void;

  onMouseHoverItem?: (index: number) => void;
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
  showOrder?: boolean;
  showSubOrder?: boolean;
  linkComponent?: React.ComponentType<{ asChild?: boolean }>;
}

export const HeadMenu = ({
  links,
  value,
  defaultValue,
  onValueChange,
  onMouseHoverItem,
  onClick,
  showOrder = true,
  showSubOrder = false,
  linkComponent,
}: HeadMenuProps) => {
  const [internalValue, setInternalValue] = useState<string | undefined>(defaultValue);
  const internalOnValueChange = (value: string) => {
    setInternalValue(value);
    onValueChange?.(value);
  };
  useEffect(() => {
    if (value) setInternalValue(value);
  }, [value, setInternalValue]);

  return (
    <Accordion
      type="single"
      collapsible
      className="bl-w-full"
      value={internalValue}
      defaultValue={defaultValue}
      onValueChange={internalOnValueChange}
      onClick={onClick}
    >
      {links.map((link, index) => (
        <HeadMenuItem
          key={index}
          index={index}
          link={link}
          isActive={internalValue === link.name}
          onMouseHover={onMouseHoverItem}
          showOrder={showOrder}
          showSubOrder={showSubOrder}
          linkComponent={linkComponent}
        />
      ))}
    </Accordion>
  );
};

export const AddTestNetLink = React.forwardRef<
  HTMLButtonElement,
  {
    name: string;
    className?: string;
    chain: Chain;
  }
>(({ name, className, chain }, ref) => {
  const { t } = useTranslation();
  const connectors = useConnectors();
  const { open } = useDialog();
  const id = WalletDriverName.MetaMask;

  const addChain = async () => {
    const connector = connectors.find((item) => item.id.startsWith(id));
    if (!connector) {
      return open({
        content: ({ close }) => <WalletNotInstallDialog id={id} close={close} />,
      });
    }
    const provider = (await connector.getProvider()) as EIP1193Provider;
    provider.request({
      method: 'wallet_addEthereumChain',
      params: [
        {
          chainId: `0x${chain.id.toString(16)}`,
          chainName: chain.name,
          nativeCurrency: chain.nativeCurrency,
          rpcUrls: chain.rpcUrls.default.http,
          blockExplorerUrls: [chain.blockExplorers?.default.url],
          iconUrls: chain.custom?.iconUrls,
        },
      ],
    });
  };

  return (
    <button className={className} onClick={addChain} ref={ref}>
      <span className="bl-whitespace-nowrap">{t(name)}</span>
      <MetaMaskIcon className="bl-min-w-0 bl-shrink-0" />
    </button>
  );
});
AddTestNetLink.displayName = 'AddTestNetLink';

export const HeaderProvider = ({ children }: { children: ReactNode }) => {
  const [widget, setWidget] = useState<ReactNode | null>(null);
  const [pinned, setPinned] = useState(false);

  return (
    <HeaderContext.Provider
      value={{
        widget,
        setWidget,
        pinned,
        setPinned,
      }}
    >
      {children}
    </HeaderContext.Provider>
  );
};
