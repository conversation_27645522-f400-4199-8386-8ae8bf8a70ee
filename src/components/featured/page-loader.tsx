import React, { useCallback, useEffect, useRef, useState } from 'react';
import { preloadImage } from '@/lib/load-checker';
import { Dialog, DialogPortal } from '@/components/ui/dialog';
import { Text } from '@/components/ui/text';
import { ClientOnly } from 'remix-utils/client-only';
import { Title } from '@/components/ui/title';
import { atom } from 'jotai';
import { useLocaleFonts } from '../i18n/styles';
import { useCountUp } from 'react-countup';
import useChange from '@react-hook/change';
import { staticAsset } from '@/lib/static';
import { cn } from '@/lib/utils';
import { useAnimate } from 'framer-motion';

export const LoadingState = atom<boolean>(false);

const blockGroups = [
  3, 2, 0, 1, 4, 4, 1, 3, 0,
  //
  2, 1, 0, 4, 2, 0, 2, 3, 3,
  //
  1, 2, 1, 0, 1, 3, 2, 1, 4,
  //
  0, 2, 2, 1, 3, 3, 0, 2, 1,
  //
  4, 1, 0, 3, 2, 1, 2, 0, 3,
];

const logoFadeInOrder = [1, 5, 6, 2, 4, 3, 7];
const logoFadeOutOrder = [3, 5, 2, 4, 6, 1, 7];

const ease = 'linear';
const logoDuration = 0.05;
const textDuration = 0.2;
const bgDuration = 0.2;
const bgGap = 0.06;

interface PageLoadingProps {
  open: boolean;
  progress: number;
  onLogoFadeIn?: () => void;
  onAnimated?: () => void;
}

function PageLoading({ open, progress, onAnimated, onLogoFadeIn }: PageLoadingProps) {
  const formatted = Math.floor(progress * 100);
  const counter = useRef<HTMLSpanElement>(null);
  const { start, update } = useCountUp({
    ref: counter,
    start: 0,
    end: formatted || 0,
    delay: 0,
    duration: 0.5,
  });

  useChange(formatted, (current, prev) => {
    if (current === undefined) return;
    if (prev === undefined) {
      start();
    } else {
      update(current);
    }
  });

  const [scope, animate] = useAnimate();
  const logoFadeIn = useCallback(async () => {
    const opacity = 1;
    for (const i of logoFadeInOrder) {
      await animate(`.logo-${i}`, { opacity }, { duration: logoDuration, ease });
    }
    await animate('.group-progress', { opacity }, { duration: textDuration, ease });
    onLogoFadeIn?.();
    // eslint-disable-next-line
  }, [animate]);

  useChange(progress, async (current) => {
    if (current !== 1) {
      animate('.group-block', { opacity: 1 });
      return;
    }

    const opacity = 0;

    // fade out the progress text
    await delay(500);
    await animate('.group-progress', { opacity }, { duration: textDuration, ease });

    // fade out the logo
    for (const i of logoFadeOutOrder) {
      await animate(`.logo-${i}`, { opacity }, { duration: logoDuration, ease });
    }

    // fade out the background
    for (let i = 0; i < 4; i++) {
      animate(`.group-${i}`, { opacity }, { duration: bgDuration, ease, delay: i * bgGap });
    }
    await animate('.group-4', { opacity }, { duration: bgDuration, ease, delay: 4 * bgGap });
    onAnimated?.();
  });

  useEffect(() => {
    logoFadeIn();
  }, [logoFadeIn]);

  return (
    <Dialog open={open} modal={true}>
      <DialogPortal>
        <div
          className="bl-fixed bl-z-50 bl-w-screen bl-h-screen bl-top-0 bl-left-0 bl-flex bl-flex-col bl-items-center bl-justify-center"
          ref={scope}
        >
          <div className="bl-absolute bl-top-0 bl-left-0 bl-size-full bl-grid bl-grid-cols-5 bl-grid-rows-9 md:bl-grid-cols-9 md:bl-grid-rows-5">
            {Array.from({ length: 45 }).map((_, i) => (
              <div
                className={cn('bl-bg-primary', `group-block group-${blockGroups[i]}`)}
                key={i}
              ></div>
            ))}
          </div>
          <div className="bl-relative bl-w-[50vw] bl-aspect-[21/19] md:bl-w-[370px] bl-overflow-hidden">
            <svg
              width="42"
              height="38"
              viewBox="0 0 42 38"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="bl-size-full bl-text-black wave-skew"
            >
              <g id="logo w/o logotype">
                <path
                  className="logo-1"
                  d="M0 0.898446V8.52049C1.73006 7.60863 3.4469 7.71949 5.15052 7.53354V0C3.28121 0 1.73006 0.0160916 0 0.898446Z"
                  fill="currentColor"
                  opacity="0"
                />
                <path
                  className="logo-2"
                  d="M15.0986 1.74141C13.4725 1.15764 11.8333 0.646287 10.1816 0.319092V7.76502C11.8333 8.0645 13.4717 8.54725 15.0986 9.10241V1.74141Z"
                  fill="currentColor"
                  opacity="0"
                />
                <path
                  className="logo-3"
                  d="M10.1819 15.211V7.76506C8.51797 7.46379 6.84079 7.34847 5.15039 7.53352V15.0671C6.84079 14.8525 8.51797 14.9392 10.1819 15.2119V15.211Z"
                  fill="currentColor"
                  opacity="0"
                />
                <path
                  className="logo-4"
                  d="M0 16.1425V23.7646C1.73006 22.7928 3.4469 22.8447 5.15052 22.5997V15.0662C3.38609 15.211 1.73006 15.2003 0 16.1425Z"
                  fill="currentColor"
                  opacity="0"
                />
                <path
                  className="logo-5"
                  d="M10.1816 15.2109V22.6569C11.8333 22.8991 13.4717 23.3256 15.0986 23.8244V16.4634C13.4725 15.936 11.8333 15.4809 10.1816 15.2109Z"
                  fill="currentColor"
                  opacity="0"
                />
                <path
                  className="logo-6"
                  d="M5.15039 22.5998V30.1333C6.84079 29.8607 8.51797 29.8893 10.1819 30.1038V22.6579C8.51797 22.4138 6.84079 22.3566 5.15039 22.5998Z"
                  fill="currentColor"
                  opacity="0"
                />
                <path
                  className="logo-7"
                  d="M37.9359 19.3617C39.851 17.7678 40.9289 15.2065 40.9289 12.5711C40.9289 8.4874 38.4127 6.81567 33.1767 6.29716V2.73914C32.0627 2.68192 30.9434 2.55855 29.817 2.38512V5.93868C28.7656 5.78581 27.7097 5.58824 26.6477 5.35849V1.77722C25.4976 1.51796 24.3422 1.22027 23.1788 0.898438C23.1788 2.10173 23.1788 3.30592 23.1788 4.50921C22.093 4.21777 21.0028 3.90577 19.9055 3.58484V10.8627C18.3156 10.2763 16.7142 9.65315 15.0996 9.10246V16.4635C16.7133 16.9873 18.3156 17.5827 19.9055 18.1415V25.4193C18.3156 24.8883 16.7142 24.3206 15.0996 23.8245V31.1855C16.7133 31.653 18.3156 32.193 19.9055 32.6972C21.0019 32.9421 22.093 33.179 23.1788 33.3954C23.1788 34.5987 23.1788 35.8029 23.1788 37.0062C24.3413 37.2279 25.4976 37.4254 26.6477 37.5854V34.0042C27.7097 34.1606 28.7665 34.2849 29.817 34.3653V37.9189C30.9434 37.9949 32.0627 38.0208 33.1767 37.9824V34.4521C38.9829 34.1347 42.0006 29.5584 42.0006 24.9947C42.0006 21.6262 40.4538 19.9929 37.9359 19.3608V19.3617ZM26.6574 11.2122C27.7159 11.4268 28.7691 11.6083 29.8161 11.7459C30.2841 11.8076 30.7512 11.8604 31.2166 11.9033C31.3056 11.9113 31.3928 11.9212 31.4783 11.9319C31.4995 11.9346 31.5206 11.9373 31.5418 11.9399C31.6211 11.9498 31.6995 11.9605 31.7753 11.973C31.7806 11.973 31.7859 11.9748 31.7912 11.9757C31.8749 11.9891 31.956 12.0043 32.0353 12.0195C32.0494 12.0222 32.0635 12.0249 32.0768 12.0284C32.1526 12.0436 32.2266 12.0597 32.2989 12.0776C32.3015 12.0776 32.3041 12.0785 32.3068 12.0794C32.3861 12.0991 32.4628 12.1196 32.5368 12.142C32.5412 12.1438 32.5465 12.1447 32.5518 12.1465C32.7792 12.2153 32.9872 12.2957 33.1749 12.3914C34.0562 12.8357 34.5048 13.5974 34.5048 14.7211C34.5048 15.8663 34.0562 16.6807 33.1749 17.0937C32.9872 17.1813 32.7783 17.2502 32.5509 17.2993C32.5465 17.3002 32.5412 17.3011 32.5368 17.3029C32.4619 17.319 32.3852 17.3324 32.3059 17.344C32.3033 17.344 32.3006 17.344 32.298 17.3449C32.2257 17.3557 32.1517 17.3637 32.0759 17.37C32.0618 17.3708 32.0486 17.3726 32.0345 17.3735C31.9551 17.3798 31.8741 17.3843 31.7903 17.386C31.785 17.386 31.7797 17.386 31.7745 17.386C31.6987 17.3878 31.6202 17.3869 31.5409 17.386C31.5198 17.386 31.4986 17.3852 31.4766 17.3843C31.3911 17.3816 31.3047 17.3771 31.2148 17.37C30.7486 17.3333 30.2824 17.2868 29.8144 17.2314C28.7665 17.108 27.7142 16.94 26.6557 16.7397V11.2149L26.6574 11.2122ZM33.1758 28.6314C33.1079 28.6421 33.0383 28.6519 32.9678 28.66C32.9475 28.6627 32.9264 28.6645 32.9061 28.6662C32.8541 28.6716 32.8021 28.6761 32.7492 28.6805C32.7245 28.6823 32.6999 28.6841 32.6752 28.685C32.6223 28.6886 32.5685 28.6904 32.5139 28.6922C32.491 28.6922 32.4681 28.694 32.4443 28.694C32.3667 28.6957 32.2892 28.6957 32.209 28.694C31.414 28.6779 30.6164 28.6305 29.8161 28.5581C28.7682 28.4633 27.7159 28.3247 26.6574 28.154V22.3431C27.7159 22.5291 28.7691 22.682 29.8161 22.7919C30.6173 22.876 31.4149 22.9341 32.209 22.9609C32.2892 22.9636 32.3676 22.9671 32.4443 22.9716C32.4681 22.9734 32.491 22.9743 32.5139 22.9761C32.5677 22.9797 32.6223 22.9832 32.6743 22.9877C32.699 22.9895 32.7237 22.9922 32.7483 22.994C32.8012 22.9984 32.8532 23.0047 32.9052 23.0101C32.9255 23.0127 32.9466 23.0145 32.9669 23.0172C33.0374 23.0261 33.107 23.0351 33.1749 23.0458C34.7763 23.2872 35.6118 24.0864 35.6118 25.7161C35.6118 27.3324 34.7763 28.3614 33.1749 28.6305L33.1758 28.6314Z"
                  fill="currentColor"
                  opacity="0"
                />
              </g>
            </svg>
          </div>
          <Text
            size="none"
            className="bl-relative bl-z-10 bl-text-3xl/none bl-text-black md:bl-text-[50px]/none bl-mt-12 md:bl-mt-24 group-progress"
            style={{ opacity: 0 }}
          >
            <span ref={counter} />%
          </Text>
          <Title size="none" className="bl-text-transparent bl-absolute bl-top-0 bl-left-0">
            Bitlayer
          </Title>
        </div>
      </DialogPortal>
    </Dialog>
  );
}

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const useResourceLoaded = ({ images = [] }: { images?: string[]; fonts?: string[] }) => {
  const [progress, setProgress] = React.useState(0);

  const check = React.useCallback(() => {
    const total = images.length;
    let loadedItems = 0;

    const onLoad = async () => {
      loadedItems++;
      if (loadedItems === total) {
        await delay(200);
        setProgress(1);
        return;
      }
      setProgress(loadedItems / total);
    };

    images.forEach((src) => {
      preloadImage(src).finally(onLoad);
    });

    // fonts.forEach(() => {
    //   document.fonts.ready.finally(onLoad); // trick to wait for font loaded
    //   // waitForFontLoaded(font).finally(onLoad);
    // });
  }, [images]);

  return { start: check, progress };
};

interface PageLoaderProps {
  onLoad?: () => void;
  images?: Array<string>;
}

export const PageLoader: React.FC<PageLoaderProps> = ({
  onLoad,
  images = [
    '/images/home/<USER>',
    '/images/home/<USER>',
    '/images/home/<USER>',
    '/images/home/<USER>',
    '/images/home/<USER>',
    '/images/home/<USER>',
  ],
}) => {
  const localeFonts = useLocaleFonts();
  const [visible, setVisible] = useState(true);

  const { start, progress } = useResourceLoaded({
    fonts: localeFonts.sans,
    images: images.map(staticAsset),
  });

  const handleAnimated = () => {
    setVisible(false);
  };

  const handleLogoFadeIn = () => {
    start();
  };

  useEffect(() => {
    onLoad?.();
  });

  return (
    <ClientOnly>
      {() => (
        <PageLoading
          open={visible}
          progress={progress}
          onAnimated={handleAnimated}
          onLogoFadeIn={handleLogoFadeIn}
        />
      )}
    </ClientOnly>
  );
};
