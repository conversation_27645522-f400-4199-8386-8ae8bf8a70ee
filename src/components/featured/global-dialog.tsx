import { DialogContent<PERSON><PERSON><PERSON>, GlobalDialogContext, OpenDialogOptions } from '@/hooks/dialog';
import { ReactNode, useCallback, useRef, useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { CornerMarkGroup } from '@/components/ui/corner-mark';
import BitlayerLogo from '@/components/icons/BitlayerLogo';
import { Text } from '@/components/ui/text';
import CloseIcon from '../icons/CloseIcon';

interface GlobalDialogProviderProps {
  children?: React.ReactNode;
}

export function GlobalDialogProvider({ children }: GlobalDialogProviderProps) {
  const [open, setOpen] = useState(false);
  const [showCloseButton, setShowCloseButton] = useState(true);
  const [outsideClickClose, setOutsideClickClose] = useState(true);
  const [, setCounter] = useState(0); // trick to force rerender the content
  const content = useRef<ReactNode | DialogContentRenderer>(undefined);

  const openDialog = useCallback(
    (options: OpenDialogOptions) => {
      content.current = options.content;
      setCounter((state) => state + 1);
      setShowCloseButton(options.showCloseButton ?? true);
      setOutsideClickClose(options.outsideClickClose ?? true);
      setOpen(true);
    },
    [content, setOpen, setCounter],
  );

  const closeDialog = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  return (
    <GlobalDialogContext.Provider
      value={{
        open: openDialog,
        close: closeDialog,
      }}
    >
      {children}

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent
          onPointerDownOutside={outsideClickClose ? undefined : (e) => e.preventDefault()}
        >
          <div className="bl-relative bl-z-20 bl-flex bl-flex-col bl-gap-6 bl-items-center bl-p-6 bl-bg-primary bl-text-black bl-font-body bl-overflow-hidden">
            <BitlayerLogo className="bl-text-black" />

            <div>
              {typeof content.current === 'function'
                ? content.current({ close: closeDialog })
                : content.current}
            </div>

            {showCloseButton && (
              <Button
                className="bl-size-7.5 bl-rounded-full bl-border bl-border-black bl-text-black bl-text-base/none hover:bl-text-primary bl-translate-y-2 bl-px-0 bl-py-0"
                style={{ WebkitMask: 'none' }}
                onClick={closeDialog}
              >
                <CloseIcon className="bl-size-3" />
              </Button>
            )}
            <CornerMarkGroup variant="dark" />
          </div>
        </DialogContent>
      </Dialog>
    </GlobalDialogContext.Provider>
  );
}

export const CommonTextDialog = ({
  title,
  description,
}: {
  title: ReactNode;
  description: ReactNode;
}) => {
  return (
    <div className="bl-w-72 bl-space-y-6">
      <Text variant="dark" size="none" className="bl-text-2xl/9 bl-text-center">
        {title}
      </Text>
      <Text variant="secondary" size="xs" className="bl-text-center bl-text-primary-foreground">
        {description}
      </Text>
    </div>
  );
};
