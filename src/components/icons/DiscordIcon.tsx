import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={165}
    height={127}
    viewBox="0 0 165 127"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M139.594 11.23h-.001l.009.002c.048.02.087.056.11.1l.005.011.007.01c18.609 27.371 27.797 58.24 24.362 93.781a.316.316 0 0 1-.133.233c-14.093 10.349-27.743 16.632-41.204 20.797a.319.319 0 0 1-.352-.119l-.001-.001a109.814 109.814 0 0 1-8.406-13.678.315.315 0 0 1 .059-.374.315.315 0 0 1 .111-.073 84.85 84.85 0 0 0 12.908-6.143l.003-.002a.727.727 0 0 0 .355-.591.737.737 0 0 0-.292-.626l-.001-.001a66.209 66.209 0 0 1-2.556-2.004.712.712 0 0 0-.756-.092c-26.63 12.303-55.8 12.303-82.75-.001l-.002-.001a.708.708 0 0 0-.748.102 69.926 69.926 0 0 1-2.548 1.997l-.001.001a.731.731 0 0 0-.182.969.74.74 0 0 0 .254.249l.002.001c4.119 2.362 8.401 4.442 12.9 6.148l.003.001a.32.32 0 0 1 .173.442 97.642 97.642 0 0 1-8.41 13.68.33.33 0 0 1-.354.116C28.76 122 15.112 115.718 1.02 105.37a.363.363 0 0 1-.135-.243c-2.87-30.742 2.98-61.865 24.343-93.78l.004-.007a.275.275 0 0 1 .126-.11l.005-.002A135.482 135.482 0 0 1 58.898.828a.323.323 0 0 1 .328.155c1.454 2.575 3.115 5.873 4.235 8.567l.062.148.159-.024c12.4-1.896 24.998-1.896 37.66 0l.158.023.061-.146c1.123-2.64 2.726-5.998 4.173-8.57l.001-.001a.302.302 0 0 1 .321-.152h.004a135.868 135.868 0 0 1 33.534 10.401Zm-98.886 91.376a.507.507 0 0 1 .277.04l10.12 9.815a.525.525 0 0 0-.286-.727c-4.488-1.702-8.76-3.778-12.871-6.134a.533.533 0 0 1-.052-.879 69.099 69.099 0 0 0 2.556-2.003.517.517 0 0 1 .256-.112Zm-.604-32.834c0 9.257 6.77 16.835 15.034 16.835l14.828-16.835.205.003v-.001c.131-9.194-6.575-16.838-15.033-16.838-8.396 0-15.034 7.58-15.034 16.836Zm54.826 0c0 9.257 6.77 16.835 15.034 16.835l14.829-16.835.205.003v-.002c.13-9.194-6.576-16.837-15.034-16.837-8.395 0-15.034 7.58-15.034 16.836Z"
      fill="currentColor"
      fillOpacity={props.opacity || 0.5}
    />
    <path
      d="M127.358 104.834a.723.723 0 0 1 .068.349.727.727 0 0 1-.355.591l-.003.002a84.85 84.85 0 0 1-12.908 6.143.32.32 0 0 0-.17.447 109.814 109.814 0 0 0 8.406 13.678l.001.001a.319.319 0 0 0 .352.119c13.461-4.165 27.111-10.448 41.204-20.797a.316.316 0 0 0 .133-.233c3.435-35.54-5.753-66.41-24.362-93.78l-.007-.01-.005-.012a.214.214 0 0 0-.11-.1l-.009-.003h.001A135.868 135.868 0 0 0 106.06.828h-.004a.302.302 0 0 0-.185.023m21.487 103.983-.186.086m.186-.086a.737.737 0 0 0-.224-.277l-.001-.001a66.209 66.209 0 0 1-2.556-2.004.712.712 0 0 0-.756-.092c-26.63 12.303-55.8 12.303-82.75-.001l-.002-.001a.708.708 0 0 0-.748.102 69.926 69.926 0 0 1-2.548 1.997l-.001.001a.731.731 0 0 0-.182.969.74.74 0 0 0 .254.249l.002.001c4.119 2.362 8.401 4.442 12.9 6.148l.003.001a.32.32 0 0 1 .173.442 97.642 97.642 0 0 1-8.41 13.68.33.33 0 0 1-.354.116C28.76 122 15.112 115.718 1.02 105.37a.363.363 0 0 1-.135-.243c-2.87-30.742 2.98-61.865 24.343-93.78l.004-.007a.275.275 0 0 1 .126-.11l.005-.002A135.482 135.482 0 0 1 58.898.828a.323.323 0 0 1 .328.155c1.454 2.575 3.115 5.873 4.235 8.567l.062.148.159-.024c12.4-1.896 24.998-1.896 37.66 0l.158.023.061-.146c1.123-2.64 2.726-5.998 4.173-8.57l.001-.001a.298.298 0 0 1 .136-.129m0 0-.088-.185m-65.075 101.94a.507.507 0 0 1 .277.04l10.12 9.815a.525.525 0 0 0-.286-.727c-4.488-1.702-8.76-3.778-12.871-6.134a.533.533 0 0 1-.052-.879 69.099 69.099 0 0 0 2.556-2.003.517.517 0 0 1 .256-.112Zm-.604-32.834c0 9.257 6.77 16.835 15.034 16.835l14.828-16.835.205.003v-.001c.131-9.194-6.575-16.838-15.033-16.838-8.396 0-15.034 7.58-15.034 16.836Zm54.826 0c0 9.257 6.77 16.835 15.034 16.835l14.829-16.835.205.003v-.002c.13-9.194-6.576-16.837-15.034-16.837-8.395 0-15.034 7.58-15.034 16.836Z"
      stroke="currentColor"
      strokeWidth={props.opacity || 0.5}
    />
  </svg>
);

export default SvgComponent;
