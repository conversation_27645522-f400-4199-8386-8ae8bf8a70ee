import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M0 1.25088V4.16794C0.659208 3.8188 1.31336 3.86132 1.96245 3.79014V0.907227C1.25044 0.907227 0.659208 0.913346 0 1.25088Z"
      fill="black"
    />
    <path
      d="M5.75251 1.57263C5.13282 1.34911 4.50839 1.15361 3.87891 1.02832V3.87806C4.50808 3.99272 5.13282 4.17727 5.75251 4.38984V1.57263Z"
      fill="black"
    />
    <path
      d="M3.87817 6.72833V3.87859C3.24394 3.76329 2.60497 3.71916 1.96094 3.79002V6.67293C2.60497 6.59112 3.24394 6.62397 3.87817 6.72833Z"
      fill="black"
    />
    <path
      d="M0 7.08479V10.0018C0.659208 9.62984 1.31336 9.64981 1.96245 9.55576V6.67285C1.28996 6.72825 0.659208 6.72406 0 7.08479Z"
      fill="black"
    />
    <path
      d="M3.87891 6.72852V9.57825C4.50808 9.67101 5.13282 9.83398 5.75251 10.0253V7.20809C5.13282 7.00615 4.50839 6.83222 3.87891 6.72852Z"
      fill="black"
    />
    <path
      d="M1.96094 9.55621V12.4391C2.60497 12.3348 3.24394 12.3457 3.87817 12.4278V9.57811C3.24394 9.4847 2.60497 9.4628 1.96094 9.55588V9.55621Z"
      fill="black"
    />
    <path
      d="M14.4515 8.3167C15.1816 7.70669 15.592 6.72629 15.592 5.71819C15.592 4.15547 14.6333 3.5155 12.638 3.31742V1.95536C12.2137 1.93346 11.7869 1.88643 11.3579 1.82009V3.1799C10.9573 3.12128 10.5548 3.04591 10.1501 2.95766V1.58723C9.71189 1.48803 9.27147 1.37401 8.82852 1.25098C8.82852 1.71155 8.82852 2.17212 8.82852 2.63269C8.41497 2.52125 7.99921 2.40176 7.58124 2.27905V5.06437C6.97546 4.83988 6.36494 4.60155 5.75 4.39058V7.20779C6.36494 7.40813 6.97546 7.63583 7.58124 7.84969V10.635C6.97546 10.4318 6.36494 10.2144 5.75 10.0247V12.8419C6.36494 13.021 6.97546 13.2274 7.58124 13.4203C7.99921 13.5141 8.41497 13.6049 8.82852 13.6873C8.82852 14.1479 8.82852 14.6085 8.82852 15.0691C9.27147 15.1538 9.7122 15.2294 10.1501 15.2906V13.9202C10.5548 13.9801 10.9573 14.0278 11.3579 14.0584V15.4182C11.7869 15.4475 12.2137 15.4572 12.638 15.4423V14.0915C14.8505 13.9701 16.0004 12.219 16.0004 10.472C16.0004 9.18309 15.4111 8.55762 14.4515 8.31606V8.3167ZM10.1539 5.19771C10.5573 5.27984 10.9585 5.34909 11.3575 5.40191C11.5359 5.42542 11.7139 5.44571 11.8912 5.46213C11.9254 5.46536 11.9583 5.4689 11.9908 5.47309C11.999 5.47405 12.0069 5.47502 12.0152 5.47598C12.0455 5.47985 12.0752 5.48404 12.1043 5.48855C12.1062 5.48855 12.1084 5.48919 12.1103 5.48951C12.1423 5.49467 12.1729 5.50014 12.2033 5.50626C12.2087 5.50723 12.2137 5.50851 12.2191 5.50948C12.2479 5.51528 12.2763 5.52172 12.3038 5.52848C12.3048 5.52848 12.306 5.5288 12.307 5.52913C12.337 5.53653 12.3664 5.54459 12.3949 5.55296C12.3968 5.55361 12.3987 5.55425 12.4006 5.55457C12.4872 5.58066 12.5666 5.6119 12.638 5.6483C12.9741 5.81835 13.1448 6.10983 13.1448 6.5398C13.1448 6.97783 12.9741 7.2896 12.638 7.44774C12.5662 7.48124 12.4869 7.50765 12.4003 7.52633C12.3984 7.52665 12.3968 7.52729 12.3949 7.52762C12.3664 7.53374 12.337 7.53889 12.307 7.5434C12.306 7.5434 12.3048 7.5434 12.3038 7.54372C12.2763 7.54758 12.2479 7.55081 12.2191 7.55338C12.2137 7.5537 12.2087 7.55435 12.2033 7.55467C12.1729 7.55692 12.1423 7.55886 12.1103 7.5595C12.1084 7.5595 12.1062 7.5595 12.1043 7.5595C12.0752 7.56015 12.0455 7.55982 12.0155 7.5595C12.0073 7.5595 11.9994 7.55918 11.9911 7.55886C11.9586 7.55757 11.9254 7.55596 11.8915 7.55338C11.7142 7.53921 11.5362 7.5215 11.3579 7.50056C10.9585 7.45322 10.5573 7.38912 10.1542 7.31247V5.19835L10.1539 5.19771ZM12.6377 11.8641C12.6118 11.8682 12.5855 11.8718 12.5587 11.875C12.5508 11.876 12.5428 11.8766 12.5349 11.8776C12.5153 11.8795 12.4954 11.8815 12.4752 11.8831C12.4657 11.8837 12.4565 11.8844 12.447 11.885C12.4268 11.8863 12.4063 11.8869 12.3857 11.8876C12.3769 11.8876 12.368 11.8882 12.3592 11.8882C12.3297 11.8889 12.3 11.8889 12.2694 11.8882C11.9665 11.8821 11.6626 11.8641 11.3575 11.8364C10.9582 11.8 10.557 11.7471 10.1539 11.6814V9.4575C10.5573 9.52868 10.9585 9.5873 11.3575 9.62917C11.6626 9.66105 11.9665 9.68328 12.2694 9.69391C12.2997 9.69487 12.3297 9.69648 12.3592 9.69809C12.368 9.69874 12.3769 9.69938 12.3857 9.6997C12.4063 9.70099 12.4268 9.7026 12.447 9.70421C12.4565 9.70486 12.466 9.70582 12.4755 9.70679C12.4957 9.70872 12.5157 9.71065 12.5353 9.71291C12.5432 9.71388 12.5511 9.71452 12.559 9.71549C12.5858 9.71871 12.6124 9.72225 12.638 9.72644C13.2482 9.81855 13.5666 10.1245 13.5666 10.7484C13.5666 11.3668 13.2482 11.7607 12.638 11.8634L12.6377 11.8641Z"
      fill="black"
    />
  </svg>
);

export default SvgComponent;
