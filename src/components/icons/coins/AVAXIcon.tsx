import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={559}
    height={558}
    viewBox="0 0 559 558"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={279.601} cy={279.114} r={212.777} fill="#fff" />
    <path
      d="M279.603.467c153.893 0 278.649 124.755 278.649 278.649s-124.756 278.65-278.649 278.65C125.709 557.766.953 433.01.953 279.116.953 125.222 125.71.466 279.603.466ZM386.927 277.94c-5.005-8.669-17.518-8.669-22.523 0l-53.439 92.56c-5.005 8.669 1.251 19.505 11.262 19.505h106.877c10.011 0 16.267-10.836 11.262-19.505l-53.439-92.56Zm-96.059-166.385c-5.005-8.669-17.517-8.669-22.522 0L118.844 370.498c-5.005 8.669 1.251 19.506 11.261 19.506h89.292a27.863 27.863 0 0 0 24.132-13.933l91.986-159.322a27.868 27.868 0 0 0 0-27.865l-44.647-77.329Z"
      fill="#FF394A"
    />
  </svg>
);

export default SvgComponent;
