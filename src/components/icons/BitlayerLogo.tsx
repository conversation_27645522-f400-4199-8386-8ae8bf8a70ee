import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={116}
    height={104}
    viewBox="0 0 116 104"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g fill="currentColor">
      <path d="M0 2.446V23.2c4.778-2.483 9.52-2.181 14.225-2.687V0C9.062 0 4.778.044 0 2.446ZM41.7 4.742C37.207 3.152 32.68 1.76 28.12.869v20.274c4.56.816 9.086 2.13 13.58 3.642V4.742Z" />
      <path d="M28.122 41.417V21.143c-4.596-.82-9.228-1.134-13.896-.63v20.512c4.668-.584 9.3-.348 13.896.395v-.003ZM0 43.954v20.753c4.778-2.646 9.52-2.504 14.225-3.171V41.023c-4.873.394-9.447.365-14.225 2.93ZM28.122 41.417v20.274c4.562.66 9.087 1.821 13.58 3.18V44.827c-4.49-1.437-9.018-2.676-13.58-3.41ZM14.226 61.536v20.512c4.668-.742 9.3-.664 13.896-.08V61.694c-4.596-.665-9.228-.82-13.896-.158ZM104.774 52.72c5.289-4.341 8.266-11.315 8.266-18.49 0-11.12-6.949-15.672-21.41-17.084V7.458c-3.078-.155-6.169-.491-9.28-.964v9.676c-2.904-.416-5.82-.954-8.753-1.58V4.84a207.772 207.772 0 0 1-9.58-2.394v9.832a425.796 425.796 0 0 1-9.041-2.517v19.817c-4.392-1.597-8.815-3.294-13.274-4.793v20.043c4.457 1.426 8.882 3.047 13.274 4.569v19.816c-4.392-1.446-8.815-2.992-13.274-4.343v20.043c4.457 1.273 8.882 2.744 13.274 4.117 3.028.666 6.041 1.311 9.04 1.9v9.832a194.96 194.96 0 0 0 9.581 1.577v-9.75c2.933.425 5.852.764 8.753.983v9.675a92.28 92.28 0 0 0 9.28.173v-9.612C107.664 92.944 116 80.483 116 68.057c0-9.172-4.272-13.62-11.226-15.34v.002Zm-31.15-22.19c2.923.583 5.832 1.078 8.724 1.453 1.292.167 2.582.311 3.868.428.246.022.487.049.723.078l.175.022c.219.026.436.056.645.09.014 0 .03.005.044.007.231.037.455.078.674.12.039.007.078.014.114.024.21.041.414.085.614.133.007 0 .014.003.022.005.219.054.43.11.635.17.012.006.027.008.041.013a10.56 10.56 0 0 1 1.721.667c2.434 1.21 3.674 3.284 3.674 6.343 0 3.119-1.24 5.336-3.674 6.46a8.222 8.222 0 0 1-1.723.56c-.012.003-.027.005-.039.01a9.962 9.962 0 0 1-1.273.183c-.039.002-.076.007-.114.01-.22.017-.443.029-.675.034h-.043c-.21.004-.427.002-.646 0-.058 0-.116-.003-.177-.005-.236-.008-.475-.02-.723-.04a99.93 99.93 0 0 1-3.868-.377c-2.894-.335-5.8-.793-8.724-1.338V30.537l.005-.008Zm18.003 47.429c-.188.03-.38.056-.575.078-.056.007-.114.012-.17.017-.144.014-.287.027-.433.039a12.975 12.975 0 0 1-.65.032c-.064 0-.127.004-.193.004-.214.005-.428.005-.65 0a95.89 95.89 0 0 1-6.608-.37c-2.894-.258-5.8-.635-8.724-1.1V60.837c2.923.506 5.832.923 8.724 1.222 2.212.229 4.415.387 6.609.46.221.007.438.017.65.03l.192.011c.***********.443.032l.204.017c.*************.433.***************.*************.024.388.048.575.077 4.423.657 6.73 2.834 6.73 7.271 0 4.401-2.307 7.203-6.73 7.936l.003.002Z" />
    </g>
  </svg>
);

export const BitlayerLogoAndText = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={153}
    height={44}
    viewBox="0 0 153 44"
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)" fill="#E36E1B">
      <path d="M0 1.04v8.825c1.997-1.056 3.98-.927 5.946-1.143V0C3.79 0 1.997.019 0 1.04ZM17.433 2.016C15.555 1.34 13.663.748 11.756.37v8.622c1.906.347 3.8.906 5.677 1.549V2.016ZM11.756 17.613V8.99c-1.922-.348-3.858-.482-5.81-.268v8.723c1.952-.248 3.888-.148 5.81.168ZM0 18.691v8.826c1.997-1.126 3.98-1.065 5.946-1.35v-8.722C3.91 17.612 1.997 17.6 0 18.69ZM11.756 17.612v8.623c1.906.28 3.8.773 5.677 1.352v-8.524c-1.878-.61-3.77-1.137-5.677-1.45ZM5.947 26.167v8.723c1.951-.316 3.887-.283 5.809-.035v-8.621c-1.922-.283-3.858-.35-5.81-.068v.001ZM43.799 22.419c2.212-1.846 3.455-4.812 3.455-7.862 0-4.729-2.904-6.665-8.95-7.264V3.172a38.378 38.378 0 0 1-3.879-.41v4.115a53.724 53.724 0 0 1-3.66-.673V2.058a85.731 85.731 0 0 1-4.004-1.017v4.18a176.46 176.46 0 0 1-3.779-1.07v8.427c-1.835-.679-3.685-1.4-5.548-2.038v8.524c1.863.606 3.713 1.295 5.548 1.942v8.427c-1.835-.615-3.685-1.273-5.548-1.847v8.524c1.863.542 3.713 1.166 5.548 1.75 1.267.284 2.526.558 3.78.808v4.18a81.3 81.3 0 0 0 4.004.67v-4.146c1.226.182 2.446.326 3.66.418v4.114c1.3.09 2.593.118 3.878.074V39.89c6.704-.368 10.188-5.666 10.188-10.951 0-3.9-1.786-5.793-4.693-6.523v.002Zm-13.022-9.437c1.223.248 2.438.458 3.647.618.54.07 1.08.132 1.617.182.104.01.203.02.302.033l.074.009c.092.011.182.024.27.038l.018.003c.097.015.19.032.282.05l.048.01a6.48 6.48 0 0 1 .532.132l.018.005c.262.079.503.173.72.283 1.017.515 1.535 1.396 1.535 2.697 0 1.326-.518 2.269-1.536 2.747a3.396 3.396 0 0 1-.72.238l-.017.004a4.303 4.303 0 0 1-.266.048h-.01c-.083.013-.17.022-.256.03l-.048.004a5.15 5.15 0 0 1-.282.015h-.018c-.088.002-.178 0-.27 0l-.073-.002a8.27 8.27 0 0 1-.302-.017 43.978 43.978 0 0 1-1.617-.16 52.966 52.966 0 0 1-3.647-.569v-6.398Zm7.526 20.17a5.984 5.984 0 0 1-.24.032l-.071.008-.181.017-.086.006c-.06.004-.123.005-.185.007l-.08.002c-.09.002-.18.002-.273 0a39.39 39.39 0 0 1-2.763-.156c-1.21-.11-2.425-.27-3.647-.47V25.87a52.65 52.65 0 0 0 3.647.52c.925.096 1.845.163 2.763.195a11.612 11.612 0 0 1 .806.058l.072.008c.081.01.162.02.24.033 1.848.278 2.813 1.204 2.813 3.092 0 1.87-.965 3.063-2.814 3.373v.002ZM59.715 14.715h6.972c.691 0 1.344.137 1.958.409a5.048 5.048 0 0 1 1.594 1.106c.449.466.807 1.011 1.075 1.636a5 5 0 0 1 .402 1.99c0 .81-.19 1.6-.57 2.373-.104.194-.19.335-.26.422l-.077.08.155.078a5.72 5.72 0 0 1 2.035 2.096 5.71 5.71 0 0 1 .738 2.861c0 .756-.146 1.481-.44 2.175a6.11 6.11 0 0 1-1.206 1.846c-.51.536-1.11.963-1.8 1.278a5.288 5.288 0 0 1-2.23.475h-8.346V14.715Zm6.972 7.276c.26 0 .51-.052.752-.157.242-.106.462-.25.662-.435a2.11 2.11 0 0 0 .48-.672c.12-.264.18-.554.18-.87 0-.545-.185-1.042-.557-1.49-.372-.449-.886-.673-1.542-.673h-4.018v4.271l4.044.026Zm-4.042 8.544h5.417a2.693 2.693 0 0 0 1.97-.83c.241-.255.432-.55.57-.883.138-.334.207-.685.207-1.055 0-.37-.07-.72-.207-1.054a2.88 2.88 0 0 0-.57-.883 2.693 2.693 0 0 0-1.97-.83h-5.417v5.537-.002ZM78.714 15.875c0 .492-.168.91-.505 1.252a1.66 1.66 0 0 1-1.231.515c-.484 0-.92-.172-1.257-.515a1.719 1.719 0 0 1-.506-1.252c0-.492.169-.937.506-1.279a1.69 1.69 0 0 1 1.257-.514c.5 0 .894.171 1.23.514.338.343.506.77.506 1.279Zm-.285 2.821V33.54H75.5l-.025-14.844h2.954ZM87.839 33.54c-.692 0-1.344-.14-1.957-.422a5.298 5.298 0 0 1-1.62-1.147 5.453 5.453 0 0 1-1.102-1.674 5.078 5.078 0 0 1-.401-2.003v-7.54h-2.152v-3.006h2.151v-3.665h2.929v3.665h2.954v3.005h-2.954v7.593a2.3 2.3 0 0 0 .596 1.529c.19.21.415.378.673.501.26.123.544.184.856.184h2.021v2.98h-1.995ZM91.752 33.54V14.082h2.954V33.54h-2.954ZM111.762 33.54h-2.903v-1.37l-.285.21a7.76 7.76 0 0 1-2.1 1.106 7.143 7.143 0 0 1-2.359.396 7.344 7.344 0 0 1-3.007-.62 7.547 7.547 0 0 1-2.423-1.687 8.1 8.1 0 0 1-1.62-2.478 7.606 7.606 0 0 1-.596-2.993 7.7 7.7 0 0 1 .596-3.005c.397-.95.942-1.78 1.633-2.492a7.537 7.537 0 0 1 2.436-1.674 7.415 7.415 0 0 1 2.981-.606c.968.017 1.806.158 2.514.422a7.735 7.735 0 0 1 1.996 1.107l.232.21v-.553h2.903v14.026l.002.001Zm-2.904-7.435c0-.72-.129-1.375-.389-1.965a4.696 4.696 0 0 0-1.049-1.516 4.906 4.906 0 0 0-1.517-.989 4.601 4.601 0 0 0-1.788-.356c-.622 0-1.249.128-1.828.382a5.099 5.099 0 0 0-1.529 1.028 4.759 4.759 0 0 0-1.412 3.415c0 .703.134 1.353.401 1.95a4.96 4.96 0 0 0 1.063 1.543c.44.431.946.765 1.516 1.002.57.238 1.157.356 1.763.356a4.57 4.57 0 0 0 1.943-.408 4.713 4.713 0 0 0 1.503-1.081c.415-.448.739-.963.973-1.543.234-.58.349-1.187.349-1.819l.001.001ZM116.271 17.985l4.277 9.913 3.68-9.913h3.137l-5.807 15.661-.077.158-1.348 3.639-3.162.026 2.074-5.563-5.988-13.921h3.214ZM130.554 26.87l.052.21c.138.58.375 1.086.713 1.517.337.43.716.795 1.141 1.094.423.3.872.523 1.347.672.476.15.921.225 1.335.225.933 0 1.74-.225 2.424-.673.682-.448 1.274-1.068 1.775-1.86l2.644 1.24c-.432.81-.93 1.482-1.491 2.018a8.305 8.305 0 0 1-1.763 1.304 7.119 7.119 0 0 1-1.866.712 8.457 8.457 0 0 1-1.853.21 6.835 6.835 0 0 1-2.865-.62 7.96 7.96 0 0 1-2.398-1.674 8.093 8.093 0 0 1-1.645-2.439 7.14 7.14 0 0 1-.61-2.913c0-1.072.195-2.088.584-3.046a8.16 8.16 0 0 1 1.606-2.53 7.537 7.537 0 0 1 2.424-1.727 7.274 7.274 0 0 1 3.033-.633c1.089 0 2.069.207 2.994.62a7.872 7.872 0 0 1 2.423 1.674 7.862 7.862 0 0 1 1.634 2.466c.397.94.595 1.947.595 3.019 0 .105-.004.294-.013.567a5.288 5.288 0 0 1-.039.567h-12.183.002Zm8.89-3.138a5.615 5.615 0 0 0-.596-1.028 4.44 4.44 0 0 0-2.126-1.569 4.675 4.675 0 0 0-1.58-.25 4.72 4.72 0 0 0-1.296.184 4.507 4.507 0 0 0-1.219.553 4.852 4.852 0 0 0-1.814 2.162l-.026.131h8.735l-.078-.184v.001ZM147.505 17.985v1.107l.285-.185c.467-.28.963-.504 1.491-.672a5.508 5.508 0 0 1 1.671-.25c.346 0 .691.026 1.037.079.346.052.682.122 1.01.21l-1.14 2.716a2.065 2.065 0 0 0-.454-.066 9.415 9.415 0 0 0-.375-.014 3.408 3.408 0 0 0-2.514 1.082 3.826 3.826 0 0 0-.738 1.16c-.181.44-.272.914-.272 1.424v8.964h-2.903V17.985h2.903-.001Z" />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h153v44H0z" />
      </clipPath>
    </defs>
  </svg>
);

export default SvgComponent;
