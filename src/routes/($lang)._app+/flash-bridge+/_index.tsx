import { useEffect, useState } from 'react';
import { Form, useLoaderData, useNavigation, useRevalidator, useLocation } from '@remix-run/react';
import { LoaderFunctionArgs, json, AppLoadContext } from '@remix-run/cloudflare';
import { Link } from '@/components/i18n/link';
import { WalletConnector, ChainIcon, ConnectWalletDialog } from '@/components/featured/wallet';
import { ChevronDown, LoaderIcon } from 'lucide-react';
import InfoIcon from '@/components/icons/InfoIcon';
import { Button } from '@/components/ui/button';
import { useDialog } from '@/hooks/dialog';
import { cn } from '@/lib/utils';

import { useManagedWallet } from '@/hooks/header';
import { useAccount } from '@/hooks/wallet/account';
import { bridgeI18nKey } from '@/modules/bridge/components/common';
import { bitlayerMainnet } from '@/wallets/config/chains';
import { createAPI } from '@/lib/api/gas-v2';
import { commitSession, getSession } from '@/modules/session';
import { useMediaQuery } from '@react-hook/media-query';
import { isAddress } from 'viem';
import { ClientOnly } from 'remix-utils/client-only';
import { Textarea } from '@/components/ui/textarea';
import { Text } from '@/components/ui/text';
import { useTranslation } from 'react-i18next';
import CornerMark, { CornerMarkGroup } from '@/components/ui/corner-mark';
import { Dialog, DialogContent } from '@/components/ui/dialog';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

import { COINS_MAP, COIN_NAME_TYPE } from '@/components/icons/coins';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import PhishingICon from '@/components/icons/PhishingICon';
import AuthorIcon from '@/components/icons/AuthorIcon';
import HighYieldIcon from '@/components/icons/HighYieldIcon';
import PonzischemeIcon from '@/components/icons/PonzischemeIcon';
import FreeAirdropIcon from '@/components/icons/FreeAirdropIcon';
import ContractIcon from '@/components/icons/ContractIcon';
import { USDCChangeDialog, USDCSwapProvider } from '@/modules/bridge/components/usdc-change';

const i18Keys = 'pages.getGas';

interface Option {
  value: number;
  amount: string;
  estimated: string;
}

const fromOptions: COIN_NAME_TYPE[] = ['USDT', 'USDC'];
const feePromotion = 1;

export const SelectChainField = ({
  current,
  options,
  onChange,
}: {
  current: COIN_NAME_TYPE;
  options: COIN_NAME_TYPE[];
  onChange: (value: COIN_NAME_TYPE) => void;
}) => {
  const selectButtonClass =
    'md:bl-min-w-52 md:bl-w-full md:bl-h-10 md:bl-text-lg/none md:bl-px-4 bl-text-white bl-border-input';
  const menuItemClass = 'bl-text-base/5 bl-flex bl-gap-2 bl-items-center';

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          type="button"
          className={selectButtonClass}
          outlineClassName="bl-border-input"
        >
          <div className="bl-flex bl-gap-2 bl-items-center bl-min-w-[100px] bl-justify-between bl-w-full">
            <div className="bl-flex bl-gap-2 bl-items-center">
              <ChainIcon icon={COINS_MAP[current]} className="bl-size-5" />
              {current}
            </div>
            <ChevronDown className="bl-size-5 bl-text-primary bl-duration-200 group-data-[state=open]:bl-rotate-180" />
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent sideOffset={8} className="md:bl-min-w-52 md:bl-w-full bl-border-primary">
        {options.map((coin: COIN_NAME_TYPE) => {
          return (
            <DropdownMenuItem key={coin} className={menuItemClass} onClick={() => onChange(coin)}>
              <ChainIcon icon={COINS_MAP[coin]} className="bl-size-5" />
              {coin}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  const session = await getSession(request);
  const api = createAPI(context);

  const config = await api.loadConfig();
  const {
    data: { status = 0 },
  } = await api.getBirdgeState();
  session.set('gas.encrypt_key', config.data.encrypt_key);
  session.set('gas.request_id', config.data.request_id);

  return json(
    {
      requestId: config.data.request_id,
      face_values: config.data.face_values,
      context,
      bridgeState: status,
    },
    {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    },
  );
}

export const FormSection = ({
  children,
  className,
}: {
  children?: React.ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        'bl-relative bl-w-full bl-px-5 bl-py-3 bl-space-y-3 md:bl-space-y-3.5 bl-bg-background bl-border bl-border-card-border',
        className,
      )}
    >
      {children}
    </div>
  );
};

const WarningIcon = ({
  text,
  icon: Icon,
  className,
}: {
  text: string;
  className?: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}) => {
  return (
    <div className="bl-flex bl-h-[38px] bl-items-center">
      <div className="bl-w-[27px] md:bl-w-10 bl-flex-center">
        <Icon className={cn('bl-mr-3 md:bl-mr-0', className)} />
      </div>
      <div className="bl-text-xs md:bl-text-[16px]">{text}</div>
    </div>
  );
};

const WarningContent = ({
  isBridge,
  closeDialog,
}: {
  isBridge: boolean;
  closeDialog: () => void;
}) => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery('(max-width: 640px)');

  const onSkip = () => {
    window.open(
      'https://t.me/pepe_miner_game_bot/miniapp?startapp=__bfbb57186eb34ff2955e60210cf716e4',
      '_blank',
    );
    closeDialog();
  };
  return (
    <div
      className="bl-text-center bl-pb-12 bl-items-center bl-p-6 bl-bg-contain"
      style={{
        background:
          'url(/images/gas/dialog-grid.png), linear-gradient(180deg, rgba(227, 110, 27, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%), #000',
      }}
    >
      <div className="bl-px-4 md:bl-px-[34px]">
        <div className="bl-text-xl md:bl-text-[32px] bl-pt-5 bl-pb-3">{t(`${i18Keys}.kind`)}</div>
        <div className="bl-text-xs md:bl-text-[16px]/[1.2] bl-text-left bl-text-secondary">
          {t(`${i18Keys}.${isBridge ? 'hasBridge' : 'notBridge'}`)}
        </div>
        {!isBridge ? (
          <div className="bl-flex bl-justify-center bl-pt-8">
            <Button
              overlayVariant="secondary"
              className={cn('bl-text-white bl-w-[194px]', { 'btn-xs': isMobile })}
              onClick={closeDialog}
              size={isMobile ? 'xs' : 'default'}
            >
              <div>{t('common.confirm')}</div>
            </Button>
          </div>
        ) : (
          <>
            <div className="bl-text-white md:bl-text-2xl bl-pt-8 bl-pb-4 bl-text-xs">
              {t(`${i18Keys}.commonRisk`)}
            </div>
            <div className="bl-grid bl-grid-cols-2">
              <WarningIcon text={t(`${i18Keys}.phishing`)} icon={PhishingICon} className="" />
              <WarningIcon text={t(`${i18Keys}.authorization`)} icon={AuthorIcon} className="" />
              <WarningIcon text={t(`${i18Keys}.highYield`)} icon={HighYieldIcon} className="" />
              <WarningIcon text={t(`${i18Keys}.ponzischeme`)} icon={PonzischemeIcon} className="" />
              <WarningIcon text={t(`${i18Keys}.freeAirdrop`)} icon={FreeAirdropIcon} className="" />
              <WarningIcon
                text={t(`${i18Keys}.contractLoophole`)}
                icon={ContractIcon}
                className=""
              />
            </div>
            <div className="bl-flex bl-justify-between bl-pt-8">
              <Button
                variant="outline-2"
                className={cn('md:bl-w-30 bl-w-[100px] bl-text-xs md:bl-text-[16px]', {
                  'btn-xs': isMobile,
                })}
                onClick={closeDialog}
                size={isMobile ? 'xs' : 'default'}
              >
                <div>{t('common.cancel')}</div>
              </Button>
              <Button
                overlayVariant="secondary"
                className={cn('bl-text-white bl-text-xs md:bl-text-[16px]', { 'btn-xs': isMobile })}
                onClick={onSkip}
                size={isMobile ? 'xs' : 'default'}
              >
                <div>{t(`${i18Keys}.aware`)}</div>
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

const ExchangeOption = ({
  option,
  active,
  onClick,
  unit,
}: {
  option: Option;
  active?: boolean;
  onClick?: (value: number) => void;
  unit: 'BTC' | 'U';
}) => {
  return (
    <button
      type="button"
      onClick={() => onClick?.(option.value)}
      className={cn(
        'bl-w-[78px] md:bl-w-[132px] bl-h-[90px] md:bl-h-24 bl-bg-background bl-group bl-border-0.5 bl-border-t-2 bl-border-secondary',
        'bl-flex bl-flex-col bl-items-center bl-justify-between bl-pt-5 bl-pb-6 md:bl-pt-5 md:bl-pb-3',
        {
          'bl-border-primary': active,
          'bl-bg-gradient-to-r bl-from-[#E36E1B]/20 bl-to-[#000000]/20': active,
          'hover:bl-border-primary bl-duration-200': !active,
        },
      )}
    >
      <span
        className={cn('bl-text-xl/none bl-pb-2 md:bl-pb-0', {
          'bl-text-white': active,
        })}
      >
        {option.amount}U
      </span>
      <span className={cn('bl-text-xs group-hover:bl-text-primary', { 'bl-text-primary': active })}>
        Get ~{unit === 'BTC' ? option.estimated : parseInt(option.estimated)}
        {unit}
      </span>
    </button>
  );
};

const ExchangeSelection = ({
  value,
  onChange,
  face_values,
  context,
  source,
}: {
  value: number;
  onChange?: (v: number) => void;
  face_values: string[];
  context: AppLoadContext;
  source: string;
}) => {
  const { t, i18n } = useTranslation('', { keyPrefix: bridgeI18nKey });
  const [fromCurrent, setFromCurrent] = useState(fromOptions[0]);
  const [toCurrent, setToCurrent] = useState<COIN_NAME_TYPE>('BTC');
  const [toOptions, setToOptions] = useState<COIN_NAME_TYPE[]>([]);
  const [options, setOptions] = useState<Option[]>([]);

  useEffect(() => {
    setToOptions(['BTC', fromCurrent]);
    setToCurrent('BTC');
  }, [fromCurrent]);

  const getPrice = async ({
    from_coin,
    to_coin,
  }: {
    from_coin: COIN_NAME_TYPE;
    to_coin: COIN_NAME_TYPE;
  }) => {
    const api = createAPI(context);
    const amounts = await api.estimateAmount({
      amount: face_values,
      from_coin,
      to_coin,
    });

    const options = amounts.data.face_values.map((value, index) => ({
      value: index,
      amount: value.face_value,
      estimated: value.target_face_value,
    }));
    setOptions(options);
  };

  useEffect(() => {
    getPrice({ from_coin: fromCurrent, to_coin: toCurrent });
  }, [fromCurrent, toCurrent]);

  return (
    <>
      <div className="bl-w-full bl-relative bl-flex bl-flex-col md:bl-flex-row bl-justify-between bl-items-end bl-pb-4.5">
        <div className="bl-h-15 md:bl-h-auto bl-px-4 md:bl-px-0 bl-border md:bl-border-0 bl-border-input bl-bg-black md:bl-bg-inherit md:bl-space-y-2.5 bl-w-full md:bl-w-auto bl-flex md:bl-block bl-items-center bl-justify-between bl-mb-5 md:bl-mb-0">
          <div className="bl-text-base/none">{t('from')}</div>
          <SelectChainField current={fromCurrent} options={fromOptions} onChange={setFromCurrent} />
        </div>
        <div className="bl-absolute bl-top-12 bl-w-full md:bl-w-auto md:bl-static bl-flex-center">
          <div className="bl-w-10 bl-h-10 bl-rotate-90 md:bl-rotate-0 bl-bg-black md:bl-bg-inherit bl-rounded-full bl-border bl-border-input bl-flex-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="12"
              viewBox="0 0 14 12"
              fill="none"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M7.17827 10.1556L10.2589 7.08991L1.0659 7.04762C0.925234 7.0471 0.78605 7.01864 0.656314 6.96389C0.526579 6.90914 0.408841 6.82916 0.309837 6.72854C0.210834 6.62792 0.13251 6.50862 0.0793471 6.37748C0.0261836 6.24635 -0.000774822 6.10594 1.48328e-05 5.9643C0.000670019 5.82274 0.0290247 5.68271 0.0834604 5.55219C0.137896 5.42167 0.217345 5.30324 0.317264 5.20366C0.417183 5.10408 0.535613 5.0253 0.665784 4.97184C0.795957 4.91837 0.935319 4.89126 1.0759 4.89205L10.2669 4.93333L7.22726 1.83639C7.12819 1.73595 7.04975 1.61683 6.99642 1.48584C6.9431 1.35485 6.91594 1.21456 6.9165 1.07299C6.91706 0.931424 6.94532 0.791358 6.99967 0.660797C7.05402 0.530237 7.1334 0.411743 7.23326 0.31209C7.43516 0.110934 7.7081 -0.00130899 7.99213 1.28262e-05C8.27616 0.00133369 8.54806 0.116112 8.7481 0.319138L13.7476 5.4025C13.9111 5.5698 14.0018 5.79563 14 6.03032C13.9981 6.26501 13.9037 6.48934 13.7376 6.65396L8.68511 11.688C8.48338 11.8891 8.2106 12.0013 7.92674 12C7.64287 11.9987 7.37114 11.8839 7.17127 11.6809C7.07223 11.5804 6.99384 11.4611 6.94059 11.33C6.88734 11.1989 6.86027 11.0585 6.86092 10.9168C6.86157 10.7752 6.88993 10.6351 6.94438 10.5044C6.99883 10.3738 7.07831 10.2553 7.17827 10.1556Z"
                fill="#AEB5C5"
              />
            </svg>
          </div>
        </div>
        <div className="bl-h-15 md:bl-h-auto bl-px-4 md:bl-px-0 bl-border md:bl-border-0 bl-border-input bl-bg-black md:bl-bg-inherit md:bl-space-y-2.5 bl-w-full md:bl-w-auto bl-flex md:bl-block bl-items-center bl-justify-between">
          <div className="bl-text-base/none">{t('to')}</div>
          <SelectChainField current={toCurrent} options={toOptions} onChange={setToCurrent} />
        </div>
      </div>
      {options[value]?.amount && (
        <input type="hidden" name="amount" value={options[value]?.amount} />
      )}
      <input type="hidden" name="from_coin" value={fromCurrent} />
      <input type="hidden" name="to_coin" value={toCurrent} />
      <input type="hidden" name="language" value={i18n.language} />
      <input type="hidden" name="source" value={source} />
      <div className="bl-flex bl-justify-between bl-items-center bl-gap-2 bl-pb-3">
        {options.slice(0, 4).map((option, index) => (
          <ExchangeOption
            key={index}
            option={option}
            active={value === index}
            onClick={(v) => onChange?.(v)}
            unit={toCurrent === 'BTC' ? 'BTC' : 'U'}
          />
        ))}
      </div>
      <div className="bl-flex bl-justify-center bl-gap-3 md:bl-gap-2">
        {options.slice(4).map((option, index) => (
          <ExchangeOption
            key={index + 4}
            option={option}
            active={value === index + 4}
            onClick={(v) => onChange?.(v)}
            unit={toCurrent === 'BTC' ? 'BTC' : 'U'}
          />
        ))}
      </div>

      <USDCSwapProvider>
        <USDCChangeDialog enabled={fromCurrent === 'USDC'} />
      </USDCSwapProvider>
    </>
  );
};

const ReceiverSection = ({
  address,
  onAddressChange,
}: {
  address?: string;
  onAddressChange?: (v: string) => void;
}) => {
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const { t } = useTranslation();
  return (
    <FormSection className="bl-px-5 bl-py-3 md:bl-space-y-2">
      <div className="bl-flex bl-items-center bl-justify-between bl-text-base/none">
        <div>{t(`${i18Keys}.recipient`)}</div>
      </div>
      {isDesktop ? (
        <div
          className={cn('bl-text-white bl-text-sm bl-break-all', {
            'bl-text-secondary/60 bl-text-base/5': !address,
          })}
        >
          {address ?? t(`${i18Keys}.placeholder`)}
          <input type="hidden" name="address" value={address} />
        </div>
      ) : (
        <Textarea
          name="address"
          placeholder={t(`${i18Keys}.placeholder`)}
          className="bl-min-h-4 bl-py-0 bl-text-base/5 placeholder:bl-font-body placeholder:bl-text-base/5 bl-px-0 bl-bg-background focus-visible:bl-text-white focus-visible:bl-bg-background bl-border-0 focus-visible:bl-ring-offset-0 focus-visible:bl-ring-0 focus-visible:bl-ring-transparent bl-transition-colors bl-resize-none"
          value={address}
          spellCheck={false}
          onChange={(e) => onAddressChange?.(e.target.value)}
        />
      )}
    </FormSection>
  );
};

const PageMain = ({ children, address }: { children: React.ReactNode; address?: string }) => {
  const { t } = useTranslation();
  const location = useLocation();
  const search = location.search;
  const urlParams = new URLSearchParams(search);
  const source = urlParams.get('source');
  const sourceParam = source ? `?source=${source}` : '';
  return (
    <div className="bl-w-full md:bl-w-[615px] bl-mt-7.5 md:bl-mt-5 bl-space-y-3">
      <div className="bl-flex bl-justify-end">
        <Link
          to={`/flash-bridge/history${sourceParam}`}
          state={{ address }}
          className={cn('bl-text-base/none hover:bl-underline', {
            'bl-invisible': !address,
          })}
        >
          {t(`${i18Keys}.history`)}
        </Link>
      </div>
      <div className="bl-bg-card-background bl-space-y-6 bl-border bl-border-card-border bl-p-3 bl-pb-6 md:bl-px-10 md:bl-py-3">
        {children}
      </div>
    </div>
  );
};

const ActionButton = ({ address, bridgeState }: { address?: string; bridgeState: number }) => {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const isSubmitting = navigation.formAction === '/flash-bridge/order';
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const chain = bitlayerMainnet;
  const buttonClass = 'bl-w-full';

  if (bridgeState === 0) {
    return (
      <Button variant="default" overlayVariant="outline" disabled={true} className={buttonClass}>
        <div className="bl-flex bl-items-center bl-gap-2">
          <span className="bl-hidden sm:bl-block">{t(`${i18Keys}.maintain`)}</span>
          <span className="sm:bl-hidden">{t(`${i18Keys}.maintainMobile`)}</span>
        </div>
      </Button>
    );
  }
  if (isDesktop) {
    if (!address) {
      return (
        <WalletConnector chain={chain}>
          <Button variant="default" overlayVariant="outline" className={buttonClass} type="button">
            <span>{t('common.connect')}</span>
          </Button>
        </WalletConnector>
      );
    }
  } else {
    let message = '';
    if (!address) {
      message = t(`${i18Keys}.placeholder`);
    } else if (!isAddress(address)) {
      message = t(`${i18Keys}.invalid`);
    }

    if (message) {
      return (
        <Button variant="secondary" disabled={true} className={buttonClass} type="button">
          <span>{message}</span>
        </Button>
      );
    }
  }

  return (
    <Button
      variant="default"
      overlayVariant="outline"
      disabled={isSubmitting}
      className={buttonClass}
      type="submit"
    >
      <div className="bl-flex bl-items-center bl-gap-2">
        {isSubmitting && <LoaderIcon className="bl-size-6 bl-animate-spin" />}
        <span>{t(`${i18Keys}.purchase`)}</span>
      </div>
    </Button>
  );
};

export default function GasV2Page() {
  const data = useLoaderData<typeof loader>();
  const { t } = useTranslation();
  const chain = bitlayerMainnet;
  useManagedWallet({ chain });
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [visible, setVisible] = useState(false);
  const revalidator = useRevalidator();
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const [source, setSource] = useState('');
  const [isBridge, setIsBridge] = useState(false);
  const { open: openDialog } = useDialog();

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const target = urlParams.get('source');
    if (target) {
      setSource(target);
    }
  }, []);

  const { address: walletAddress } = useAccount({ network: chain.networkType });
  const [address, setAddress] = useState<string | undefined>(walletAddress);

  const closeDialog = () => {
    setVisible(false);
  };

  useEffect(() => {
    if (source && walletAddress) {
      getIsBridge();
    }
  }, [source, walletAddress]);

  const handleClickConnect = () => {
    openDialog({
      content: ({ close }) => (
        <ConnectWalletDialog
          title={t('common.connect')}
          description={t('common.connectDesc')}
          chain={chain}
          close={close}
        />
      ),
    });
  };

  const getIsBridge = async () => {
    if (walletAddress && source) {
      const api = createAPI(data.context);
      const res = await api.isBirdge({
        source,
        address: walletAddress,
      });
      setIsBridge(res.data?.hasBridge);
    }
  };

  const handlePepe = () => {
    if (address) {
      setVisible(true);
    } else {
      handleClickConnect();
    }
  };

  useEffect(() => {
    if (walletAddress) {
      setAddress(walletAddress);
    }
  }, [walletAddress, setAddress]);

  useEffect(() => {
    const timer = setInterval(() => {
      revalidator.revalidate();
    }, 60_000);
    return () => clearInterval(timer);
  });

  return (
    <section className="bl-w-full md:bl-w-auto bl-flex-center bl-flex-col">
      <PageMain address={address}>
        <Form className="bl-w-full bl-relative" method="POST" action="/flash-bridge/order">
          <ExchangeSelection
            face_values={data.face_values}
            context={data.context}
            value={activeIndex}
            onChange={setActiveIndex}
            source={source}
          />
          <div className="bl-flex bl-items-center bl-py-3">
            <div className="bl-text-primary bl-w-full bl-text-center">
              {t(`${i18Keys}.promotion`)}{' '}
              <span
                className="bl-text-[#B31515] bl-px-1 bl-decoration-slice"
                style={{
                  textDecoration: 'line-through',
                }}
              >
                $3
              </span>{' '}
              {`$${feePromotion}!`}
            </div>
          </div>
          <ClientOnly>
            {() => <ReceiverSection address={address} onAddressChange={setAddress} />}
          </ClientOnly>
          <div className="bl-space-y-2.5 bl-pt-[22px]">
            <ClientOnly>
              {() => <ActionButton address={address} bridgeState={data.bridgeState} />}
            </ClientOnly>
            <div className="bl-text-xs md:bl-text-base bl-text-indicator bl-text-center">
              {t(`${i18Keys}.estimated`)}
            </div>
          </div>
          <input type="hidden" name="request_id" value={data.requestId} />
          {/* <Link
            to="/bridge/gas"
            className="bl-flex bl-right-1 md:bl-right-[-16px] bl-items-center hover:bl-underline bl-bottom-[-22px] md:bl-bottom-[-8px] bl-absolute"
          >
            <span className="bl-text-sm bl-pr-1 bl-whitespace-nowrap">
              {t(`${i18Keys}.getRegular`)}
            </span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="10"
              height="8"
              viewBox="0 0 10 8"
              fill="none"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M5.12733 6.77042L7.32782 4.72661L0.761361 4.69842C0.660883 4.69806 0.561465 4.6791 0.468797 4.64259C0.376129 4.60609 0.29203 4.55277 0.221313 4.48569C0.150596 4.41861 0.0946508 4.33908 0.0566771 4.25166C0.0187034 4.16423 -0.000552539 4.07063 1.15973e-05 3.9762V3.9762C0.000479267 3.88183 0.020733 3.78847 0.0596155 3.70146C0.098498 3.61445 0.155247 3.53549 0.226618 3.46911C0.297988 3.40272 0.382581 3.3502 0.475561 3.31456C0.568542 3.27891 0.668086 3.26084 0.768503 3.26137L7.33353 3.28889L5.16233 1.22426C5.09156 1.1573 5.03553 1.07788 4.99745 0.990557C4.95936 0.90323 4.93996 0.809704 4.94036 0.715326C4.94075 0.620949 4.96094 0.527571 4.99977 0.440531C5.03859 0.35349 5.09529 0.274494 5.16662 0.208059V0.208059C5.31083 0.0739554 5.50579 -0.000873404 5.70866 7.55878e-06C5.91154 0.000888521 6.10575 0.0774072 6.24864 0.212758L9.8197 3.60167C9.93647 3.7132 10.0013 3.86375 9.99998 4.02021C9.99864 4.17667 9.93122 4.32623 9.81256 4.43598L6.20365 7.792C6.05956 7.92606 5.86472 8.00087 5.66196 7.99999C5.4592 7.99911 5.2651 7.92261 5.12233 7.7873V7.7873C5.05159 7.72023 4.9956 7.64073 4.95757 7.55332C4.91953 7.46591 4.90019 7.37232 4.90066 7.27789C4.90112 7.18346 4.92138 7.09003 4.96027 7.00296C4.99917 6.91589 5.05593 6.83687 5.12733 6.77042Z"
                fill="#AEB5C5"
              />
            </svg>
          </Link> */}
        </Form>
      </PageMain>
      <div className="bl-w-full bl-flex bl-items-center bl-gap-2 bl-pt-[10px] bl-text-left">
        {t(`${i18Keys}.aboutFlash`)}
        {isDesktop ? (
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger>
                <InfoIcon className="hover:bl-text-primary" />
              </TooltipTrigger>
              <TooltipContent className="bl-max-w-[230px]">
                <div dangerouslySetInnerHTML={{ __html: t(`${i18Keys}.aboutInfo`) }}></div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          <Popover>
            <PopoverTrigger>
              <InfoIcon className="hover:bl-text-primary" />
            </PopoverTrigger>
            <PopoverContent className="bl-max-w-[230px]">
              <div dangerouslySetInnerHTML={{ __html: t(`${i18Keys}.aboutInfo`) }}></div>
            </PopoverContent>
          </Popover>
        )}
      </div>
      {source === 'pepe' && (
        <div className="bl-bg-primary bl-w-full bl-mt-6">
          <Text
            className="bl-flex-center bl-cursor-pointer bl-p-4 bl-bg-cover bl-relative"
            style={{
              WebkitMaskSize: '100% 100%',
              background:
                'url(/images/gas/gas-dialog-bg.png) no-repeat center center, linear-gradient(180deg, rgb(76, 33, 25), #311705)',
            }}
            onClick={handlePepe}
          >
            <img src="/images/gas/PEPE.png" alt="" />

            <CornerMark position="tl" className="bl-left-3 bl-top-3" />
            <CornerMark position="tr" className="bl-right-3 bl-top-3" />
            <CornerMark position="bl" className="bl-left-3 bl-bottom-3" />
            <CornerMark position="br" className="bl-right-3 bl-bottom-3" />
          </Text>
        </div>
      )}
      <Dialog open={visible}>
        <DialogContent className="md:bl-w-[460px] bl-p-0">
          <div className="bl-relative bl-z-20 bl-min-h-10 bl-font-body">
            <WarningContent isBridge={isBridge} closeDialog={closeDialog} />
            <CornerMarkGroup />
          </div>
        </DialogContent>
      </Dialog>
    </section>
  );
}
