import {
  Transaction,
  TransactionAddressContainer,
  TransactionAddressRow,
  TransactionHashRow,
  TransactionTitle,
  TransactionWarning,
} from '@/components/featured/transaction';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import * as Scroll<PERSON>rea from '@radix-ui/react-scroll-area';
import CornerMark from '@/components/ui/corner-mark';
import { useTransactionList } from '@/modules/bridge/hooks/transaction';
import { cn } from '@/lib/utils';
import { TransactionData } from '@/modules/bridge/transactions/types';
import { useLocation, useParams } from '@remix-run/react';
import { ChevronUp } from 'lucide-react';
import { useAtomValue } from 'jotai';
import { fromNetworkAtom } from '@/modules/bridge/hooks/config';
import { useChains } from '@/hooks/wallet/chains';
import { useManagedWallet } from '@/hooks/header';
import { useAccount } from '@/hooks/wallet/account';
import { GasTransactionAccordionItem } from './gas';
import { useGetGasTransactionList } from '@/hooks/gas/transaction';
import { NetworkType } from '@/wallets/config/type';

import { Trans, useTranslation } from 'react-i18next';
const historyI18nKey = 'pages.bridge';
interface LoadingProps {
  error?: string;
  message?: string | React.ReactNode;
}

export const Loading = ({ error, message }: LoadingProps) => {
  return (
    <div className="bl-border bl-border-input bl-relative bl-w-full bl-h-[228px] bl-bg-gradient-to-b bl-from-[#311705]/80 bl-to-background bl-flex bl-flex-col bl-items-center bl-justify-center bl-px-6">
      <div
        className={cn('bl-px-5 bl-py-1 bl-flex bl-gap-2 bl-items-center bl-text-xl bl-relative', {
          'bl-bg-primary/30': error,
        })}
      >
        {error ? (
          <>
            <div className="bl-text-primary bl-cursor-default">{error}</div>
            <CornerMark position="tl" className="[--bl-space:0]" />
            <CornerMark position="br" className="[--bl-space:0]" />
          </>
        ) : (
          <>
            <div className="bl-text-white bl-cursor-default bl-whitespace-nowrap">
              <Trans i18nKey={`${historyI18nKey}.loadingData`} />
            </div>
            <div className="dot-loader"></div>
          </>
        )}
      </div>
      {message}
      <CornerMark position="tl" />
      <CornerMark position="tr" />
      <CornerMark position="bl" />
      <CornerMark position="br" />
    </div>
  );
};

interface TransactionAccordionProps {
  data: TransactionData;
}

const TransactionAccordionItem = ({ data }: TransactionAccordionProps) => {
  return (
    <AccordionItem value={data.fromTxId} asChild>
      <Transaction>
        <AccordionTrigger className="bl-w-full bl-relative bl-gap-2">
          <TransactionTitle
            token={data.token}
            amount={data.amount}
            status={data.status}
            from={data.fromChain}
            timestamp={data.finishedTime ? data.finishedTime : data.createdTime}
          ></TransactionTitle>
          <ChevronUp className="bl-text-primary bl-duration-200 bl-min-w-0 bl-shrink-0" />
        </AccordionTrigger>
        <AccordionContent className="w-full bl-pb-0 bl-space-y-3.5">
          <TransactionAddressContainer>
            <TransactionAddressRow type="from" address={data.from} chain={data.fromChain} />
            <TransactionAddressRow type="to" address={data.to} chain={data.toChain} />
          </TransactionAddressContainer>
          <TransactionHashRow
            type="sender"
            hash={data.fromTxId}
            status={data.fromTxStatus}
            chain={data.fromChain}
          />
          <TransactionHashRow
            type="receiver"
            hash={data.toTxId}
            status={data.toTxStatus}
            chain={data.toChain}
          />
          {data.reason && <TransactionWarning message={data.reason} />}
        </AccordionContent>
      </Transaction>
    </AccordionItem>
  );
};

const BridgeHistoryMain = ({ address, testnet }: { address?: string; testnet?: boolean }) => {
  const { t } = useTranslation('', { keyPrefix: historyI18nKey });
  const { state } = useLocation();
  const { data, error, isLoading } = useTransactionList({
    from: address,
    init: state?.tx,
    testnet,
  });

  if (error) {
    return <Loading error={error.message} />;
  }

  if (!data && isLoading) return <Loading />;

  if (!data || data.length === 0) {
    return <Loading error={t('noData')} />;
  }

  return (
    <Accordion
      type="single"
      collapsible
      className="bl-w-full bl-space-y-3"
      defaultValue={data[0].fromTxId}
    >
      {data.map((item) => (
        <TransactionAccordionItem key={item.fromTxId} data={item} />
      ))}
    </Accordion>
  );
};

export default function HistoryPage() {
  const params = useParams();
  const { search } = useLocation();

  const urlParams = new URLSearchParams(search);
  const from = urlParams.get('from');
  const domain = urlParams.get('domain') as string;

  const testnet = params.network === 'testnet';
  const network = useAtomValue(fromNetworkAtom);

  const chains = useChains({ network: from === 'gas' ? NetworkType.evm : network, testnet });
  // const chains = useChains({ network, testnet });
  const { address } = useAccount({ network });

  useManagedWallet({ chain: chains[0] });

  return (
    <div className="bl-w-full md:bl-w-[600px] bl-space-y-6 md:bl-p-7.5 bl-mt-7.5 md:bl-border bl-border-card-border md:bl-bg-card-background">
      <div className="bl-w-full bl-relative bl-z-10 md:bl-space-y-5">
        <h2 className="bl-hidden md:bl-block bl-text-2xl/none bl-text-white bl-text-center">
          <Trans i18nKey={`${historyI18nKey}.historyTab`} />
        </h2>
        {from === 'gas' ? (
          <GasHistoryMain domain={domain} />
        ) : (
          <BridgeHistoryMain address={address} testnet={testnet} />
        )}
      </div>
    </div>
  );
}

const GasHistoryMain = ({ domain }: { domain: string }) => {
  const { data, error, isLoading } = useGetGasTransactionList({ baseURL: domain });

  if (error) {
    return <Loading error={error.message} />;
  }

  if (!data && isLoading) return <Loading />;

  if (!data || data.length === 0) {
    return <Loading error={'No transaction found'} />;
  }

  return (
    <ScrollArea.Root className="ScrollAreaRoot bl-h-[400px]">
      <ScrollArea.Viewport className="ScrollAreaViewport bl-h-full">
        <Accordion
          type="single"
          collapsible
          className="bl-w-full bl-space-y-3"
          defaultValue={'gas-history-0'}
        >
          {data.map((item: any, index: number) => {
            const accordionKey = `gas-history-${index}`;
            return (
              <GasTransactionAccordionItem
                key={accordionKey}
                data={item}
                accordionKey={accordionKey}
              />
            );
          })}
        </Accordion>
      </ScrollArea.Viewport>
      <ScrollArea.Scrollbar
        className="ScrollAreaScrollbar"
        orientation="vertical"
        // style={{
        //   padding: '1px',
        //   background: 'hsl(var(--primary))',
        //   transition: 'background 160ms ease-out',
        // }}
      >
        <ScrollArea.Thumb
          className="ScrollAreaThumb"
          // style={{
          //   background: 'blue',
          //   borderRadius: '2px',
          //   flex: '1'
          // }}
        />
      </ScrollArea.Scrollbar>
    </ScrollArea.Root>
  );
};
