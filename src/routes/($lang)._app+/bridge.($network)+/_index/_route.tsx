import React, { useEffect, useMemo, useState } from 'react';
import { useRemixForm } from 'remix-hook-form';
import { Button } from '@/components/ui/button';
import { useAccount } from '@/hooks/wallet/account';
import { useWalletBalance } from '@/hooks/wallet/balance';
import { UseEnsureOnChain } from '@/hooks/wallet/chains';
import {
  useChains,
  useChainTypes,
  useChainPairTokens,
  getChains,
} from '@/modules/bridge/hooks/chains';
import {
  fromNetworkAtom,
  useBridgeConfig,
  useDefaults,
  useEstimateFee,
} from '@/modules/bridge/hooks/config';
import { useTransfer } from '@/modules/bridge/hooks/transfer';
import { useManagedWallet, usePinnedHeader } from '@/hooks/header';
import { NavLink, useNavigate, useParams } from '@remix-run/react';
import { LoaderIcon } from 'lucide-react';
import { BigNumber, utils } from 'ethers';
import { mapToSerializable } from '@/modules/bridge/hooks/transaction';
import { CommonTextDialog } from '@/components/featured/global-dialog';
import { useDialog } from '@/hooks/dialog';
import { useSetAtom } from 'jotai';
import { ClientOnly } from 'remix-utils/client-only';
import { useTranslation } from 'react-i18next';
import { FormData, TransferFormProps, resolver } from '@/modules/bridge/components/form';
import { safeParseUnits } from '@/modules/bridge/components/common';
import { ClientTransferForm } from './form';
import { ReadableError } from '@/modules/bridge/transactions/errors';
import { BTCCautionDialog } from '@/modules/bridge/components/btc-caution';
import { ThirdPartyBridge } from '@/modules/bridge/components/third-party';
import { USDCChangeDialog, USDCSwapProvider } from '@/modules/bridge/components/usdc-change';
import { useXverseAddress } from '@/modules/bridge/hooks/xverse';
import { Dialog, DialogClose, DialogContent } from '@/components/ui/dialog';
import BitlayerLogo from '@/components/icons/BitlayerLogo';
import { CornerMarkGroup } from '@/components/ui/corner-mark';
import CloseIcon from '@/components/icons/CloseIcon';
import { title } from 'process';

const bridgeI18nKey = 'pages.bridge';

export default function BridgePage() {
  usePinnedHeader();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const params = useParams();
  const testnet = params.network === 'testnet';

  const [[sourceChainType, targetChainType], setChainTypes] = useChainTypes(testnet);

  const leftOptions = useChains({ relation: sourceChainType, testnet });
  const rightOptions = useChains({ relation: targetChainType, testnet, as: 'target' });

  const { transfer, state: transferState } = useTransfer();
  const { ensure } = UseEnsureOnChain();

  const { setSelectedChains, setSelectedToken, ...defaultValues } = useDefaults(
    leftOptions,
    rightOptions,
  );

  const form = useRemixForm<FormData>({
    mode: 'onSubmit',
    resolver,
    defaultValues,
  });
  const { setValue, reset: resetForm } = form;

  const left = form.watch('left');
  const right = form.watch('right');
  const tokenId = form.watch('token');
  const amount = form.watch('amount');

  const fromChain = useMemo(
    () => leftOptions.find((item) => item.id === left),
    [leftOptions, left],
  );
  const toChain = useMemo(
    () => rightOptions.find((item) => item.id === right),
    [rightOptions, right],
  );

  const tokens = useChainPairTokens(fromChain?.id, toChain?.id);
  const token = useMemo(() => tokens.find((item) => item.id === tokenId)!, [tokenId, tokens]);

  // Save the selected chain and token to the global store
  useEffect(() => {
    if (fromChain && toChain && token) {
      setSelectedChains([fromChain.id, toChain.id]);
      setSelectedToken(token.id);
    }
  }, [fromChain, toChain, token]);

  // This contorls the wallet button on the header
  useManagedWallet({ chain: fromChain });

  // switch xverse address
  useXverseAddress({ token });

  const { address: senderAddress } = useAccount({ network: fromChain?.networkType });

  const { data: balance } = useWalletBalance({
    network: fromChain?.networkType,
    address: senderAddress,
    token,
    chain: fromChain, // to make sure the balance is from the correct chain
  });

  const setFromNetwork = useSetAtom(fromNetworkAtom);

  useEffect(() => {
    if (!fromChain) {
      return;
    }
    setFromNetwork(fromChain.networkType);
    ensure({ chain: fromChain });
  }, [fromChain, ensure, setFromNetwork]);

  const { data: bridgeConfig } = useBridgeConfig({ fromChain, toChain, token });
  const { open: openDialog } = useDialog();

  // handle page navigation for example from testnet to mainnet
  useEffect(() => {
    if (
      !leftOptions.find((item) => item.id === left) ||
      !rightOptions.find((item) => item.id === right)
    ) {
      resetForm({
        left: leftOptions[0].id,
        right: rightOptions[0].id,
        amount: '',
        address: '',
        payload: '',
      });
    }
  }, [leftOptions, left, rightOptions, right, resetForm]);

  // Keeps the token selection in sync with the token list
  useEffect(() => {
    if (!tokens.length) {
      return;
    }
    if (tokens.some((item) => item.id === tokenId)) {
      setValue('token', tokenId);
    } else {
      setValue('token', tokens[0].id);
    }
    setValue('amount', '');
  }, [tokens, tokenId, setValue]);

  const { data: estimatedFee } = useEstimateFee({
    fromChain,
    toChain,
    token,
    amount: token && amount ? safeParseUnits(amount, token.decimals) : undefined,
    fee: bridgeConfig?.bridgeFee,
  });

  const [messageDialogOpen, setMessageDialogOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState({ title: '', description: '' });

  const handleSwap = (): boolean | undefined => {
    // prevent swapping when any of the chain list is empty
    const newLeftChains = getChains({ relation: targetChainType, testnet });
    const newRightChains = getChains({ relation: sourceChainType, testnet, as: 'target' });
    if (!newLeftChains.length || !newRightChains.length) {
      return false;
    }

    setChainTypes([targetChainType, sourceChainType]);
    setValue('left', right);
    setValue('right', left);
    setValue('amount', '');
    setValue('address', '');
  };

  const handleTransfer = (e: React.FormEvent) => {
    e.preventDefault();

    const amount = form.getValues('amount');
    const left = form.getValues('left');
    const right = form.getValues('right');
    const fromChain = leftOptions.find((item) => item.id === left);
    const toChain = rightOptions.find((item) => item.id === right);
    const receiverAddress = form.getValues('address');

    if (
      !amount ||
      !senderAddress ||
      !receiverAddress ||
      !fromChain ||
      !toChain ||
      !token ||
      !bridgeConfig
    ) {
      console.warn('invalid form data');
      return;
    }

    const params = {
      from: senderAddress,
      to: receiverAddress,
      fromChain,
      toChain,
      amount: utils.parseUnits(amount, token.decimals),
      token,
      bridgeAddress: bridgeConfig.fromChainReceiver,
      fee: bridgeConfig.bridgeFee,
      feeReceiver: bridgeConfig.bridgeFeeReceiver,
      payload: form.getValues('payload'),
    };

    transfer(params, {
      onSuccess: (data) => {
        navigate(
          {
            pathname: './history',
          },
          {
            state: {
              tx: mapToSerializable(data),
            },
          },
        );
      },
      onError: (error) => {
        console.error(error);
        let title = 'transferFailed';
        let message = 'transferError';
        let data: Record<string, unknown> | undefined;
        if (error instanceof ReadableError) {
          message = error.message;
          if (error.title) {
            title = error.title;
          }
          data = error.data;
        }

        setErrorMessage({
          title: t(`${bridgeI18nKey}.${title}`),
          description: t(`${bridgeI18nKey}.errors.${message}`, data),
        });
        setMessageDialogOpen(true);
        // openDialog({
        //   content: () => (
        //     <CommonTextDialog
        //       title={t(`${bridgeI18nKey}.transferFailed`)}
        //       description={t(`${bridgeI18nKey}.errors.${message}`)}
        //     />
        //   ),
        // });
      },
    });
  };

  const formProps: TransferFormProps = {
    form,
    leftOptions,
    rightOptions,
    fromChain,
    toChain,
    balance,
    tokens,
    state: transferState,
    // fee: bridgeConfig?.bridgeFee,
    fee: estimatedFee as BigNumber | undefined,
    max: bridgeConfig?.maxAmount,
    min: bridgeConfig?.minAmount,
    onSubmit: handleTransfer,
    onSwap: handleSwap,
  };

  const tutorialLink =
    'https://medium.com/@Bitlayer/bitlayer-official-bridge-cross-chain-cross-in-tutorial-okx-wallet-149014fcdf05';

  const renderBridgeTip = () => {
    return (
      <>
        <div className="bl-flex bl-justify-end">
          <a
            href={tutorialLink}
            target="_blank"
            rel="noreferer noreferrer"
            className="bl-underline hover:bl-text-primary"
          >
            {t(`${bridgeI18nKey}.newToBridge`)}
          </a>
        </div>
      </>
    );
  };

  return (
    <div className="bl-w-full md:bl-w-[600px] bl-mt-7.5 md:bl-mt-5 bl-space-y-3">
      <div className="bl-hidden md:bl-flex bl-items-end bl-justify-between bl-mt-1.5">
        {renderBridgeTip()}
      </div>
      <div className="bl-bg-card-background bl-space-y-3 bl-border bl-border-card-border bl-p-2.5 md:bl-p-7.5">
        <ClientOnly>{() => <ClientTransferForm {...formProps} />}</ClientOnly>
      </div>
      <div className="bl-flex md:bl-hidden bl-items-center bl-justify-between bl-mt-1.5 bl-pb-6">
        {renderBridgeTip()}
      </div>
      <div className="bl-flex bl-justify-center bl-mt-7.5">
        {senderAddress && (
          <NavLink to={`./history`}>
            {({ isPending }) => (
              <Button
                variant="outline"
                overlayVariant="secondary"
                size="lg"
                className="bl-h-10 md:bl-min-w-40 hover:bl-border-primary"
                outlineClassName="group-hover/button:bl-border-primary"
              >
                <div className="bl-flex bl-items-center bl-gap-2">
                  {isPending && <LoaderIcon className="bl-size-6 bl-animate-spin" />}
                  <span>{t(`${bridgeI18nKey}.historyTab`)}</span>
                </div>
              </Button>
            )}
          </NavLink>
        )}
      </div>
      <ThirdPartyBridge token={token} />
      <BTCCautionDialog />

      <USDCSwapProvider>
        <USDCChangeDialog enabled={token?.id === 'USDC'} />
      </USDCSwapProvider>

      <Dialog open={messageDialogOpen} onOpenChange={setMessageDialogOpen}>
        <DialogContent>
          <div className="bl-relative bl-z-20 bl-flex bl-flex-col bl-gap-6 bl-items-center bl-p-6 bl-bg-primary bl-text-black bl-font-body bl-overflow-hidden">
            <BitlayerLogo className="bl-text-black" />

            <div>
              <CommonTextDialog title={errorMessage.title} description={errorMessage.description} />
            </div>

            <DialogClose asChild>
              <Button
                className="bl-size-7.5 bl-rounded-full bl-border bl-border-black bl-text-black bl-text-base/none hover:bl-text-primary bl-translate-y-2 bl-px-0 bl-py-0"
                style={{ WebkitMask: 'none' }}
              >
                <CloseIcon className="bl-size-3" />
              </Button>
            </DialogClose>
            <CornerMarkGroup variant="dark" />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
