import { TaskFolders } from '@/modules/user-center/components/task';

import { useMatches } from '@remix-run/react';
import { ClientOnly } from 'remix-utils/client-only';
import { LoaderResponse } from '../_layout';

export default function Tasks() {
  const matches = useMatches();
  const { tasks } = matches?.find(
    (match) => match.id === 'routes/($lang)._app+/me+/_index+/_layout',
  )?.data as LoaderResponse;

  if (!tasks) {
    return null;
  }
  return (
    <>
      <ClientOnly>
        {() => {
          return (
            <section
              className="bl-container bl-pt-[50px] bl-flex bl-flex-col bl-items-center lg:bl-w-[1440px]"
              id="tasks-section"
            >
              <TaskFolders tasks={tasks} />
            </section>
          );
        }}
      </ClientOnly>
    </>
  );
}
