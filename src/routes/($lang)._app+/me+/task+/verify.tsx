import { commitSession, getSession } from '@/modules/session';
import { createAPIClient } from '@/modules/user-center/api.server';
import { ActionFunctionArgs, json } from '@remix-run/cloudflare';

export async function action({ request }: ActionFunctionArgs) {
  const body = await request.json<{ taskId: number }>();
  const session = await getSession(request);

  const token = session.get('user.token') as string | undefined;
  if (!token) {
    return json({ error: 'Unauthorized' }, { status: 403 });
  }

  const api = createAPIClient(token);
  const { code, data: result } = await api.verifyTask(body.taskId);
  if (code) {
    return json(
      { message: 'Error occured while verifying task.', code },
      {
        status: 400,
        headers: {
          'Set-Cookie': await commitSession(session),
        },
      },
    );
  }

  if (!result.isVerified) {
    return json(
      { message: 'Task not verified' },
      {
        status: 400,
        headers: {
          'Set-Cookie': await commitSession(session),
        },
      },
    );
  }

  return json(
    {
      message: 'ok',
    },
    {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    },
  );
}
