import { AnimatePageLayout } from '@/components/ui/page';
import { Title } from '@/components/ui/title';
import { Trans } from 'react-i18next';
export default function BtcFiBanner() {
  return (
    <AnimatePageLayout>
      <div className="bl-w-full bl-relative bl-overflow-hidden before:bl-mt-[-50px] before:md:bl-mt-0 bl-z-[1] before:bl-absolute before:-bl-z-[2] before:-bl-top-[21px] before:md:bl-top-[-1px] before:md:bl-left-0 before:bl-w-full before:bl-h-[237px] before:md:bl-h-30 before:bl-bg-[url('/images/btcfi/bg-yield-m.png')] before:md:bl-bg-[url('/images/btcfi/bg-yield.png')] before:bl-bg-transparent md:before:bl-bg-black before:bl-bg-[length:cover] before:bl-bg-no-repeat before:bl-bg-[center_center]">
        <div className="bl-container bl-overflow-hidden bl-relative bl-h-[167px] md:bl-h-auto bl-z-10">
          <section className="bl-container md:bl-block bl-pt-4">
            <div className="bl-flex bl-relative bl-w-full bl-flex-col lg:bl-justify-between lg:bl-flex-row bl-gap-6 lg:bl-gap-10">
              <div className="bl-flex bl-w-full md:bl-px-36 bl-items-center bl-relative bl-justify-center bl-gap-4 md:bl-gap-[26px] bl-z-[20] md:bl-z-[0]">
                <div className="bl-flex bl-flex-row bl-gap-2">
                  <Title
                    variant="white"
                    className="bl-text-[40px]/[60px] md:bl-text-[54px]/[80px] md:bl-font-bold"
                  >
                    <span>BTC</span>
                  </Title>
                  <Title
                    variant="default"
                    className="bl-text-[40px]/[60px] md:bl-text-[54px]/[80px]"
                  >
                    Fi
                  </Title>
                </div>
                <div className="bl-border-r bl-block bl-border-white bl-h-5 md:bl-h-9" />
                <div
                  className="md:bl-flex bl-text-nowrap bl-text-base md:bl-text-2xl bl-uppercase bl-font-bold bl-text-secondary"
                  style={{ fontFamily: 'Tomkin Narrow' }}
                >
                  <Trans i18nKey={'navigation.links.btcfiDesc'} />
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </AnimatePageLayout>
  );
}
