import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn, truncateTo8Decimals, formatNumber, toPercent } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import BTCIcon from '@/components/icons/coins/BTCIcon';
import { StakeDialog } from '@/modules/btcfiyield/components/stake';
import { RedeemDialog } from '@/modules/btcfiyield/components/redeem';
import Decimal from 'decimal.js';
import Approximately from '@/modules/btcfiyield/icons/approximately';
const pageKey = 'pages.btcfi.yield';
import { useReadContract } from 'wagmi';
import { btcfi_address } from '@/modules/btcfiyield/const';
import { btcfiAbi } from '@/modules/btcfiyield/abi';
import { Card } from '@/modules/btcfiyield/components/card';
import NavArrow from '@/modules/btcfiyield/icons/nav-arrow';
import { useClaimPoints, useGetApr } from '@/modules/btcfiyield/hooks/btcfi';
import { BtcDialog } from '@/modules/mining-gala/components/btc-dialog';
import { Loading } from '@/modules/btcfi/components/Loading';
import { BtcfiWhiteIcon, BtcfiBlackIcon } from '@/modules/btcfiyield/icons/btcfi';

import {
  RuleDataResponse,
  UserDataResponse,
  TvlResponse,
  AprResponse,
  BtcFiInfoResponse,
} from '@/modules/btcfiyield/types';

type YeildProps = {
  tvlData?: TvlResponse;
  ruleData?: RuleDataResponse;
  userData?: UserDataResponse;
  address?: string;
  aprData?: AprResponse;
  verifySignIn: () => boolean;
  isSigned: boolean;
  handleClickConnect: () => void;
  token?: string;
  btcfiInfo?: BtcFiInfoResponse;
  stakeOpen: boolean;
  redeemOpen: boolean;
  setStakeOpen: (_arg0: boolean) => void;
  setRedeemOpen: (_arg0: boolean) => void;
  setBitfiInfo: (_arg0: { btc: string; balance: string }) => void;
};

export const BtcfiYield = ({
  address,
  verifySignIn,
  isSigned,
  handleClickConnect,
  token = '',
  btcfiInfo,
  stakeOpen,
  setStakeOpen,
  setRedeemOpen,
  redeemOpen,
  setBitfiInfo,
}: YeildProps) => {
  const [detailOpen, setDetailOpen] = useState(false);
  const { t } = useTranslation();

  const [btcNum, setBtcNum] = useState('0');
  const { mutateAsync: claimPoints } = useClaimPoints();
  const { data: aprData } = useGetApr();
  const [loading, setLoading] = useState(false);
  const [noBalance, setNoBalance] = useState(false);

  const disabled = loading || Number(btcfiInfo?.user.noWithdrawReward) === 0;

  const handleDialog = (type: 'stake' | 'redeem') => {
    verifySignIn();
    if (isSigned) {
      if (type === 'stake') setStakeOpen(true);
      if (type === 'redeem') setRedeemOpen(true);
    }
  };

  const handleClaim = async () => {
    verifySignIn();
    if (isSigned && !disabled) {
      setLoading(true);
      await claimPoints();
      // setNoBalance(result.isNotBalance);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!token) {
      setStakeOpen(false);
      setRedeemOpen(false);
      setBtcNum('');
    }
  }, [token]);

  const { data: _bfbtcAmount, refetch: getBfbtc } = useReadContract({
    abi: btcfiAbi,
    address: btcfi_address,
    functionName: 'balanceOf',
    args: [address],
  });

  const { data: ratio } = useReadContract({
    address: btcfi_address,
    abi: btcfiAbi,
    functionName: 'currentRatio',
    args: [],
  }) as { data: bigint | undefined };

  const bfbtcAmount = useMemo(() => {
    const res = _bfbtcAmount
      ? new Decimal(_bfbtcAmount.toString()).div(Decimal.pow(10, 8)).toString()
      : 0;
    return res;
  }, [_bfbtcAmount]);

  const getBtcAmount = async () => {
    if (!_bfbtcAmount || !ratio) {
      setBtcNum('0');
      return;
    }
    const bfbtcRes = _bfbtcAmount
      ? new Decimal(_bfbtcAmount.toString())
          .add(new Decimal(btcfiInfo?.user.depositCount || 0))
          .toString()
      : 0;
    const _btcAmount = ratio
      ? new Decimal(bfbtcRes.toString()).div(new Decimal(ratio.toString())).toString()
      : '0';
    
    setBtcNum(_btcAmount);
  };

  useEffect(() => {
    getBtcAmount();
  }, [_bfbtcAmount, address, token, ratio, btcfiInfo]);

  const detailInfo = [
    {
      title: t(`${pageKey}.yieldsFrom`),
      value: (
        <a
          href="https://app.bitfi.one/bfbtc/stake"
          className="bl-underline bl-cursor-pointer"
          target="_blank"
          rel="noreferrer"
        >
          Bitfi&apos;s BFBTC Pool
        </a>
      ),
    },
    {
      title: t(`${pageKey}.supportTokens`),
      value: (
        <div className="bl-flex-center bl-gap-1">
          <BTCIcon className="bl-size-6 bl-rounded-full" />
          BTC
        </div>
      ),
    },
    {
      title: t(`${pageKey}.rewardTokens`),
      value: 'BTC',
    },
    {
      title: t(`${pageKey}.yieldsType`),
      value: 'Cedefi',
    },
  ];

  const handleStake = () => {
    handleDialog('stake');
  };

  useEffect(() => {
    setBitfiInfo({
      btc: btcNum,
      balance: String(bfbtcAmount),
    });
  }, [bfbtcAmount, btcNum]);

  return (
    <div className="bl-mb-15">
      <Card>
        <div
          id="bitfi-section"
          className="bl-relative bl-flex md:bl-mx-auto bl-flex-col bl-items-center md:bl-max-w-[747px] bl-pt-15 md:bl-pb-3 bl-font-['Tomkin_Narrow'] bl-font-normal md:bl-font-bold bl-border-0 md:bl-border bl-border-[#A9A9A9] bl-rounded-md bl-z-[1] before:bl-content-none md:before:bl-content-[''] before:bl-absolute before:-bl-z-[2] before:bl-left-[-1px] before:bl-bottom-[-1px] before:bl-w-10 before:bl-h-10 before:bl-bg-white before:bl-bg-[url('/images/btcfi/angle-black.svg')] before:bl-bg-[length:100%] before:bl-rotate-180 after:bl-content-none md:after:bl-content-[''] after:bl-absolute after:-bl-z-[2] after:bl-right-[-1px] after:bl-top-[-1px] after:bl-w-10 after:bl-h-10 after:bl-bg-white after:bl-bg-[url('/images/btcfi/angle-black.svg')] after:bl-bg-[length:100%]"
        >
          <div className="bl-text-center">
            {!address && (
              <button
                className="bl-flex bl-items-center bl-justify-center bl-gap-2 bl-w-[235px] bl-h-[50px] bl-my-[23px] bl-bg-[#E36E1B] bl-rounded-sm bl-text-black bl-text-2xl bl-border bl-border-black"
                onClick={handleClickConnect}
              >
                {t('common.connect')}
              </button>
            )}
          </div>
          <div className="bl-w-[91px] md:bl-w-[84px] bl-font-medium bl-h-[30px] md:bl-h-9 bl-rounded-tl-sm bl-text-lg md:bl-text-xl bl-rounded-br-sm bl-text-white  bl-bg-black bl-absolute bl-left-0 bl-top-0 bl-gap-1 bl-flex-center">
            <BtcfiWhiteIcon />
            <span>Bitfi</span>
          </div>

          <div className="bl-flex bl-flex-col bl-text-center md:bl-text-left md:bl-flex-row bl-items-center bl-justify-center bl-w-full">
            {/* APY */}
            <div className="md:bl-w-1/2 md:bl-pl-[68px] bl-text-black bl-font-normal">
              <div className="bl-text-lg bl-pb-2 bl-flex bl-justify-center md:bl-justify-start bl-gap-1 bl-items-center">
                <span>{t(`${pageKey}.balance`)}:</span>
                <BtcfiBlackIcon className="bl-size-5" />
                <span className="bl-font-bold">
                  {isSigned ? truncateTo8Decimals(bfbtcAmount) : '--'}
                </span>
                <span className="bl-font-light"> BFBTC</span>
              </div>
              <div className="bl-flex bl-justify-start bl-gap-2 bl-pb-2 md:bl-pb-0 bl-items-center bl-text-black bl-text-[30px] md:bl-text-[24px] bl-font-bold">
                <Approximately className="bl-size-4" />
                <BTCIcon className="bl-size-9" />
                {isSigned ? formatNumber(btcNum) : '--'}
                <span className="bl-font-light bl-text-[30px] md:bl-text-[24px]">BTC</span>
              </div>
            </div>
            <div className="bl-w-[1px] bl-hidden md:bl-block bl-h-6 md:bl-h-12 bl-bg-secondary bl-shrink-0 bl-mt-3.5 md:bl-mt-2.5" />
            {/* Reward */}
            <div className="md:bl-w-1/2 bl-relative bl-flex md:bl-block bl-items-center bl-justify-center md:bl-pl-15">
              <div className="md:bl-text-lg bl-text-base bl-text-black bl-font-normal md:bl-pb-2">
                <span>{t(`${pageKey}.apy`)}</span>
                <span className="bl-inline md:bl-hidden bl-pr-2">:</span>
              </div>
              <div className="md:bl-text-[24px] bl-flex bl-items-center bl-gap-1 bl-text-base bl-text-primary">
                <span>
                  {Number(aprData?.btcApr.min) * 100}%~
                  {(Number(aprData?.btcApr.max) * 100).toFixed(0)}%{' '}
                </span>
              </div>
              <div className="bl-text-xs bl-absolute bl-top-6 md:bl-font-semilight md:bl-top-18">
                Yield may vary depending on network conditions
              </div>
            </div>
          </div>
          <div
            className={cn(
              'bl-w-full bl-hidden md:bl-pt-12 md:bl-pl-14 md:bl-flex bl-justify-start bl-items-center bl-text-sm md:bl-max-w-[747px]',
            )}
          >
            <span className="bl-text-secondary">{t(`${pageKey}.btcfi.reward`)}</span>
            <img className="bl-size-6 bl-ml-3" src="/images/btcfi/btr-token.png" alt="" />
            <span className="bl-text-black">
              &nbsp; {isSigned ? btcfiInfo?.user.noWithdrawReward || 0 : '--'}
            </span>
          </div>
          <div className="bl-flex bl-justify-center bl-mt-15 md:bl-mt-0 bl-gap-4 md:bl-gap-0 bl-scale-95 md:bl-scale-[100%]">
            <Button
              variant="outline-5"
              className={cn(
                'md:bl-scale-[85%] md:bl-flex-center bl-hidden bl-group bl-w-[138px] bl-h-10 md:bl-w-[236px] md:bl-h-[51px] bl-text-lg/none md:bl-text-2xl/5 bl-font-normal md:bl-font-bold',
              )}
              onClick={handleClaim}
              disabled={Number(btcfiInfo?.user.noWithdrawReward) === 0 || !isSigned}
              noOverlay
            >
              {loading ? (
                <Loading />
              ) : (
                <span
                  className={cn('bl-font-normal group-hover:bl-text-primary', {
                    'group-hover:bl-text-black':
                      Number(btcfiInfo?.user.noWithdrawReward) === 0 || !isSigned,
                  })}
                >
                  {t(`${pageKey}.btcfi.claim`)}
                </span>
              )}
            </Button>
            <Button
              variant="outline-5"
              className="md:bl-scale-[85%] md:-bl-mx-4 bl-flex-center bl-group bl-w-[138px] bl-h-10 md:bl-w-[236px] md:bl-h-[51px] bl-text-lg/none md:bl-text-2xl/5 bl-font-normal md:bl-font-bold hover:bl-text-white md:hover:bl-text-black"
              onClick={() => handleDialog('redeem')}
              noOverlay
            >
              <span className="bl-font-normal group-hover:bl-text-primary">
                {t(`${pageKey}.redeem`)}
              </span>
            </Button>
            <div className="bl-relative">
              <Button
                variant="outline-5"
                keepOverlay
                className="md:bl-scale-[85%] bl-flex-center bl-w-[138px] bl-h-10 md:bl-w-[236px] md:bl-h-[51px] bl-text-lg/none md:bl-text-2xl/5 bl-font-normal md:bl-font-bold hover:bl-text-white"
                onClick={handleStake}
              >
                <span className="bl-font-normal">{t(`${pageKey}.btcfi.stake`)}</span>
              </Button>
              {/* <button
                onClick={handleTask}
                className="bl-absolute hover:bl-bg-primary hover:bl-text-white hover:bl-border-white bl-cursor-pointer bl-z-10 bl-border bl-border-primary bl-top-[-16px] bl-right-[-60px] md:bl-right-[-130px] bl-w-[95px] md:bl-w-[162px] bl-h-8 bl-flex-center bl-text-xs bl-text-white bl-bg-black"
              >
                10 BTR bonus<span className="bl-hidden md:bl-inline">,&nbsp;timelimited</span>
              </button> */}
            </div>
          </div>

          <div className="bl-text-sm bl-pt-2 bl-text-[#00000066] bl-hidden md:bl-block">
            {t(`${pageKey}.btcfi.service`)}
          </div>
        </div>

        <div className="bl-mt-10 md:bl-mt-1 md:bl-px-0 bl-font-['Tomkin_Narrow'] bl-font-bold bl-text-center">
          <div className="bl-flex bl-h-[42px] bl-mx-6 bl-font-normal bl-mt-4 bl-border-y-[0.5px] md:bl-hidden bl-justify-between bl-items-center">
            <div className="bl-text-[13px]">{t(`${pageKey}.btcfi.reward`)}</div>
            <div className="bl-flex-center bl-gap-1">
              <img className="bl-size-4 bl-ml-3" src="/images/btcfi/btr-token.png" alt="" />
              <span className="bl-text-sm bl-text-black">
                {isSigned ? btcfiInfo?.user.noWithdrawReward || 0 : '--'}
              </span>
              <button
                onClick={handleClaim}
                className="bl-w-[47px] bl-ml-1 bl-rounded-sm bl-h-[19px] bl-bg-primary disabled:bl-bg-secondary bl-border bl-flex-center bl-text-[10px] bl-text-black bl-border-black"
                disabled={Number(btcfiInfo?.user.noWithdrawReward) === 0 || !isSigned}
              >
                {loading ? (
                  <Loading />
                ) : (
                  <span className="bl-font-normal">{t(`${pageKey}.claim`)}</span>
                )}
              </button>
            </div>
          </div>
          <div
            className="bl-mt-4 md:bl-mt-[15px] bl-text-[#5B5B5C] bl-text-xs md:bl-text-base bl-font-[350] md:bl-font-bold [&>a]:bl-underline"
            dangerouslySetInnerHTML={{
              __html: t(`${pageKey}.btcfi.accept`),
            }}
          ></div>
          <button
            className="bl-flex-center bl-group md:bl-pt-4 bl-gap-1 bl-mx-auto bl-my-6 md:bl-mt-0 bl-text-sm bl-text-black bl-cursor-pointer"
            onClick={() => setDetailOpen(!detailOpen)}
          >
            <span className="bl-font-normal">{t(`${pageKey}.details`)}</span>
            <NavArrow
              className={cn('bl-size-4 group-hover:bl-text-primary bl-text-black bl-duration-200', {
                'bl-rotate-180': detailOpen,
              })}
            />
          </button>
        </div>
        {/* detail */}
        {detailOpen && (
          <div className="bl-relative bl-max-w-[747px] bl-mx-5 md:bl-mx-auto bl-py-4 md:bl-p-7.5 md:bl-px-12 md:bl-py-8 bl-mt-5 md:bl-mt-6 bl-font-['Tomkin_Narrow'] bl-text-sm md:bl-text-base md:bl-font-bold bl-tracking-[0.08em] bl-text-black bl-border-0 md:bl-border bl-border-[#A9A9A9] bl-rounded-md bl-z-[1] before:bl-content-none md:before:bl-content-[''] before:bl-absolute before:-bl-z-[2] before:bl-left-[-1px] before:bl-bottom-[-1px] before:bl-w-10 before:bl-h-10 before:bl-bg-white before:bl-bg-[url('/images/btcfi/angle-flat.svg')] before:bl-bg-[length:100%] before:bl-rotate-180 after:bl-content-none md:after:bl-content-[''] after:bl-absolute after:-bl-z-[2] after:bl-right-[-1px] after:bl-top-[-1px] after:bl-w-10 after:bl-h-10 after:bl-bg-white after:bl-bg-[url('/images/btcfi/angle-flat.svg')] after:bl-bg-[length:100%]">
            {detailInfo.map((item) => (
              <div
                key={item.title}
                className="bl-flex bl-justify-between bl-items-center bl-h-6 bl-mb-3"
              >
                <span className="bl-text-[#00000099]">{item.title}</span>
                <span className="bl-font-medium md:bl-font-bold">{item.value}</span>
              </div>
            ))}
            <div className="bl-w-full bl-h-[1px] bl-my-6 md:bl-my-7 bl-bg-[#CECECE]"></div>
            <div className="bl-h-6 bl-text-[#393D44] bl-font-bold">Yields Source:</div>
            <div className="bl-leading-6 bl-text-[#393D44] bl-my-4">
              <span className="bl-text-[#00000099] ">{t(`${pageKey}.btcfi.finalYield`)}</span>
              <span className="bl-font-bold">
                {toPercent(Number(aprData?.btcApr.min))} - {toPercent(Number(aprData?.btcApr.max))}
              </span>
            </div>
            <div
              className="bl-relative bl-pl-5 bl-font-medium md:bl-font-bold before:bl-absolute before:bl-left-1.5 md:before:bl-left-[10px] before:bl-top-2 md:before:bl-top-[10px] before:bl-w-[3px] before:bl-h-[3px] before:bl-bg-black"
              dangerouslySetInnerHTML={{
                __html: t(`${pageKey}.btcfi.yieldDesc1`, {
                  percent: `${toPercent(Number(aprData?.btcApr.min))}~${toPercent(Number(aprData?.btcApr.max))}`,
                }),
              }}
            ></div>
            {/* <div
              className="bl-relative bl-pl-5 bl-font-medium md:bl-font-bold bl-my-4 before:bl-absolute before:bl-left-1.5 md:before:bl-left-[10px] before:bl-top-2 md:before:bl-top-[10px] before:bl-w-[3px] before:bl-h-[3px] before:bl-bg-black"
              dangerouslySetInnerHTML={{
                __html: t(`${pageKey}.btcfi.yieldDesc2`, {
                  percent: toPercent(aprData?.btrApr),
                }),
              }}
            ></div> */}
          </div>
        )}
        <div className="bl-hidden md:bl-block bl-max-w-[854px] bl-mx-auto md:bl-mt-15 bl-my-6 bl-border-[0.5px] bl-border-black" />

        {isSigned && (
          <StakeDialog
            open={stakeOpen}
            onOpenChange={setStakeOpen}
            aprData={aprData}
            refetch={getBfbtc}
          />
        )}
        {isSigned && (
          <RedeemDialog
            open={redeemOpen}
            token={token}
            onOpenChange={setRedeemOpen}
            address={address}
          />
        )}
      </Card>
      <div className="md:bl-pb-5 bl-text-sm bl-text-[#00000066] bl-bg-white bl-text-center bl-font-['Tomkin_Narrow'] bl-font-[350] md:bl-hidden">
        {t(`${pageKey}.service`)}
      </div>
      <BtcDialog open={noBalance} flashOnly onOpenChange={setNoBalance} />
    </div>
  );
};
