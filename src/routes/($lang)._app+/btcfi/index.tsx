import { UserContent } from '@/modules/btcfi/components/banner-user';
import Banner from './section.banner';
import { json, LoaderFunctionArgs, MetaFunction } from '@remix-run/cloudflare';
import { commitSession, getSession } from '@/modules/session';
import i18nServer from '@/modules/i18n.server';
import { createAPIRquest } from '@/modules/btcfi/api.server';
import { createAPIClient as createUserApi } from '@/modules/user-center/api.server';
import { createAPIRquest as createYield } from '@/modules/btcfiyield/api.server';
import { useLoaderData, useRevalidator } from '@remix-run/react';
import { useCallback, useState, useEffect, useMemo } from 'react';
import { useAccount } from '@/hooks/wallet/account';
import { chain, chain as BitlayerNetwork } from '@/modules/user-center/config';
import { LoginProvider, useLoginContext } from '@/modules/user-center/components/login';
import useChange from '@react-hook/change';
import { ConnectWalletDialog, SwitchChainDialog } from '@/components/featured/wallet';
import { useTranslation } from 'react-i18next';
import { useDialog } from '@/hooks/dialog';
import { useMediaQuery } from '@react-hook/media-query';
import { useScroll } from '@use-gesture/react';
import ky from 'ky';
import { ClientOnly } from 'remix-utils/client-only';
import { Title } from '@/components/ui/title';
import { CheckinSuccessDialog } from '@/modules/user-center/components/checkin-success-dialog.tsx';
import { Task } from './section.task';
import { BtcFiTaskResponse, TaskItemProps } from '@/modules/btcfi/types';
import { pool_address } from '@/modules/btcfiyield/const';
import { Desyn } from './section.desyn';
import { BtcfiYield } from './section.yield';
import { ErrorBoundary } from 'react-error-boundary';
import { Assets } from './section.assets.tsx';

import {
  RuleDataResponse,
  UserDataResponse,
  ClaimResponse,
  RedeemParamResponse,
  AllowanceResponse,
  ApproveResponse,
  ApplyRedeemResponse,
  TvlResponse,
  BtcFiInfoResponse,
  AssetResponse,
} from '@/modules/btcfiyield/types';

interface LoaderResponse {
  title: string;
  address?: string;
  btr: number;
  error?: string;
  isOver?: boolean;
  ruleData?: RuleDataResponse;
  userData?: UserDataResponse;
  ClaimParam?: ClaimResponse;
  RedeemData?: ApplyRedeemResponse;
  RedeemParam?: RedeemParamResponse;
  Allowance?: AllowanceResponse;
  Approve?: ApproveResponse;
  dailyInfo: TaskItemProps;
  taskinfo: BtcFiTaskResponse;
  tvlData?: TvlResponse;
  token?: string;
  points: number;
  btcfiInfo?: BtcFiInfoResponse;
  assetsInfo?: AssetResponse;
  gems: number;
}

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  const token = session.get('user.token') as string | undefined;
  const address = session.get('user.address') as string | undefined;
  const t = await i18nServer.getFixedT(request);
  const api = await createAPIRquest(request);
  const userApi = await createUserApi(token);
  const yieldApi = await createYield(request);

  const title = t('navigation.links.btcfi');
  const { data: ruleData } = await yieldApi.getRuleData();
  const { data: dailyInfo } = await api.getDailyTaskList();
  const { data: taskinfo } = await api.getTaskList();
  const { data: tvlData } = await yieldApi.getTvl({ pool_address });

  if (!token) {
    return json<LoaderResponse>({
      title,
      address: '0x',
      btr: 0,
      ruleData,
      dailyInfo: dailyInfo.taskInfo,
      taskinfo,
      tvlData,
      points: 0,
      gems: 0,
      assetsInfo: {
        list: [],
      },
    });
  }

  try {
    const { data: btr } = await api.getUserBtr();
    const { data: userData } = await yieldApi.getUserData({
      pool_address,
    });
    const { data: btcfiInfo } = await yieldApi.getBtcfiInfo();
    const { data: userInfo } = await userApi.getUserInfo();
    const { data: assetInfo } = await yieldApi.getAsset();
    const error = session.get('error') as string | undefined;

    return json<LoaderResponse>(
      {
        title,
        address,
        btr: btr.btr,
        error,
        ruleData,
        userData,
        dailyInfo: dailyInfo.taskInfo,
        taskinfo,
        tvlData,
        token,
        points: userInfo.totalPoints,
        gems: userInfo.gemsPoints,
        btcfiInfo,
        assetsInfo: assetInfo,
      },
      {
        headers: {
          'Set-Cookie': await commitSession(session),
        },
      },
    );
  } catch (error) {
    throw new Error('Failed to load data from server');
  }
}

export const meta: MetaFunction<typeof loader> = () => {
  return [
    { title: 'Premier Bitcoin Yield Hub | Bitlayer' },
    {
      property: 'twitter:card',
      content: 'summary_large_image',
    },
    {
      property: 'twitter:site',
      content: '@BitlayerLabs',
    },
    {
      property: 'twitter:creator',
      content: '@BitlayerLabs',
    },
  ];
};

export function MainContent() {
  const { address, active, chainId } = useAccount({ network: chain.networkType });
  const loaderData = useLoaderData<typeof loader>();
  // const { btr } = loaderData;
  // const isMobile = useMediaQuery('(max-width: 640px)');

  const { login } = useLoginContext();
  const revalidator = useRevalidator();
  const isRightChain = useMemo(() => chainId === chain.chain.id, [chainId]);
  const isSigned = useMemo(() => address === loaderData.address, [address, loaderData.address]);
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [points, setPoints] = useState(0);
  const [stakeOpen, setStakeOpen] = useState(false);
  const [redeemOpen, setRedeemOpen] = useState(false);
  const [bitfiInfo, setBitfiInfo] = useState({
    btc: '0',
    balance: '0',
  });

  const [desynInfo, setDesynInfo] = useState({
    btc: '0',
    balance: '0',
  });

  const dailyInfo = loaderData.dailyInfo;
  const isTxPending = dailyInfo?.extraData?.is_tx_pending || false;
  const currentDay = dailyInfo?.extraData.cur_done_progress;

  const isFull = Number(loaderData?.tvlData?.tvlSwitchV2) === 1;

  useEffect(() => {
    const currentData = dailyInfo.action.payload?.progress_cfg?.filter(
      (item) => item.key === currentDay,
    );
    const _points = currentData?.[0]?.value || 0;
    setPoints(_points);
    if (isTxPending) {
      setTimeout(() => {
        revalidator.revalidate();
      }, 2000);
    }
  }, [loaderData.dailyInfo]);

  useChange(dailyInfo?.extraData?.is_tx_pending, (current, prev) => {
    if (!current && prev && dailyInfo?.isCompleted) {
      setOpen(true);
      revalidator.revalidate();
    }
  });

  const verifySignIn = (): boolean => {
    if (!address) {
      handleClickConnect();
      return false;
    }
    if (!isSigned) {
      handleLogin();
      return false;
    }
    return true;
  };

  const handleClickConnect = () => {
    openDialog({
      content: ({ close }) => (
        <ConnectWalletDialog
          title={t('common.connect')}
          description={t('common.connectDesc')}
          chain={BitlayerNetwork}
          close={close}
        />
      ),
    });
  };

  const handleDialog = (type: 'stake' | 'redeem') => {
    verifySignIn();
    if (isSigned) {
      if (type === 'stake') setStakeOpen(true);
      if (type === 'redeem') setRedeemOpen(true);
    }
  };

  /* 隔离区 End */

  const { open: openDialog, close: closeDialog } = useDialog();

  const [hidden, setHidden] = useState(false);

  const setHiddenByPin = useCallback((state: boolean) => {
    setHidden(state);
  }, []);

  useScroll(
    (state) => {
      const [, offsetY] = state.offset;
      if (offsetY === 0) {
        setHiddenByPin(false);
        return;
      }
      if (offsetY < 0) {
        return;
      }
      const [, directionY] = state.direction;
      if (directionY === 1) {
        setHiddenByPin(true);
      } else {
        setHiddenByPin(false);
      }
    },
    { target: window },
  );

  const handleLogout = async () => {
    await ky.get(`/logout`);
    revalidator.revalidate();
    return;
  };

  const handleLogin = useCallback(async () => {
    if (isSigned || !address) {
      return;
    }
    setTimeout(() => {
      login(address, {
        onSuccess: () => {
          revalidator.revalidate();
        },
      });
    }, 1000);
  }, [isSigned, address]);

  useChange(chainId, (cur, prev) => {
    if (cur && prev && cur !== prev) {
      handleLogin();
    }
  });

  useChange(isSigned, (current, prev) => {
    if (prev && !current) {
      handleLogout();
    }
  });

  useEffect(() => {
    if (active) {
      if (!isRightChain) {
        openDialog({
          content: ({ close }) => <SwitchChainDialog chain={BitlayerNetwork} close={close} />,
          showCloseButton: false,
          outsideClickClose: false,
        });
      } else {
        closeDialog();
      }
    } else {
      closeDialog();
    }
  }, [active, isRightChain]);

  useEffect(() => {
    address && handleLogin();
  }, [address]);

  useEffect(() => {
    const ele = document?.getElementById('btcfi-title');
    const rect = ele?.getBoundingClientRect();
    if (rect) {
      window.scrollTo({
        top: rect.top - 20,
        behavior: 'smooth',
      });
    }
  }, []);

  return (
    <div className="bl-mb-[150px] bl-min-h-screen bl-font-btcfi md:bl-pt-[70px] bl-pt-20">
      <div className="bl-bg-white">
        <Banner />
        <Assets
          assetList={loaderData?.assetsInfo?.list || []}
          btr={loaderData.btr}
          gems={loaderData.gems}
          points={loaderData.points}
          bitfiInfo={bitfiInfo}
          desynInfo={desynInfo}
          btcfiInfo={loaderData?.btcfiInfo}
          isSigned={isSigned}
        />
        <div className="bl-relative bl-hidden md:bl-block md:bl-w-full bl-bg-white bl-px-4 bl-py-6 md:bl-py-0 md:bl-mt-2 md:bl-p-0 bl-mx-auto bl-mb-[10px] bl-z-10 bl-border md:bl-border-0 bl-border-[#A9A9A9] bl-rounded-md before:bl-content-[''] md:before:bl-content-none before:bl-absolute before:-bl-z-[2] before:bl-left-[-1px] before:bl-bottom-[-1px] before:bl-w-7 before:bl-h-7 before:bl-bg-white before:bl-bg-[url('/images/btcfi/angle-black-m.svg')] before:bl-bg-[length:100%] after:bl-content-[''] md:after:bl-content-none after:bl-absolute after:-bl-z-[2] after:bl-right-[-1px] after:bl-top-[-1px] after:bl-w-7 after:bl-h-7 after:bl-bg-black after:bl-bg-[url('/images/btcfi/angle-white-m.svg')] after:bl-bg-[length:100%]">
          <div className="bl-flex-center ">
            <Title variant="dark" className="bl-text-[40px] md:bl-text-[80px] md:bl-font-bold">
              <div className="bl-h-100">BTC</div>
            </Title>
            <Title variant="default" className="bl-text-[40px] md:bl-text-[80px] md:bl-font-bold">
              Yield
            </Title>
          </div>
        </div>
        <ErrorBoundary fallback={<p></p>}>
          <BtcfiYield
            tvlData={loaderData?.tvlData}
            address={address}
            verifySignIn={verifySignIn}
            isSigned={isSigned}
            userData={loaderData?.userData}
            ruleData={loaderData?.ruleData}
            handleClickConnect={handleClickConnect}
            token={loaderData?.token}
            btcfiInfo={loaderData?.btcfiInfo}
            stakeOpen={stakeOpen}
            setStakeOpen={setStakeOpen}
            redeemOpen={redeemOpen}
            setRedeemOpen={setRedeemOpen}
            setBitfiInfo={setBitfiInfo}
          />
        </ErrorBoundary>

        <ErrorBoundary fallback={<p></p>}>
          <Desyn
            tvlData={loaderData?.tvlData}
            address={address}
            verifySignIn={verifySignIn}
            isSigned={isSigned}
            userData={loaderData?.userData}
            ruleData={loaderData?.ruleData}
            handleClickConnect={handleClickConnect}
            setDesynInfo={setDesynInfo}
          />
        </ErrorBoundary>

        <ErrorBoundary fallback={<p></p>}>
          <Task
            taskList={loaderData.taskinfo.btcfiTasks}
            dailyInfo={loaderData.dailyInfo}
            onAction={verifySignIn}
            isFull={isFull}
            handleStake={() => handleDialog('stake')}
          />
        </ErrorBoundary>
      </div>

      <CheckinSuccessDialog open={open} onOpenChange={setOpen} points={points} unit="BTR" />
    </div>
  );
}

export default function BtcFiPage() {
  const loaderData = useLoaderData<typeof loader>();
  const { address } = useAccount({ network: chain.networkType });

  return (
    <LoginProvider walletAddress={address} sessionAddress={loaderData.address}>
      <ClientOnly>{() => <MainContent />}</ClientOnly>
    </LoginProvider>
  );
}
export function links() {
  return [
    {
      rel: 'preload',
      href: '/images/btcfi/bg-border-left-m.png',
      as: 'image',
    },
    {
      rel: 'preload',
      href: '/images/btcfi/bg-border-black.svg',
      as: 'image',
      type: 'image/svg+xml',
      media: '(min-width: 768px)',
    },
    {
      rel: 'preload',
      href: '/images/btcfi/bg-border-black-197-m.png',
      as: 'image',
    },
    {
      rel: 'preload',
      href: '/images/btcfi/bg-border-black-197.svg',
      as: 'image',
      type: 'image/svg+xml',
      media: '(min-width: 768px)',
    },
  ];
}
