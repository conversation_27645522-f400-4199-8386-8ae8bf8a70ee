import { shortAddress } from '@/components/featured/wallet';
import { useAccount } from '@/hooks/wallet/account';
import { chain } from '@/modules/user-center/config';
import { formatNumberWithCommas, formatNumber, cn } from '@/lib/utils';
import { useState, useEffect, useRef } from 'react';
import { useMediaQuery } from '@react-hook/media-query';

import FoldIcon from '@/modules/btcfi/icons/fold';
import { BtcfiBlackIcon } from '@/modules/btcfiyield/icons/btcfi';
import { DesynIcon } from '@/modules/btcfiyield/icons/desyn';
import { LineChart } from '@/modules/btcfi/components/LineCharts';
import { ChartsItem, BtcFiInfoResponse } from '@/modules/btcfiyield/types';
import dayjs from 'dayjs';
import utc from "dayjs/plugin/utc"
dayjs.extend(utc)

import AboutIcon from '@/modules/btcfiyield/icons/about';

type AssetsProps = {
  assetList: ChartsItem[];
  btr: number;
  gems: number;
  points: number;
  isSigned: boolean;
  bitfiInfo: {
    btc: string;
    balance: string;
  };
  desynInfo: {
    btc: string;
    balance: string;
  };
  btcfiInfo?: BtcFiInfoResponse;
};

export const Assets = (props: AssetsProps) => {
  return <AssetDesktop {...props} />;
};

const Split = () => {
  return (
    <div className="md:bl-inline-block bl-hidden bl-w-[1px] bl-h-5 bl-bg-secondary bl-mx-3"></div>
  );
};

const CountNum = ({ type, count }: { type: string; count: number }) => {
  return (
    <div className="bl-text-xs md:bl-text-base">
      <span className="bl-text-primary">{type}</span>
      <span className="bl-text-black">
        :<span className="bl-pl-1"></span>
        {formatNumberWithCommas(count)}
      </span>
    </div>
  );
};

const onToSection = (id: string) => {
  const targetElement = document.getElementById(id);
  const targetPosition = targetElement?.getBoundingClientRect();

  if (targetPosition) {
    window.scrollTo({
      top: targetPosition.top + window.scrollY - 20,
      left: targetPosition.left + window.scrollX,
      behavior: 'smooth',
    });
  }
};

const AssetDesktop = ({
  btcfiInfo,
  gems,
  points,
  btr,
  bitfiInfo,
  desynInfo,
  assetList,
  isSigned,
}: AssetsProps) => {
  const { address } = useAccount({ network: chain.networkType });
  const [isFlod, setIsFlod] = useState(false);
  const [windowWidth, setWindowWidth] = useState(0);
  const isMobile = useMediaQuery('(max-width: 640px)');
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    if (elementRef.current) {
      const elementWidth = elementRef.current.offsetWidth - 68;
      setWindowWidth(elementWidth);
    }
  }, []);

  const btcData = (() => {
    const today = dayjs.utc();
    const data =
      assetList?.map((item) => ({
        title: dayjs.utc(item.timestamp * 1000).format('MM.DD'),
        value: Number(item.balance) / 1e18,
      }))
    const last7Data = data.slice(-7);
    const emptyData = Array.from({ length: 7 }, (_, index) => ({
        title: today.subtract(7 - index - 1, 'day').format('MM.DD'),
        value: 0,
        isEmpty: true,
    })).slice(0, 7 - last7Data.length);
    return [...emptyData, ...last7Data];
  })();

  return (
    <div className="bl-flex-center bl-mb-[35px] md:bl-mb-8 bl-container bl-font-body bl-w-full bl-pt-4 md:bl-pt-10">
      <div
        ref={elementRef}
        className="md:bl-w-[700px] bl-w-full bl-flex bl-flex-col bl-gap-2 bl-border bl-rounded-[6px] bl-p-4 md:bl-p-[20px] bl-border-[#A9A9A9] bl-mx-0 bl-my-auto"
        style={{
          background: `linear-gradient(296deg, rgba(255, 255, 255, 0.00) 86.13%, rgba(227, 110, 27, 0.20) 97.64%), rgba(227, 110, 27, 0.05)`,
        }}
      >
        {address && (
          <div className="bl-bg-[#9E9E9E] bl-flex-center bl-font-semilight bl-w-[95px] bl-text-white">
            {shortAddress(address)}
          </div>
        )}
        <div className="bl-border bl-bg-white bl-border-[#A9A9A9] bl-px-4 md:bl-px-[22px] bl-flex bl-h-[67px] md:bl-h-[51px] bl-rounded-[6px]">
          <div className="bl-flex bl-flex-col bl-w-full bl-gap-1 md:bl-gap-0 md:bl-w-auto md:bl-flex-row bl-items-start md:bl-items-center bl-justify-center">
            <span className="bl-text-black bl-text-xl md:bl-text-lg">Racer Center Asset</span>
            <Split />
            <div className="bl-flex bl-justify-between bl-w-full md:bl-w-auto bl-gap-5">
              <CountNum type="BTR" count={btr} />
              <CountNum type="Points" count={points} />
              {!!gems && <CountNum type="Gems" count={gems} />}
            </div>
          </div>
        </div>
        <div
          className={cn(
            'bl-border bl-bg-white bl-border-[#A9A9A9] bl-px-4 md:bl-px-[22px] bl-rounded-[6px]',
            {
              'bl-pb-4': isFlod,
            },
          )}
        >
          <div className="bl-justify-between bl-bg-white bl-flex bl-items-center bl-h-[70px] md:bl-h-[62px]">
            <div className="bl-flex bl-flex-col md:bl-flex-row bl-items-start md:bl-items-center">
              <span className="bl-text-black bl-text-xl md:bl-text-lg">BTCFi Asset</span>
              <Split />
              <div className="bl-flex bl-font-semilight bl-items-center">
                <span className="bl-text-black bl-pr-1">Total Value</span>
                <AboutIcon className="bl-mx-1" />
                <span className="bl-text-black bl-font-[500]">
                  {formatNumber(Number(bitfiInfo.btc) + Number(desynInfo.balance))}
                </span>
                <span className="bl-text-primary bl-pl-1">BTC</span>
              </div>
            </div>
            <FoldIcon
              className={cn('bl-cursor-pointer', {
                'bl-hidden': isFlod,
              })}
              onClick={() => setIsFlod(true)}
            />
          </div>
          {isFlod && (
            <div className="bl-relative">
              <div className="bl-flex bl-relative md:bl-gap-4 bl-gap-2 bl-justify-between md:bl-justify-start">
                <BitFiCard bitfiInfo={bitfiInfo} btcfiInfo={btcfiInfo} />
                <DesynCard desynInfo={desynInfo} assetList={assetList} />
              </div>
              {isSigned && (
                <>
                  <div className="bl-pt-5 bl-pb-2 bl-text-black bl-font-semilight">
                    BTCFi Value/last 7 days
                  </div>
                  <div className="bl-relative">
                    <LineChart
                      data={btcData}
                      width={isMobile ? windowWidth : 568}
                      height={155}
                      lineColor="#E36E1B"
                      isMobile={isMobile}
                    />
                    <div className="bl-grid bl-grid-cols-8 md:bl-w-[568px] bl-pt-1 bl-pb-2">
                      {btcData?.map((item, index) => {
                        return (
                          <div className="bl-relative bl-text-[10px] bl-font-semilight" key={index}>
                            <div
                              key={index}
                              className="bl-absolute bl-right-0 bl-top-1/2 bl-transform bl-translate-x-1/2 -translate-y-1/2"
                            >
                              {item.title}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </>
              )}
              <div className="bl-flex bl-mt-4 md:bl-mt-0 bl-justify-end">
                <FoldIcon
                  className={cn(
                    'bl-cursor-pointer md:bl-absolute bl-z-10 bl-right-0 bl-bottom-0 bl-rotate-180',
                  )}
                  onClick={() => setIsFlod(false)}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const BitFiCard = ({
  bitfiInfo,
  btcfiInfo,
}: {
  bitfiInfo: {
    btc: string;
    balance: string;
  };
  btcfiInfo?: BtcFiInfoResponse;
}) => {
  return (
    <div
      className="md:bl-w-[275px] bl-overflow-hidden bl-w-1/2 bl-text-black bl-flex bl-flex-col bl-justify-between bl-items-start bl-p-3 md:bl-p-4 bl-h-[194px] bl-border bl-border-[#DCDCDC] bl-rounded-[6px]"
      style={{
        background:
          'linear-gradient(296deg, rgba(255, 255, 255, 0.00) 86.13%, rgba(227, 110, 27, 0.20) 97.64%), #FFF',
      }}
    >
      <div>
        <div className="bl-flex bl-gap-2 bl-items-center">
          <BtcfiBlackIcon />
          <span className="bl-text-black md:bl-text-xl bl-text-base bl-whitespace-nowrap">
            Bitfi Yield
          </span>
        </div>
        <div className="bl-pt-4">Balance:</div>
        <div className="bl-font-bold">
          <span className="bl-text-black bl-text-sm md:bl-text-base bl-font-bold">
            {formatNumber(bitfiInfo.balance)}
          </span>
          <span className="bl-text-primary bl-px-1">BFBTC</span>
        </div>
        <div className="bl-text-xs md:bl-text-sm">
          (~{formatNumber(bitfiInfo.btc)} <span className="bl-text-primary">BTC</span>)
        </div>
        <div className="md:bl-text-sm bl-pt-1 bl-text-xs bl-text-[#8B8B8B]">
          Plus rewards:{' '}
          <span className="bl-block md:bl-inline">
            {formatNumberWithCommas(btcfiInfo?.user?.noWithdrawReward || 0)} Points
          </span>
        </div>
      </div>
      <button
        className="bl-underline bl-text-[10px] bl-text-[#8B8B8B]"
        onClick={() => onToSection('bitfi-section')}
      >
        Show More
      </button>
    </div>
  );
};

const DesynCard = ({
  desynInfo,
}: {
  desynInfo: {
    btc: string;
    balance: string;
  };
  assetList: ChartsItem[];
}) => {
  return (
    <div
      className="md:bl-w-[275px] bl-overflow-hidden bl-w-1/2 bl-text-black bl-flex bl-flex-col bl-justify-between bl-items-start bl-p-3 md:bl-p-4 bl-h-[194px] bl-border bl-border-[#DCDCDC] bl-rounded-[6px]"
      style={{
        background:
          'linear-gradient(296deg, rgba(255, 255, 255, 0.00) 86.13%, rgba(227, 110, 27, 0.20) 97.64%), #FFF',
      }}
    >
      <div>
        <div className="bl-flex bl-gap-2 bl-items-center">
          <DesynIcon />
          <span className="bl-text-black md:bl-text-xl bl-text-base bl-whitespace-nowrap">
            Desyn Yield
          </span>
        </div>
        <div className="bl-pt-4">Balance:</div>
        <div className="bl-font-bold">
          <span className="bl-text-black bl-text-sm md:bl-text-base bl-font-bold">
            {formatNumber(desynInfo.balance)}
          </span>
          <span className="bl-text-primary bl-px-1">BLBTC</span>
        </div>
        <div className="bl-text-xs md:bl-text-sm">
          (~{formatNumber(desynInfo.balance)} <span className="bl-text-primary">BTC</span>)
        </div>
      </div>
      <button
        className="bl-underline bl-text-[10px] bl-text-[#8B8B8B]"
        onClick={() => onToSection('desyn-section')}
      >
        Show More
      </button>
    </div>
  );
};
