import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn, truncateTo8Decimals, formatNumber, toPercent } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import BTCIcon from '@/components/icons/coins/BTCIcon';
import { ClaimDialog } from '@/modules/btcfi/components/yield-claim';
import { StakeDialog } from '@/modules/btcfi/components/yield-stake';
import { RedeemDialog } from '@/modules/btcfi/components/yield-redeem';
import Decimal from 'decimal.js';
import Approximately from '@/modules/btcfiyield/icons/approximately';
import { useToast } from '@/hooks/toast';
import { InfoWarning } from '@/modules/btcfi/components/extra-waring';
import { Card } from '@/modules/btcfiyield/components/card';
const pageKey = 'pages.btcfi.yield';
import NavArrow from '@/modules/btcfiyield/icons/nav-arrow';
import DesynIcon from '@/modules/btcfiyield/icons/desyn';

import { RuleDataResponse, UserDataResponse, TvlResponse } from '@/modules/btcfiyield/types';

type DesynProps = {
  tvlData?: TvlResponse;
  ruleData?: RuleDataResponse;
  userData?: UserDataResponse;
  address?: string;
  verifySignIn: () => boolean;
  isSigned: boolean;
  handleClickConnect: () => void;
  setDesynInfo: (_arg0: { btc: string; balance: string }) => void;
};

export const Desyn = ({
  tvlData,
  address,
  verifySignIn,
  isSigned,
  userData,
  ruleData,
  handleClickConnect,
  setDesynInfo,
}: DesynProps) => {
  const isFull = Number(tvlData?.tvlSwitchV2) === 1;
  const [detailOpen, setDetailOpen] = useState(false);
  const { t } = useTranslation();
  const { toast } = useToast();
  const [claimOpen, setClaimOpen] = useState(false);
  const tvl = Number(tvlData?.maxTvlV2) - Number(tvlData?.tvlV2);
  const formattedTvl = tvl % 1 === 0 ? tvl.toFixed(0) : Math.floor(tvl * 100) / 100;
  const [stakeOpen, setStakeOpen] = useState(false);
  const [redeemOpen, setRedeemOpen] = useState(false);

  const handleDialog = (type: 'stake' | 'redeem') => {
    verifySignIn();
    if (isSigned) {
      if (type === 'stake') setStakeOpen(true);
      if (type === 'redeem') setRedeemOpen(true);
    }
  };

  const getTotal = () => {
    const a = new Decimal(String(userData?.dsnBtcvalueRewards));
    const b = new Decimal(String(userData?.btrBtcvalueRewards));
    const c = new Decimal(String(userData?.nextBtcRewards));
    const total = a.plus(b).plus(c).toFixed(20);
    return truncateTo8Decimals(total);
  };

  const totalRewards = userData ? getTotal() : 0;

  const handleTask = () => {
    const targetElement = document.getElementById('btcfi-yeild');
    const targetPosition = targetElement?.getBoundingClientRect();
    if (targetPosition) {
      window.scrollTo({
        top: targetPosition.top + window.scrollY - 50,
        left: targetPosition.left + window.scrollX,
        behavior: 'smooth',
      });
    }
  };

  const detailInfo = [
    {
      title: t(`${pageKey}.yieldsFrom`),
      value: (
        <a
          href="https://www.desyn.io/#/pool/******************************************/rewardsPreview?network=bitlayer"
          className="bl-underline bl-cursor-pointer"
          target="_blank"
          rel="noreferrer"
        >
          Desyn&apos;s BLBTC Pool
        </a>
      ),
    },
    {
      title: t(`${pageKey}.supportTokens`),
      value: (
        <div className="bl-flex-center bl-gap-1">
          <BTCIcon className="bl-size-6 bl-rounded-full" />
          BTC
        </div>
      ),
    },
    {
      title: t(`${pageKey}.rewardTokens`),
      value: 'BTC/wBTC + DSN',
    },
    {
      title: t(`${pageKey}.yieldsType`),
      value: 'Cedefi',
    },
  ];

  const handleStake = () => {
    if (isFull) {
      toast('Sold out in this round, please wait for the next round');
      return;
    }
    handleDialog('stake');
  };

  useEffect(() => {
    setDesynInfo({
      balance: String(formatNumber(Number(userData?.totalBtcAmount) + Number(totalRewards))),
      btc: '0',
    });
  }, [userData, totalRewards]);

  return (
    <div id="desyn-section" className="">
      <Card className="bl-mt-0 bl-relative bl-mx-auto" topRightBlack>
        <div className="bl-relative">
          <div className="bl-w-[91px] bl-gap-1 bl-items-center bl-flex md:bl-hidden bl-text-center md:bl-w-[84px] bl-h-[30px] md:bl-h-9 bl-rounded-tl-sm bl-text-lg bl-font-medium md:bl-text-xl bl-rounded-br-sm bl-text-white  bl-bg-black bl-absolute bl-left-0 bl-top-0 bl-flex-center">
            <DesynIcon />
            <span>Desyn</span>
          </div>
          {tvlData && (
            <div className="bl-flex bl-container bl-flex-center bl-pt-15 bl-pb-2 md:bl-pt-0 md:bl-pb-6">
              <div className="bl-flex bl-flex-col bl-justify-between bl-items-center bl-w-[747px] bl-gap-1">
                <div className="bl-flex bl-w-full bl-justify-between bl-text-xs md:bl-text-sm">
                  <span className="md:bl-font-[500] bl-text-black">
                    {isFull ? 'Finished' : `${formattedTvl}BTC remaining`}
                  </span>
                  <span className="bl-text-[#60656F]">
                    Total volume: {Number(tvlData.maxTvlV2)}BTC
                  </span>
                </div>
                <div className="bl-h-2 bl-w-full bl-bg-secondary">
                  <div
                    className="bl-bg-primary bl-min-w-12 md:bl-min-w-8 bl-h-full bl-flex bl-items-center bl-bg-[50%_100%] bl-justify-end bl-bg-[url('/images/btcfi/progress.svg')] bl-bg-repeat-x"
                    style={{
                      width: `${((Number(tvlData?.tvlV2) / Number(tvlData?.maxTvlV2)) * 100).toFixed(2)}%`,
                    }}
                  >
                    <span className="bl-text-[10px] bl-text-black">
                      {`${((Number(tvlData?.tvlV2) / Number(tvlData?.maxTvlV2)) * 100).toFixed(2)}%`}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="bl-mt-2 bl-border-b bl-border-secondary md:bl-hidden bl-mx-4"></div>
        <div className="bl-relative bl-flex bl-flex-col bl-items-center bl-max-w-[747px] bl-mx-4 md:bl-mx-auto bl-pt-5 md:bl-pt-10 md:bl-pb-3 bl-font-['Tomkin_Narrow'] bl-font-normal md:bl-font-bold bl-border-0 md:bl-border bl-border-[#A9A9A9] bl-rounded-md bl-z-[1] before:bl-content-none md:before:bl-content-[''] before:bl-absolute before:-bl-z-[2] before:bl-left-[-1px] before:bl-bottom-[-1px] before:bl-w-10 before:bl-h-10 before:bl-bg-white before:bl-bg-[url('/images/btcfi/angle-black.svg')] before:bl-bg-[length:100%] before:bl-rotate-180 after:bl-content-none md:after:bl-content-[''] after:bl-absolute after:-bl-z-[2] after:bl-right-[-1px] after:bl-top-[-1px] after:bl-w-10 after:bl-h-10 after:bl-bg-white after:bl-bg-[url('/images/btcfi/angle-black.svg')] after:bl-bg-[length:100%]">
          <div className="bl-w-[91px] bl-gap-1 bl-items-center bl-hidden md:bl-flex md:bl-w-[84px] bl-h-[30px] md:bl-h-9 bl-rounded-tl-sm bl-text-lg bl-font-medium md:bl-text-xl bl-rounded-br-sm bl-text-white  bl-bg-black bl-absolute bl-left-0 bl-top-0 bl-flex-center">
            <DesynIcon />
            <span>Desyn</span>
          </div>
          <div className="bl-text-center">
            {!address ? (
              <button
                className="bl-flex bl-items-center bl-justify-center bl-gap-2 bl-w-[235px] bl-h-[50px] bl-my-[23px] bl-bg-[#E36E1B] bl-rounded-sm bl-text-black bl-text-2xl bl-border bl-border-black"
                onClick={handleClickConnect}
              >
                {t('common.connect')}
              </button>
            ) : (
              <>
                <div className="bl-text-[#393D44] bl-flex-center bl-gap-1 bl-font-normal md:bl-text-lg/6">
                  {t(`${pageKey}.totalValue`)}
                  <InfoWarning />
                </div>
                <div className="bl-flex-center bl-gap-2 bl-text-black bl-mt-[14px] bl-text-[30px] md:bl-text-[46px]/[58px] bl-font-bold">
                  <Approximately className="bl-size-4" />
                  <BTCIcon className="bl-size-10 -bl-translate-y-1" />
                  {formatNumber(Number(userData?.totalBtcAmount) + Number(totalRewards))}
                  <span className="bl-ml-1 bl-font-light bl-text-[30px] md:bl-text-[46px]">
                    BTC
                  </span>
                </div>
              </>
            )}
          </div>
          <div className="bl-flex bl-justify-center bl-gap-6 md:bl-gap-4 md:bl-w-[216px] bl-mt-6 md:bl-mt-8">
            <div className="md:bl-w-[260px] bl-relative bl-shrink-0">
              <div className="bl-text-[#393D44] bl-text-xs/5 md:bl-text-base">
                {t(`${pageKey}.apy`)}
              </div>
              <div className="bl-mt-1.5 md:bl-mt-3 md:bl-text-2xl bl-text-[#E36E1B] bl-font-bold">
                {toPercent(ruleData?.minTotalApr)}~{toPercent(ruleData?.maxTotalApr)}
              </div>
              <div className="bl-text-xs bl-hidden md:bl-block bl-font-semilight bl-absolute bl-top-16">
                Yield may vary depending on network conditions
              </div>
            </div>
            <div className="bl-w-[1px] bl-h-6 md:bl-h-12 bl-bg-secondary bl-shrink-0 bl-mt-3.5 md:bl-mt-2.5" />
            <div className="md:bl-w-[214px] md:bl-flex md:bl-flex-col bl-items-start md:bl-ml-[108px] bl-shrink-0 md:bl-text-right">
              <div className="bl-flex bl-justify-end bl-items-center md:bl-h-7">
                <span className="bl-text-[#393D44] bl-text-xs md:bl-text-base">
                  {t(`${pageKey}.rewards`)}
                </span>
                {address && isSigned && (
                  <button
                    className="bl-flex bl-items-center bl-justify-center bl-h-5 md:bl-h-7 bl-ml-[10px] bl-px-2.5 md:bl-px-[14px] bl-bg-[#E36E1B] bl-rounded-sm bl-text-black bl-text-[10px]/[10px] md:bl-text-sm/[10px] bl-border bl-border-black hover:bl-text-white"
                    onClick={() => setClaimOpen(true)}
                  >
                    {t(`${pageKey}.claim`)}
                  </button>
                )}
              </div>
              <div className="bl-mt-2 bl-text-base/5 md:bl-text-2xl bl-flex bl-items-center bl-gap-1 md:bl-gap-2 bl-text-black bl-font-bold">
                <Approximately className="bl-w-[10px]" />
                <BTCIcon className="bl-size-6 bl-hidden md:bl-block" />
                <span className="bl-whitespace-nowrap">{formatNumber(totalRewards)} BTC</span>
              </div>
            </div>
          </div>
          <div className="bl-text-xs md:bl-hidden bl-block bl-pt-0">
            Yield may vary depending on network conditions
          </div>
          <div className="bl-flex-center bl-mt-10 md:bl-mt-12 bl-gap-4 md:bl-gap-0 bl-scale-95 md:bl-scale-100">
            <Button
              variant="outline-5"
              className="bl-flex-center md:-bl-mr-2 md:bl-scale-[85%] bl-group bl-w-[138px] bl-h-10 md:bl-w-[236px] md:bl-h-[51px] bl-text-lg/none md:bl-text-2xl/5 bl-font-normal md:bl-font-bold hover:bl-text-white md:hover:bl-text-black"
              onClick={() => handleDialog('redeem')}
              noOverlay
            >
              <span className="bl-font-normal group-hover:bl-text-primary">
                {t(`${pageKey}.redeem`)}
              </span>
            </Button>
            <div className="bl-relative">
              <Button
                variant="outline-5"
                keepOverlay
                className="bl-flex-center md:-bl-ml-2 md:bl-scale-[85%] bl-w-[138px] bl-h-10 md:bl-w-[236px] md:bl-h-[51px] bl-text-lg/none md:bl-text-2xl/5 bl-font-normal md:bl-font-bold hover:bl-text-white"
                onClick={handleStake}
              >
                <span className="bl-font-normal">{t(`${pageKey}.stake`)}</span>
              </Button>
              {/* <button
                onClick={handleTask}
                className="bl-absolute hover:bl-bg-primary hover:bl-text-white hover:bl-border-white bl-cursor-pointer bl-z-10 bl-border bl-border-primary bl-top-[-16px] bl-right-[-60px] md:bl-right-[-130px] bl-w-[95px] md:bl-w-[162px] bl-h-8 bl-flex-center bl-text-xs bl-text-white bl-bg-black"
              >
                10 BTR bonus<span className="bl-hidden md:bl-inline">,&nbsp;timelimited</span>
              </button> */}
            </div>
          </div>
          <div className="bl-mt-2 bl-text-sm bl-text-[#00000066] bl-hidden md:bl-block">
            {t(`${pageKey}.service`)}
          </div>
        </div>
        {/* function */}
        <div className="bl-mt-4 md:bl-mt-0 bl-font-['Tomkin_Narrow'] bl-font-bold bl-text-center">
          <div
            className="bl-mt-4 md:bl-mt-[15px] bl-text-[#5B5B5C] bl-text-xs md:bl-text-base bl-font-[350] md:bl-font-bold [&>a]:bl-underline"
            dangerouslySetInnerHTML={{
              __html: t(`${pageKey}.accept`),
            }}
          ></div>
          <button
            className="bl-flex-center bl-group bl-gap-1 md:bl-mt-4 bl-mx-auto bl-my-6 bl-text-sm bl-text-black bl-cursor-pointer"
            onClick={() => setDetailOpen(!detailOpen)}
          >
            <span className="bl-font-normal">{t(`${pageKey}.details`)}</span>
            <NavArrow
              className={cn('bl-size-4 group-hover:bl-text-primary bl-text-black bl-duration-200', {
                'bl-rotate-180': detailOpen,
              })}
            />
          </button>
        </div>
        {/* detail */}
        {detailOpen && (
          <div className="bl-relative bl-max-w-[747px] bl-px-6 md:bl-mx-auto bl-py-4 md:bl-p-7.5 md:bl-px-12 md:bl-py-8 bl-mt-5 md:bl-mt-6 bl-font-['Tomkin_Narrow'] bl-text-sm md:bl-text-base md:bl-font-bold bl-tracking-[0.08em] bl-text-black bl-border-0 md:bl-border bl-border-[#A9A9A9] bl-rounded-md bl-z-[1] before:bl-content-none md:before:bl-content-[''] before:bl-absolute before:-bl-z-[2] before:bl-left-[-1px] before:bl-bottom-[-1px] before:bl-w-10 before:bl-h-10 before:bl-bg-white before:bl-bg-[url('/images/btcfi/angle-flat.svg')] before:bl-bg-[length:100%] before:bl-rotate-180 after:bl-content-none md:after:bl-content-[''] after:bl-absolute after:-bl-z-[2] after:bl-right-[-1px] after:bl-top-[-1px] after:bl-w-10 after:bl-h-10 after:bl-bg-white after:bl-bg-[url('/images/btcfi/angle-flat.svg')] after:bl-bg-[length:100%]">
            {detailInfo.map((item) => (
              <div
                key={item.title}
                className="bl-flex bl-justify-between bl-items-center bl-h-6 bl-mb-3"
              >
                <span className="bl-text-[#00000099]">{item.title}</span>
                <span className="bl-font-medium md:bl-font-bold">{item.value}</span>
              </div>
            ))}
            <div className="bl-w-full bl-h-[1px] bl-my-6 md:bl-my-7 bl-bg-[#CECECE]"></div>
            <div className="bl-h-6 bl-text-[#393D44] bl-font-bold">Yields Source:</div>
            <div className="bl-leading-6 bl-text-[#393D44] bl-my-4">
              <span className="bl-text-[#00000099] ">{t(`${pageKey}.finalYield`)}</span>
              <span className="bl-font-bold">
                {toPercent(ruleData?.minTotalApr)} -{toPercent(ruleData?.maxTotalApr)}
              </span>
            </div>
            <div
              className="bl-relative bl-pl-5 bl-font-medium md:bl-font-bold before:bl-absolute before:bl-left-1.5 md:before:bl-left-[10px] before:bl-top-2 md:before:bl-top-[10px] before:bl-w-[3px] before:bl-h-[3px] before:bl-bg-black"
              dangerouslySetInnerHTML={{
                __html: t(`${pageKey}.yieldDesc1`, {
                  percent: `${toPercent(ruleData?.minBtcApr)}~${toPercent(ruleData?.maxBtcApr)}`,
                }),
              }}
            ></div>
            <div
              className="bl-relative bl-pl-5 bl-font-medium md:bl-font-bold before:bl-absolute before:bl-left-1.5 md:before:bl-left-[10px] before:bl-top-2 md:before:bl-top-[10px] before:bl-w-[3px] before:bl-h-[3px] before:bl-bg-black"
              dangerouslySetInnerHTML={{
                __html: t(`${pageKey}.yieldDesc3`, {
                  percent: toPercent(ruleData?.dsnApr),
                }),
              }}
            ></div>
          </div>
        )}
        <div className="bl-hidden md:bl-block bl-max-w-[854px] bl-mx-auto bl-mt-10 bl-my-6 bl-border-[0.5px] bl-border-black" />
      </Card>
      <div className="md:bl-pb-5 bl-text-sm bl-text-[#00000066] bl-bg-white bl-text-center bl-font-['Tomkin_Narrow'] bl-font-[350] md:bl-hidden">
        {t(`${pageKey}.service`)}
      </div>
      {claimOpen && isSigned && (
        <ClaimDialog open={claimOpen} onOpenChange={setClaimOpen} userData={userData} />
      )}
      {stakeOpen && isSigned && (
        <StakeDialog open={stakeOpen} onOpenChange={setStakeOpen} ruleData={ruleData} />
      )}
      <RedeemDialog
        open={redeemOpen}
        onOpenChange={setRedeemOpen}
        userData={userData}
        address={address}
      />
    </div>
  );
};
