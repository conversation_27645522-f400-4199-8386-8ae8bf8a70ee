import { cn } from '@/lib/utils';
import { useMediaQuery } from '@react-hook/media-query';
import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import ky from 'ky';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  CarPriceResponse,
  ICollectionItem,
  InfoResponse,
  RoiResponse,
} from '@/modules/raffle/types';
import { ConnectWalletDialog, WalletConnector } from '@/components/featured/wallet';
import { chain } from '@/modules/user-center/config';
import { Button } from '@/components/ui/button';
import { useAccount } from '@/hooks/wallet/account';
import { useDialog } from '@/hooks/dialog';
import { motion, AnimatePresence } from 'framer-motion';
import { mapHashedImage } from '@/modules/raffle/hooks/img-map';
import RefreshIcon from '@/components/icons/RefreshIcon';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import MoreInfoIcon from '@/components/icons/MoreInfoIcon';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useCountUp } from 'react-countup';
import useChange from '@react-hook/change';
import { UseEnsureOnChain } from '@/hooks/wallet/chains';
import RoiChart from './components/roi-chart';
import FourStarIcon from '@/components/icons/FourStarIcon';
import FiveStarIcon from '@/components/icons/FiveStarIcon';
import { ClientOnly } from 'remix-utils/client-only';

const ImagePreloader = ({ imageSrc, alt }: { imageSrc: string; alt: string }) => {
  useEffect(() => {
    const img = new Image();
    img.src = imageSrc;
  }, [imageSrc]);

  return <img src={imageSrc} alt={alt} />;
};

// 0:normal 1:premium 2: top
export enum CarType {
  Normal = 0,
  Premium = 1,
  Top = 2,
}
export enum StarType {
  Normal = 3,
  Premium = 4,
  Top = 5,
}

export const ConnectWalletButton = () => {
  const { t } = useTranslation();
  const className =
    'bl-h-9 bl-w-40 bl-text-sm bl-text-black lg:bl-h-12 lg:bl-w-56 lg:bl-text-[24px] bl-z-50';
  return (
    <WalletConnector chain={chain}>
      <Button variant="default" overlayVariant="outline" type="button" className={cn(className)}>
        <span>{t('common.connect')}</span>
      </Button>
    </WalletConnector>
  );
};

type AssembleButtonProps = {
  onClick: () => void;
  disabled?: boolean;
};

const AssembleButton: React.FC<React.PropsWithChildren<AssembleButtonProps>> = ({
  onClick,
  disabled,
  children,
}) => {
  const handleClick = () => {
    if (!disabled) {
      onClick();
    }
  };
  return (
    <button
      className={cn('bl-w-[160px] bl-relative bl-h-[42px] bl-bg-red bl-cursor-pointer bl-group')}
      onClick={handleClick}
    >
      <div
        className={cn(
          `bl-flex bl-items-center bl-justify-center bl-h-full bl-text-base bl-w-full bl-relative bl-z-10 bl-uppercase bl-text-center bl-text-white
      bl-bg-[url('https://static.bitlayer.org/images/user-center/raffle/assemble-hover.413986a946.png')] bl-bg-cover
      group-hover:bl-bg-[url('https://static.bitlayer.org/images/user-center/raffle/assemble-primary.1cf63f8538.png')] group-hover:bl-text-black `,
          {
            "bl-disabled !bl-text-[#D6D6D6] !bl-bg-[url('https://static.bitlayer.org/images/user-center/raffle/assemble-disabled.83d2125a74.png')] hover:bl-cursor-not-allowed":
              disabled,
          },
        )}
      >
        {children}
      </div>
    </button>
  );
};

interface BorderProps {
  star: StarType;
}

function BorderAnimate({ star }: BorderProps) {
  const [current, setCurrent] = useState(0);
  const items = ['0', '1', '2', '3'];

  const images = [
    `/images/user-center/raffle/assemble-cars/key1_${star}.png`,
    `/images/user-center/raffle/assemble-cars/key2_${star}.png`,
    `/images/user-center/raffle/assemble-cars/key3_${star}.png`,
    `/images/user-center/raffle/assemble-cars/key4_${star}.png`,
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrent((prev) => (prev + 1) % items.length);
    }, 400);
    return () => clearInterval(timer);
  }, [items.length]);

  return (
    <div className="relative size-10">
      <AnimatePresence>
        {items.map((item, key) =>
          current === key ? (
            <motion.div
              key={`notAssembling-${current}`}
              className="bl-absolute bl-top-0 bl-right-0 bl-left-0 bl-bottom-0"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{
                // duration: 0.3,
                ease: 'linear',
              }}
            >
              <img src={images[current]} alt="" className="bl-w-full bl-h-full" />
            </motion.div>
          ) : null,
        )}
      </AnimatePresence>
    </div>
  );
}

interface AssembleAnimateProps {
  star: StarType;
}

function AssembleAnimate({ star }: AssembleAnimateProps) {
  const [current, setCurrent] = useState(0);
  const items = ['0', '1', '2', '3'];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrent((prev) => (prev + 1) % items.length);
    }, 150);
    return () => clearInterval(timer);
  }, [items.length]);

  return (
    <div className="relative size-10">
      <AnimatePresence>
        <motion.div
          key="assembling"
          className="bl-absolute bl-top-0 bl-right-0 bl-left-0 bl-bottom-0"
          initial={{ opacity: 0, scale: 1 }}
          animate={{ opacity: 1, scale: 1.2 }}
          exit={{ opacity: 0, scale: 1 }}
          transition={{
            duration: 0.3,
            ease: 'linear',
          }}
        >
          <motion.img
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{
              // duration: 0.1,
              ease: 'linear',
            }}
            src={
              mapHashedImage(
                `/images/user-center/raffle/assemble-cars/assemble_key${current + 1}_${star}.png`,
              )!
            }
            alt=""
            className="bl-w-full bl-h-full"
          />
        </motion.div>
      </AnimatePresence>
    </div>
  );
}

interface CarCardProps {
  isSigned?: boolean;
  onLogin?: () => void;
  isSigning?: boolean;
  categoryId: CarType;
  star: StarType;
  amount: string;
  canAssemble: boolean;
  refresh: () => void;
  carPriceData?: number;
}

const CarCard = ({
  isSigned,
  isSigning,
  amount,
  canAssemble,
  star = 3,
  refresh,
  onLogin,
  carPriceData,
}: CarCardProps) => {
  const { t } = useTranslation();
  const [isAssembling, setIsAssembling] = useState(false);
  const noCars = Number(amount) <= 0;
  const { address } = useAccount({ network: chain.networkType });
  const { open: openDialog } = useDialog();
  const { t: TCommon } = useTranslation();
  const [assembleMusic, setAssembleMusic] = useState<HTMLAudioElement | null>(null);
  const { ensure } = UseEnsureOnChain();

  useEffect(() => {
    const assembleMusic = new Audio('https://static.bitlayer.org/assemble-cars/music/assemble.wav');
    setAssembleMusic(assembleMusic);
  }, []);

  const { isPending, mutateAsync } = useMutation({
    mutationKey: ['carAssemble', star],
    mutationFn: async () => {
      await ensure({ chain });

      await ky.post('/api/raffle/assemble', {
        json: { starRating: star },
      });
      return true;
    },
  });

  const handleClickConnect = () => {
    openDialog({
      content: ({ close }) => (
        <ConnectWalletDialog
          title={TCommon('common.connect')}
          description={TCommon('common.connectDesc')}
          chain={chain}
          close={close}
        />
      ),
    });
  };

  const onAssemble = async () => {
    if (!address) {
      handleClickConnect();
      return;
    }

    if (!isSigned) {
      return onLogin && onLogin();
    }
    await mutateAsync();
    assembleMusic?.play();
    refresh();

    if (!canAssemble) {
      return;
    }

    setIsAssembling(true);
    setTimeout(() => {
      setIsAssembling(false);
    }, 600);
  };

  const carAnimation = useMemo(
    () => ({
      initial: { scale: 1 },
      animate: isAssembling ? { scale: 1.1 } : { scale: 1 },
      transition: {
        duration: 0.5,
        ease: 'easeInOut',
      },
    }),
    [isAssembling],
  );

  const carSrc = useMemo(
    () =>
      mapHashedImage(
        `/images/user-center/raffle/assemble-cars/car_${
          isAssembling ? 'got' : canAssemble ? 'assemble' : noCars ? 'no' : 'got'
        }_${star}.png`,
      ),
    [isAssembling, canAssemble, noCars, star],
  );

  const onRefresh = async () => {
    // await mutateAsync();
    refresh();
  };

  const counter = useRef<HTMLSpanElement>(null);
  const { start, update } = useCountUp({
    ref: counter,
    start: 0,
    end: Number(amount) || 0,
    delay: 0,
    duration: 2,
  });

  useChange(amount, (current, prev) => {
    if (current === undefined) return;
    if (prev === undefined) {
      start();
    } else {
      update(current);
    }
  });

  return (
    <div className="bl-flex bl-relative bl-flex-col bl-items-center bl-pb-12">
      <div className="bl-flex bl-w-full bl-flex-row bl-justify-center bl-text-[20px] bl-items-center bl-gap-2">
        {t('pages.raffle.collection.estimated')}:
        <span className="bl-font-bold bl-text-primary bl-text-[28px]">{carPriceData} USD/Car</span>
        <div
          className="bl-flex-center bl-gap-2 bl-text-secondary bl-text-base bl-cursor-pointer"
          onClick={onRefresh}
        >
          <RefreshIcon className="bl-w-4" />
        </div>
      </div>
      <div className="bl-relative bl-flex bl-items-center bl-justify-center md:bl-w-[317px] md:bl-h-[173px]">
        <motion.img
          src={carSrc!}
          className="bl-flex bl-top-0 bl-right-0 bl-w-full bl-h-full bl-max-h-full"
          alt=""
          {...carAnimation}
        />

        {canAssemble && (
          <AnimatePresence>
            {isAssembling ? <AssembleAnimate star={star} /> : <BorderAnimate star={star} />}
          </AnimatePresence>
        )}
        <div
          className={cn(
            'bl-absolute bl-top-2 bl-right-4 bl-text-xl md:bl-text-[36px] bl-font-[700] bl-text-white',
            {
              'bl-hidden': noCars,
            },
          )}
        >
          X {amount === undefined ? '0' : <span ref={counter} />}
        </div>
      </div>
      <div className="bl-absolute bl-bottom-11 bl-w-full bl-flex bl-flex-col bl-items-center bl-justify-center bl-z-20">
        <div className="bl-w-full bl-flex bl-justify-center bl-items-center bl-pb-3">
          <img src={mapHashedImage(`/images/user-center/raffle/star-${star}.png`)!} alt="" />
        </div>
        <AssembleButton onClick={onAssemble} disabled={isSigned && (isPending || !canAssemble)}>
          {t('pages.raffle.collection.ready')}
        </AssembleButton>
      </div>
    </div>
  );
};

interface CollectionItemProps {
  amount: number;
  star: number;
  imageKey: string;
  label: string;
  itemClassName?: string;
  itemLabelClassName?: string;
  itemTagClassName?: string;
  itemXClassName?: string;
  itemAmountClassName?: string;
}
export const CollectionItem = ({
  amount,
  star,
  imageKey,
  label,
  itemClassName = '',
  itemLabelClassName = '',
  itemTagClassName = '',
  itemXClassName = '',
  itemAmountClassName = '',
}: CollectionItemProps) => {
  const disabled = amount <= 0;
  // const imgSrc = `https://static.bitlayer.org/assemble-cars/${imageKey}_color_${star}.png`
  const imgSrc = mapHashedImage(
    `/images/user-center/raffle/assemble-cars/${imageKey}_${disabled ? 'grey' : 'color'}_${star}.png`,
  );

  const styles = {
    container: {
      background:
        'repeating-linear-gradient(222.72deg, #81C1D9 15.29%, #ACA2DB 36.15%, #65BFE0 53.09%, #8DC7D8 67.95%, #FBF4F4 81.32%, #CCAAD5 90.22%, #EEE2F1 96.27%)',
      backgroundSize: 'cover',
      animation: 'diagonal-gradient-animation 1s ease infinite',
      animationDelay: '500ms',
    },
  };

  const bgSrc =
    star <= 4
      ? mapHashedImage(`/images/user-center/raffle/assemble-cars/collection_bg_${star}.png`)
      : mapHashedImage(`/images/user-center/raffle/assemble-cars/collection_bg_5.gif`);

  return (
    <div
      className={cn(
        'bl-relative bl-flex bl-items-center bl-justify-center bl-max-w-[23%]',
        itemClassName,
        {},
      )}
    >
      <div className="bl-relative bl-z-10">
        <ImagePreloader imageSrc={imgSrc!} alt={imageKey} />
        <div
          className={cn(
            'bl-absolute bl-top-1 md:bl-top-2 bl-right-1 md:bl-right-4 md:bl-text-2xl bl-font-[700] bl-text-white',
            itemTagClassName,
            { 'bl-hidden': disabled },
          )}
        >
          <span className={cn('bl-text-xs md:bl-text-xl', itemXClassName)}>X </span>
          <span className={cn('bl-text-base md:bl-text-[26px]', itemAmountClassName)}>
            {amount}
          </span>
        </div>
        <div
          className={cn(
            'bl-absolute bl-bottom-0 bl-left-0 bl-right-0 bl-text-[10px]/none md:bl-text-lg/[1] bl-font-[700] bl-text-black bl-text-center bl-uppercase',
            itemLabelClassName,
          )}
        >
          {label}
        </div>
      </div>

      <div className="bl-absolute bl-top-0 bl-left-0 bl-right-0 bl-bottom-0 bl-z-0">
        <ImagePreloader imageSrc={bgSrc!} alt={`${star} bg`} />
      </div>
    </div>
  );
};

interface CollectionCardProps {
  collection: ICollectionItem[];
  className?: string;
  itemClassName?: string;
  itemLabelClassName?: string;
  itemTagClassName?: string;
  itemXClassName?: string;
  itemAmountClassName?: string;
}
export const CollectionCard = ({
  collection,
  className,
  itemClassName,
  itemLabelClassName,
  itemTagClassName,
  itemXClassName,
  itemAmountClassName,
}: CollectionCardProps) => {
  const { t } = useTranslation('', { keyPrefix: 'pages.raffle.collection' });

  return (
    <div
      className={cn(
        'bl-flex bl-justify-between md:bl-justify-center bl-items-center bl-gap-2 bl-pb-12 bl-flex-wrap md:bl-flex-nowrap',
        className,
      )}
    >
      {collection.map((item: ICollectionItem, index: number) => {
        const { amount, itemName, star } = item;
        return (
          <CollectionItem
            key={index}
            amount={amount}
            star={Number(star)}
            imageKey={itemName}
            label={t(`cars.${itemName}`)}
            itemClassName={itemClassName}
            itemLabelClassName={itemLabelClassName}
            itemTagClassName={itemTagClassName}
            itemXClassName={itemXClassName}
            itemAmountClassName={itemAmountClassName}
          />
        );
      })}
    </div>
  );
};

export interface CarProps {
  id: number;
  categoryId: CarType;
  cars: number;
}

export interface StashProps {
  isSigned?: boolean;
  onLogin?: () => void;
  isSigning?: boolean;
  normalCarAmount: string;
  premiumCarAmount: string;
  topCarAmount: string;
  itemList: ICollectionItem[];
  refresh: () => void;
  carPrice: CarPriceResponse;
  userInfo: InfoResponse;
  setCanAssemble: any;
  roiData: RoiResponse;
}

export interface FeaturedProps extends StashProps {
  collections: Record<CarType, ICollectionItem[]>;
  canAssemble: Record<CarType, boolean>;
}

const DesktopFeatured = ({
  normalCarAmount = '0',
  premiumCarAmount = '0',
  topCarAmount = '0',
  collections,
  canAssemble,
  isSigned,
  isSigning,
  onLogin,
  refresh,
  carPrice,
}: FeaturedProps) => {
  const { t } = useTranslation('', { keyPrefix: 'pages.raffle.collection' });

  return (
    <div className="bl-m-w-[1440px] bl-px-36">
      <div className="bl-flex bl-justify-between bl-items-center bl-pt-15 bl-pb-12">
        <CarCard
          isSigned={isSigned}
          isSigning={isSigning}
          onLogin={onLogin}
          categoryId={CarType.Normal}
          star={StarType.Normal}
          amount={normalCarAmount}
          canAssemble={canAssemble[CarType.Normal]}
          refresh={refresh}
          carPriceData={carPrice.normalCarPrice}
        />
        <CarCard
          isSigned={isSigned}
          isSigning={isSigning}
          onLogin={onLogin}
          categoryId={CarType.Premium}
          star={StarType.Premium}
          amount={premiumCarAmount}
          canAssemble={canAssemble[CarType.Premium]}
          refresh={refresh}
          carPriceData={carPrice.premiumCarPrice}
        />
        <CarCard
          isSigned={isSigned}
          isSigning={isSigning}
          onLogin={onLogin}
          categoryId={CarType.Top}
          star={StarType.Top}
          amount={topCarAmount}
          canAssemble={canAssemble[CarType.Top]}
          refresh={refresh}
          carPriceData={carPrice.topCarPrice}
        />
      </div>

      <div className="bl-pt-15 bl-pb-12">
        {Object.keys(collections).map((key: string, index: number) => {
          const categoryId = parseInt(key) as CarType;
          const star = categoryId + 3;
          return (
            <div key={index}>
              <div className="bl-flex bl-items-center bl-gap-2 bl-mb-5 bl-px-5 bl-py-3 bl-border-t-[3px] bl-border-r-[3px] bl-border-white bl-w-fit bl-bg-[url('https://static.bitlayer.org/images/user-center/raffle/noisy-bg.2a61deb446.png')] bl-bg-cover">
                <img src={mapHashedImage(`/images/user-center/raffle/star-${star}.png`)!} alt="" />
                <span className="bl-uppercase bl-text-xl bl-font-[700] bl-text-white">
                  {t('collection')}
                </span>
              </div>
              <CollectionCard collection={collections[categoryId]} />
            </div>
          );
        })}
      </div>
    </div>
  );
};

const MobileFeatured = ({
  normalCarAmount = '0',
  premiumCarAmount = '0',
  topCarAmount = '0',
  collections,
  canAssemble,
  isSigned,
  isSigning,
  onLogin,
  refresh,
  carPrice,
}: FeaturedProps) => {
  const { t } = useTranslation('', { keyPrefix: 'pages.raffle.collection' });
  const starList = [StarType.Normal, StarType.Premium, StarType.Top];
  const [selectedTab, setSelectedTab] = useState(StarType.Normal);
  const prevTabRef = useRef(StarType.Normal);

  const currentIndex = starList.indexOf(selectedTab);
  const prevIndex = starList.indexOf(prevTabRef.current);
  const direction = currentIndex > prevIndex ? 'next' : 'prev';

  const variants = {
    enter: (direction: string) => {
      if (direction === 'next') {
        return {
          x: [300, 0],
          y: 10,
          filter: 'brightness(0.5)',
          opacity: 1,
          transition: {
            x: { duration: 0.5, ease: 'easeInOut' },
            y: { duration: 0 },
            filter: { duration: 0 },
            opacity: { duration: 0 },
          },
        };
      } else {
        return {
          x: -300,
          opacity: 0,
          transition: { duration: 0.5, ease: 'easeInOut' },
        };
      }
    },
    animate: (direction: string) => {
      if (direction === 'next') {
        return {
          x: 0,
          y: [10, 0],
          filter: ['brightness(0.5)', 'brightness(1)'],
          opacity: 1,
          transition: {
            y: { duration: 0.5, ease: 'easeInOut', delay: 0.5 },
            filter: { duration: 0.5, ease: 'easeInOut', delay: 0.5 },
          },
        };
      } else {
        return {
          x: 0,
          opacity: 1,
          transition: {
            duration: 0.5,
            ease: 'easeOut',
          },
        };
      }
    },
    exit: (direction: string) => {
      if (direction === 'next') {
        return {
          x: [0, 0, -300],
          y: [0, 10, 10],
          filter: ['brightness(1)', 'brightness(0.5)', 'brightness(0.1)'],
          opacity: 1,
          transition: {
            duration: 1,
            times: [0, 0.5, 1],
            ease: 'easeInOut',
          },
        };
      } else {
        return {
          x: 300,
          opacity: 0,
          transition: { duration: 0.5, ease: 'easeInOut' },
        };
      }
    },
  };
  return (
    <div className="bl-px-6 bl-min-h-[540px]">
      <div className="window">
        <div className="bl-h-8 md:bl-h-12 bl-border bl-border-primary bl-items-center bl-justify-center bl-bg-black bl-text-primary bl-w-full bl-flex">
          {starList.map((item) => (
            <div
              key={item}
              data-state={item === selectedTab ? 'active' : ''}
              className={cn(
                'bl-relative bl-group bl-inline-flex bl-items-center bl-justify-center bl-whitespace-nowrap bl-px-8 bl-h-full bl-text-[22px] bl-transition-all focus-visible:bl-outline-none disabled:bl-pointer-events-none data-[state=active]:bl-text-white bl-border-r bl-border-primary last:bl-border-r-0 bl-p-0 bl-uppercase bl-flex-1 bl-text-base bl-text-primary',
              )}
              onClick={() => {
                if (selectedTab !== item) {
                  prevTabRef.current = selectedTab;
                  setSelectedTab(item);
                }
              }}
            >
              <motion.div className={cn('bl-z-10')}>
                {item} {t('stars')}
              </motion.div>

              {item === selectedTab ? (
                <motion.div
                  className="bl-z-0 bl-absolute bl-left-0 bl-top-0 bl-right-0 bl-bottom-0 bl-bg-primary"
                  layoutId="bg-primary"
                />
              ) : null}
            </div>
          ))}
        </div>
        <main
          className={cn(
            'bl-mt-10 bl-ring-offset-background focus-visible:bl-outline-none focus-visible:bl-ring-2 focus-visible:bl-ring-ring focus-visible:bl-ring-offset-2',
          )}
        >
          <AnimatePresence initial={false} custom={direction} mode="wait">
            <motion.div
              key={selectedTab}
              custom={direction}
              variants={variants}
              initial="enter"
              animate="animate"
              exit="exit"
            >
              <div
                className={cn('bl-relative', {
                  'bl-hidden': selectedTab !== StarType.Normal,
                })}
              >
                <CollectionCard collection={collections[CarType.Normal]} />
                <CarCard
                  isSigned={isSigned}
                  isSigning={isSigning}
                  onLogin={onLogin}
                  categoryId={CarType.Normal}
                  star={StarType.Normal}
                  amount={normalCarAmount}
                  canAssemble={canAssemble[CarType.Normal]}
                  refresh={refresh}
                  carPriceData={carPrice.normalCarPrice}
                />
              </div>
              <div
                className={cn('bl-relative', {
                  'bl-hidden': selectedTab !== StarType.Premium,
                })}
              >
                <CollectionCard collection={collections[CarType.Premium]} />
                <CarCard
                  isSigned={isSigned}
                  isSigning={isSigning}
                  onLogin={onLogin}
                  categoryId={CarType.Premium}
                  star={StarType.Premium}
                  amount={premiumCarAmount}
                  canAssemble={canAssemble[CarType.Premium]}
                  refresh={refresh}
                  carPriceData={carPrice.premiumCarPrice}
                />
              </div>
              <div
                className={cn('bl-relative', {
                  'bl-hidden': selectedTab !== StarType.Top,
                })}
              >
                <CollectionCard collection={collections[CarType.Top]} />
                <CarCard
                  isSigned={isSigned}
                  isSigning={isSigning}
                  onLogin={onLogin}
                  categoryId={CarType.Top}
                  star={StarType.Top}
                  amount={topCarAmount}
                  canAssemble={canAssemble[CarType.Top]}
                  refresh={refresh}
                  carPriceData={carPrice.topCarPrice}
                />
              </div>
            </motion.div>
          </AnimatePresence>
        </main>
      </div>
    </div>
  );
};

export default function Stash(props: StashProps) {
  const isMobile = useMediaQuery('(max-width: 640px)');
  const { itemList = [] } = props;
  const { t, i18n } = useTranslation('', { keyPrefix: 'pages.raffle.collection' });
  const { t: TCommon } = useTranslation();

  const collections = useMemo(() => {
    return itemList.reduce(
      (acc, item: ICollectionItem) => {
        switch (Number(item.star)) {
          case StarType.Normal:
            acc[CarType.Normal].push(item);
            break;
          case StarType.Premium:
            acc[CarType.Premium].push(item);
            break;
          case StarType.Top:
            acc[CarType.Top].push(item);
            break;
        }
        return acc;
      },
      {
        [CarType.Normal]: [],
        [CarType.Premium]: [],
        [CarType.Top]: [],
      } as Record<CarType, ICollectionItem[]>,
    );
  }, [itemList]);

  const canAssemble = useMemo(() => {
    const _canAssemble = {
      [CarType.Normal]: collections[CarType.Normal].every((item) => item.amount > 0),
      [CarType.Premium]: collections[CarType.Premium].every((item) => item.amount > 0),
      [CarType.Top]: collections[CarType.Top].every((item) => item.amount > 0),
    };
    props.setCanAssemble(_canAssemble);
    return _canAssemble;
  }, [collections]);

  const counter = useRef<HTMLSpanElement>(null);
  const { start, update } = useCountUp({
    ref: counter,
    start: 0,
    end: props.userInfo.estimateIncome || 0,
    delay: 0,
    duration: 2,
  });

  useChange(props.userInfo.estimateIncome, (current, prev) => {
    if (current === undefined) return;
    if (prev === undefined) {
      start();
    } else {
      update(current);
    }
  });

  useEffect(() => {
    const parsedUrl = new URL(window.location.href);
    const element = document.getElementById('ad');
    if (parsedUrl.hash.includes('ad')) {
      element?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, []);

  return (
    <section className="md:bl-w-[1440px] bl-m-auto" id="ad">
      <div>
        <div className="bl-flex bl-justify-center bl-items-center bl-pb-6 bl-px-16 md:bl-px-6">
          <img
            src="https://static.bitlayer.org/images/user-center/raffle/collection-title.33c4eac483.png"
            alt="airdrop collection"
            className="bl-w-72 md:bl-w-[534px]"
          />
        </div>
        <div className="bl-flex md:bl-flex-row bl-flex-col md:bl-px-36 bl-px-8 bl-pt-16">
          <div className="md:bl-w-1/3 bl-text-primary bl-flex bl-justify-center md:bl-justify-start bl-items-center md:bl-items-start bl-flex-col bl-pb-8">
            <div className="bl-w-full bl-text-[#AEB5C5] bl-text-[20px] md:bl-text-[30px] bl-flex bl-justify-center md:bl-justify-start bl-items-center bl-flex-row bl-gap-2">
              <span dangerouslySetInnerHTML={{ __html: t('myEstimatedTotalAirdrop') }}></span>
              {isMobile ? (
                <Popover>
                  <PopoverTrigger>
                    <MoreInfoIcon />
                  </PopoverTrigger>
                  <PopoverContent className="bl-max-w-[230px]">
                    <div className="bl-text-[14px]">- {t('valueTips.tip1')}</div>
                    <div className="bl-text-[14px]">- {t('valueTips.tip2')}</div>
                    <div className="bl-text-[14px]">- {t('valueTips.tip3')}</div>
                  </PopoverContent>
                </Popover>
              ) : (
                <TooltipProvider delayDuration={0}>
                  <Tooltip>
                    <TooltipTrigger>
                      <MoreInfoIcon />
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="bl-w-[300px]">- {t('valueTips.tip1')}</div>
                      <div className="bl-w-[300px]">- {t('valueTips.tip2')}</div>
                      <div className="bl-w-[300px]">- {t('valueTips.tip3')}</div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <div className="bl-w-full md:bl-w-auto bl-text-primary bl-flex bl-justify-center md:bl-justify-start bl-items-center bl-flex-row bl-gap-1 bl-border-b-2 bl-border-[#5D5D5D]">
              <span className="bl-text-white bl-text-[40px] bl-font-bold">
                {props.userInfo.estimateIncome === undefined ? '--' : <span ref={counter} />}
              </span>
              <span className="bl-text-[#AEB5C5] bl-text-[20px] bl-pt-2 md:bl-pt-4">
                USD <span className="bl-text-primary">($BTR)</span>
              </span>
            </div>
            <div className="bl-w-full bl-text-[#AEB5C5] bl-text-[20px] md:bl-text-[30px] bl-flex bl-justify-center md:bl-justify-start bl-pt-5 bl-items-center bl-flex-row bl-gap-2">
              <span className="bl-text-primary bl-text-[24px]">My Cost</span>
            </div>
            <div className="bl-w-full md:bl-w-auto bl-text-primary bl-flex bl-justify-center md:bl-justify-start bl-items-center bl-flex-row bl-gap-1 bl-pb-5 md:bl-pb-0 bl-border-b-2 bl-border-[#5D5D5D]">
              <span className="bl-text-secondary bl-text-[30px] bl-font-bold">
                {props.userInfo.totalCostInUSD === undefined ? '--' : props.userInfo.totalCostInUSD}
              </span>
              <span className="bl-text-[#AEB5C5] bl-text-[20px]">USD</span>
            </div>
            <div className="bl-w-full bl-text-[#AEB5C5] bl-text-[20px] md:bl-text-[30px] bl-flex bl-justify-center md:bl-justify-start bl-pt-5 bl-items-center bl-flex-row bl-gap-2">
              <span className="bl-text-primary bl-text-[24px]">My Current ROI</span>
            </div>
            <div className="bl-w-full md:bl-w-auto bl-text-primary bl-flex bl-justify-center md:bl-justify-start bl-items-center bl-flex-row bl-gap-1">
              <span className="bl-text-secondary bl-text-[30px] bl-font-bold">
                {props.userInfo.roi === undefined ? '--' : (props.userInfo.roi * 100).toFixed(0)}%
              </span>
            </div>
          </div>
          <div className="md:bl-w-2/3 md:bl-pl-30 bl-pb-2 md:bl-pb-12 bl-w-full bl-overflow-scroll md:bl-overflow-hidden bl-justify-center">
            <ClientOnly>
              {() => (
                <RoiChart
                  roiData={props.roiData}
                  userCost={props.userInfo.totalCostInUSD}
                  fourUSD={props.userInfo.premiumAssembleUsd}
                  fiveUSD={props.userInfo.topAssembleUsd}
                />
              )}
            </ClientOnly>
          </div>
        </div>
        <div className="bl-flex bl-flex-col md:bl-hidden bl-text-[14px] bl-gap-4 bl-pt-8 bl-px-8 bl-pb-12">
          {props.userInfo.premiumAssembleUsd > 0 && (
            <div className="bl-flex bl-flex-row bl-gap-2 bl-items-center">
              <FourStarIcon width={isMobile ? 12 : 14} height={isMobile ? 12 : 14} />
              Your first<span className="bl-text-primary">4-star</span> car assembled
            </div>
          )}
          {props.userInfo.topAssembleUsd > 0 && (
            <div className="bl-flex bl-flex-row bl-gap-2 bl-items-center">
              <FiveStarIcon width={isMobile ? 12 : 14} height={isMobile ? 12 : 14} />
              Your first<span className="bl-text-primary">5-star</span> car assembled
            </div>
          )}
        </div>

        {isMobile ? (
          <MobileFeatured
            {...props}
            collections={collections}
            canAssemble={canAssemble}
            carPrice={props.carPrice}
          />
        ) : (
          <DesktopFeatured
            {...props}
            collections={collections}
            canAssemble={canAssemble}
            carPrice={props.carPrice}
          />
        )}
      </div>
    </section>
  );
}

export const DefaultCollectionData: ICollectionItem[] = [
  { categoryId: 0, itemName: 'engine', amount: 0, star: '3' },
  { categoryId: 0, itemName: 'wheels', amount: 0, star: '3' },
  { categoryId: 0, itemName: 'steeringwheel', amount: 0, star: '3' },
  { categoryId: 0, itemName: 'discbrake', amount: 0, star: '3' },
  { categoryId: 0, itemName: 'chassisrigged', amount: 0, star: '3' },
  { categoryId: 0, itemName: 'truckhitch', amount: 0, star: '3' },
  { categoryId: 0, itemName: 'seat', amount: 0, star: '3' },
  { categoryId: 0, itemName: 'gascylinder', amount: 0, star: '3' },

  { categoryId: 1, itemName: 'engine', amount: 0, star: '4' },
  { categoryId: 1, itemName: 'wheels', amount: 0, star: '4' },
  { categoryId: 1, itemName: 'steeringwheel', amount: 0, star: '4' },
  { categoryId: 1, itemName: 'discbrake', amount: 0, star: '4' },
  { categoryId: 1, itemName: 'chassisrigged', amount: 0, star: '4' },
  { categoryId: 1, itemName: 'truckhitch', amount: 0, star: '4' },
  { categoryId: 1, itemName: 'seat', amount: 0, star: '4' },
  { categoryId: 1, itemName: 'gascylinder', amount: 0, star: '4' },

  { categoryId: 2, itemName: 'engine', amount: 0, star: '5' },
  { categoryId: 2, itemName: 'wheels', amount: 0, star: '5' },
  { categoryId: 2, itemName: 'steeringwheel', amount: 0, star: '5' },
  { categoryId: 2, itemName: 'discbrake', amount: 0, star: '5' },
  { categoryId: 2, itemName: 'chassisrigged', amount: 0, star: '5' },
  { categoryId: 2, itemName: 'truckhitch', amount: 0, star: '5' },
  { categoryId: 2, itemName: 'seat', amount: 0, star: '5' },
  { categoryId: 2, itemName: 'gascylinder', amount: 0, star: '5' },
];
