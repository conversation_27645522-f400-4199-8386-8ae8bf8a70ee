import { BitlayerLogoAndText } from '@/components/icons/BitlayerLogo';
import DiscordIcon from '@/components/icons/DiscordIcon';
import { TelegramFillIcon } from '@/components/icons/TelegramIcon';
import { XFillIcon } from '@/components/icons/XIcon';
import { Button } from '@/components/ui/button';
import { AnimatePageLayout } from '@/components/ui/page';
import { Title } from '@/components/ui/title';
import { cn } from '@/lib/utils';
import { Trans, useTranslation } from 'react-i18next';

interface Partner {
  logo: string;
  link: string;
}

const partners: Partner[] = [
  {
    logo: '/images/ai-grant/ai-partner-01.png',
    link: 'https://www.dog.xyz/',
  },
  {
    logo: '/images/ai-grant/ai-partner-02.png',
    link: 'https://www.hubble.xyz/',
  },
];

const contacts = [
  {
    icon: TelegramFillIcon,
    link: 'https://t.me/bitlayerofficial',
  },
  {
    icon: XFillIcon,
    link: 'https://twitter.com/BitLayerLabs',
  },
  {
    icon: DiscordIcon,
    link: 'https://discord.gg/bitlayer',
  },
];

const docsLink = 'https://docs.bitlayer.org/docs/Build/TrackPack/AIBoostcamp';

export default function AIGrantPage() {
  const { t } = useTranslation();

  return (
    <AnimatePageLayout>
      <section className="bl-flex bl-flex-col bl-justify-center bl-items-center bl-h-screen bl-relative lg:bl-min-h-[720px]">
        <div
          className="bl-absolute bl-left-0 -bl-z-10 bl-w-screen bl-h-screen bl-bg-cover"
          style={{
            backgroundImage: 'url("/images/tracer/tracer_bg.gif")',
            backgroundPosition: 'center center',
            maskSize: '100% 100%',
            maskImage: 'linear-gradient(rgb(255, 255, 255) 73.35%, transparent 95.34%)',
          }}
        ></div>
        <Title className="bl-text-center bl-uppercase bl-text-[28px] md:bl-text-[60px] lg:bl-text-[80px]">
          <Trans
            i18nKey={'pages.aiGrant.title'}
            components={{ span: <span className="bl-text-white" /> }}
          />
        </Title>
        <div className="bl-flex bl-items-center bl-gap-2 bl-text-base bl-mt-4 lg:bl-mt-2 lg:bl-text-[32px]">
          <StarsIcon className="bl-size-5 lg:bl-size-7.5" />
          {t('pages.aiGrant.bootcamp')}
        </div>
        <div className="bl-flex bl-gap-4 bl-justify-center bl-mt-16 lg:bl-mt-20">
          <Button className="bl-w-32 bl-h-8 lg:bl-w-[192px] lg:bl-h-12" overlayFrom="none" asChild>
            <a href={docsLink} target="_blank" rel="noreferrer">
              <span className="bl-text-base lg:bl-text-xl">{t('pages.aiGrant.docs')}</span>
            </a>
          </Button>
        </div>
      </section>

      <section className="bl-flex bl-flex-col bl-items-center bl-py-8 lg:bl-pt-24 lg:bl-pb-20">
        <Title className="bl-text-center bl-uppercase bl-text-secondary bl-text-2xl md:bl-text-2xl lg:bl-text-[40px]">
          <Trans
            i18nKey="pages.aiGrant.buildOnBitlayer"
            components={{
              span: <span className="bl-text-primary" />,
            }}
          />
        </Title>
        <div className="bl-text-sm bl-mt-4 lg:bl-mt-6 lg:bl-text-2xl">
          {t('pages.aiGrant.empowerWithMCP')}
        </div>
        <img
          src="/images/ai-grant/build.png"
          alt="build"
          className="bl-hidden lg:bl-block -bl-mt-20 bl-max-w-[1384px] bl-relative -bl-z-10"
        />
        <img
          src="/images/ai-grant/build-sm.png"
          alt="build"
          className="lg:bl-hidden -bl-mt-10 bl-w-screen bl-relative -bl-z-10"
        />
      </section>

      <section className="bl-container bl-flex bl-flex-col bl-items-center bl-py-10 lg:bl-py-20">
        <Title className="bl-uppercase bl-text-secondary bl-text-2xl md:bl-text-[40px] lg:bl-text-[40px]">
          {t('pages.aiGrant.partner')}
        </Title>
        <div className="bl-flex bl-mt-8 lg:bl-mt-12">
          {partners.map((item, index) => (
            <AIPartnerItem key={index} partner={item} isFirst={index === 0} />
          ))}
        </div>
      </section>

      <section className="bl-flex bl-flex-col bl-items-center bl-py-10 lg:bl-py-20">
        <Title className="bl-uppercase bl-text-secondary bl-text-2xl md:bl-text-[40px] lg:bl-text-[40px]">
          {t('pages.aiGrant.booster')}
        </Title>
        <div className="bl-border-y bl-border-card-border bl-w-full bl-mt-8 bl-py-6 lg:bl-mt-12 lg:bl-py-8">
          <div className="bl-container bl-flex lg:bl-items-center lg:bl-justify-center bl-gap-6 lg:bl-gap-15 lg:bl-px-[140px]">
            <BitlayerLogoAndText className="bl-h-5 lg:bl-h-11" />
            <div className="bl-flex bl-flex-col bl-gap-3 lg:bl-gap-[145px] lg:bl-flex-row">
              <div className="bl-text-xs lg:bl-text-base bl-text-white">
                {t('pages.aiGrant.boosterHint')}
              </div>
              <div className="bl-flex bl-gap-3.5 lg:bl-gap-9">
                {contacts.map((item, index) => (
                  <a
                    key={index}
                    href={item.link}
                    target="_blank"
                    rel="noreferrer"
                    className="hover:bl-text-primary"
                  >
                    <item.icon className="bl-size-4 lg:bl-size-8" opacity={1} />
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    </AnimatePageLayout>
  );
}

function AIPartnerItem({ partner, isFirst }: { partner: Partner; isFirst: boolean }) {
  return (
    <a
      className={cn(
        'bl-flex bl-items-center bl-justify-center bl-border bl-border-card-border bl-w-[146px] bl-h-10 lg:bl-w-[410px] lg:bl-h-[112px]',
        {
          'bl-border-l-0': !isFirst,
        },
      )}
      href={partner.link}
      target="_blank"
      rel="noreferrer"
    >
      <img src={partner.logo} alt="partner" className="bl-h-[18px] lg:bl-h-[52px]" />
    </a>
  );
}

const StarsIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    viewBox="0 0 30 30"
    fill="none"
    {...props}
  >
    <path
      d="M18.526 7c0 5.8 4.714 10.5 10.526 10.5-5.815 0-10.526 4.702-10.526 10.5 0-5.8-4.714-10.5-10.526-10.5 5.815 0 10.526-4.702 10.526-10.5ZM7.5 4A5.5 5.5 0 0 0 13 9.5 5.5 5.5 0 0 0 7.5 15 5.5 5.5 0 0 0 2 9.5 5.5 5.5 0 0 0 7.5 4Z"
      fill="#E36E1B"
    />
  </svg>
);
