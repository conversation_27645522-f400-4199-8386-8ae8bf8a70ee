import React from 'react';
import * as zod from 'zod';
import { json, useF<PERSON>cher, useLoaderData } from '@remix-run/react';
import { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/cloudflare';
import { zodResolver } from '@hookform/resolvers/zod';
import { getValidatedFormData, useRemixForm } from 'remix-hook-form';
import { Button } from '@/components/ui/button';
import { FormProvider } from '@/components/ui/form';
import { SelectField, TextField } from '@/components/ui/form-field';
import { Text } from '@/components/ui/text';
import { Title } from '@/components/ui/title';
import BTCIcon from '@/components/icons/coins/BTCIcon';
import BRCIcon from '@/components/icons/coins/BRCIcon';
import USDTIcon from '@/components/icons/coins/USDTIcon';
import USDCIcon from '@/components/icons/coins/USDCIcon';
import { APIClientError, createAPI } from '@/lib/api';
import { cn } from '@/lib/utils';
import { Turnstile, TurnstileInstance } from '@marsidev/react-turnstile';
import { LoaderIcon } from 'lucide-react';
import { AnimatePageLayout } from '@/components/ui/page';
import { useTranslation, Trans } from 'react-i18next';
import i18nServer from '@/modules/i18n.server';
import { TFunction } from 'i18next';
import { isAddress } from 'viem';
const faucetI18nKey = 'pages.faucet';
/**
 * Prepare the form schema and resolver.
 *
 * This schema is used to validate the form data.
 */
const schema = zod.object({
  token: zod.string().min(1),
  challenge: zod.string().optional(),
  address: zod
    .string()
    .refine((value) => isAddress(value), { message: `${faucetI18nKey}.error.testAddress` }),
});

// FormData is the type of the form data.
// It is used by TypeScript to infer the type of the form data.
type FormData = zod.infer<typeof schema>;

// The resolver is used to validate the form data and return the errors.
const resolver = zodResolver(schema);

const tokenAddress: { [key: string]: string } = {
  btc: '',
  brc: '******************************************',
  usdt: '******************************************',
  usdc: '******************************************',
};

const tokens = [
  { value: 'btc', label: '0.01 BTC' },
  { value: 'brc', label: '0.01 BRC' },
  { value: 'usdt', label: '2 USDT' },
  { value: 'usdc', label: '2 USDC' },
];

const rewriteErrorMessages = (error: string, t: TFunction) => {
  if (error.includes('exceeded the rate limit')) {
    const regex = /(\d+h)?(\d+m)?(\d+s)/;
    const match = error.match(regex);
    if (match) {
      return t(`${faucetI18nKey}.error.exceededLimit`, {
        h: match[1] ?? '',
        m: match[2] ?? '',
        s: match[3] ?? '',
      });
    }
  } else if (error.includes('invalid address')) {
    return t(`${faucetI18nKey}.error.invalidAddress`);
  }
  return error;
};

/**
 * The action function is called when the form is submitted.
 */
export const action = async ({ request, context }: ActionFunctionArgs) => {
  const t = await i18nServer.getFixedT(request);
  const {
    errors,
    data,
    receivedValues: defaultValues,
  } = await getValidatedFormData<FormData>(request, resolver);
  if (!data) {
    return json({ errors, defaultValues, code: 400, message: '' });
  }

  if (!data.challenge) {
    return json({ code: -1, message: t(`${faucetI18nKey}.error.verifyCaptcha`) });
  }

  const verifyEndpoint = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';
  const secret = context.cloudflare?.env['CF_TURNSTILE_SECRET_KEY'];
  const res = await fetch(verifyEndpoint, {
    method: 'POST',
    body: new URLSearchParams({ secret, response: data.challenge }).toString(),
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  });
  const verifiedData = (await res.json()) as { success: boolean };
  if (!verifiedData.success) {
    return json({ code: -1, message: t(`${faucetI18nKey}.error.verifyCaptcha`) });
  }

  try {
    const headers: HeadersInit = {};
    const clientIp = request.headers.get('CF-Connecting-IP');
    if (clientIp) {
      headers['X-Forwarded-For'] = `${clientIp}`;
    }
    headers['Client-ID'] = context.cloudflare?.env['API_TEST_NET_CLIENT_ID'] ?? '';
    headers['Client-SECRET'] = context.cloudflare?.env['API_TEST_NET_CLIENT_SECRET'] ?? '';

    const api = createAPI(context);
    const resp = await api.faucet.claimTokens(
      {
        address: data.address,
        address20: tokenAddress[data.token],
      },
      headers,
    );
    return json({ code: 0, message: resp.msg });
  } catch (error) {
    if (error instanceof APIClientError) {
      return json({ code: error.code, message: rewriteErrorMessages(error.message, t) });
    }
    console.error('failed to claim tokens', error);
    return json({
      code: -1,
      message: t(`${faucetI18nKey}.error.unexpectedError`),
    });
  }
};

export const loader = async ({ context }: LoaderFunctionArgs) => {
  const siteKey = context.cloudflare?.env['CF_TURNSTILE_SITE_KEY'];
  return json({ siteKey });
};

const tokenIcons: { [key: string]: React.ComponentType<React.SVGProps<SVGSVGElement>> } = {
  btc: BTCIcon,
  brc: BRCIcon,
  usdt: USDTIcon,
  usdc: USDCIcon,
};

interface ExpandedButtonContentProps {
  submitted: boolean;
  tokens?: string;
  error?: string;
}

const ExpandedButtonContent = ({ tokens, submitted, error }: ExpandedButtonContentProps) => {
  const { t } = useTranslation();
  return (
    <div className="bl-text-white bl-flex bl-w-full bl-h-full bl-overflow-hidden bl-items-center bl-justify-center bl-relative">
      <div className="bl-relative bl-z-10 bl-px-4 md:bl-px-36 bl-whitespace-normal">
        {submitted ? (
          error ? (
            <Text variant="secondary">{error}</Text>
          ) : (
            <div>
              <Text>{t(`${faucetI18nKey}.result.gotTip`, { tokens })}</Text>
              <Text variant="secondary">
                {t(`${faucetI18nKey}.result.gotAnother`, { token: tokens?.split(' ')[1] })}
              </Text>
            </div>
          )
        ) : (
          <div className="bl-text-white">{t(`${faucetI18nKey}.result.sending`)}</div>
        )}
      </div>
      <img
        src="/images/bitlayer.gif"
        className="bl-h-[194px] bl-aspect-[37/33] bl-opacity-30 bl-absolute bl-top-1/2 bl-left-1/2 -bl-translate-x-1/2 -bl-translate-y-1/2"
        alt="bitlayer"
      />
    </div>
  );
};

export default function FaucetPage() {
  const pageData = useLoaderData<typeof loader>();
  const fetcher = useFetcher<typeof action>();
  const { t } = useTranslation();
  const [submitted, setSubmitted] = React.useState(false);
  const [expanded, setExpanded] = React.useState(false);
  const [expandedTokenText, setExpandedTokenText] = React.useState<string | undefined>(undefined);
  const [error, setError] = React.useState<string | undefined>(undefined);

  const [challengeStatus, setChallengeStatus] = React.useState<string>('idle');
  const [interactive, setInteractive] = React.useState<boolean>(false);
  const turnstileRef = React.useRef<TurnstileInstance>(null);
  const formRef = React.useRef<HTMLFormElement>(null);

  const form = useRemixForm<FormData>({
    fetcher,
    mode: 'onSubmit',
    resolver,
    defaultValues: {
      token: 'btc',
      address: '',
      challenge: '',
    },
  });
  const { formState } = form;
  const selectedToken = form.watch('token', 'btc');

  const tokenText = () =>
    selectedToken ? tokens.find((t) => t.value === selectedToken)?.label : '';

  React.useEffect(() => {
    if (formState.isValid && formState.isSubmitting) {
      setExpanded(true);
      setExpandedTokenText(tokenText());
    }
  }, [formState]);

  React.useEffect(() => {
    const data = fetcher.data;
    if (!data) {
      return;
    }
    setSubmitted(true);
    if (data.code !== 0) {
      setError(data.message);
    }
  }, [fetcher.data]);

  const resetForm = () => {
    form.reset(undefined, { keepDirtyValues: true, keepValues: true });
    setExpanded(false);
    setSubmitted(false);
    setError(undefined);
    form.setValue('challenge', '');
    turnstileRef.current?.reset();
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      setChallengeStatus('loading');
      turnstileRef.current?.render();
      turnstileRef.current?.execute();
      const res = await turnstileRef.current?.getResponsePromise();
      form.setValue('challenge', res);
      form.handleSubmit(e);
    } catch (error) {
      console.error(error);
    } finally {
      setChallengeStatus('idle');
    }
  };
  return (
    <AnimatePageLayout>
      <section className="bl-container bl-w-screen bl-flex bl-flex-col bl-items-center bl-justify-center bl-py-32 md:bl-py-48">
        <Title variant="secondary">Bitlayer</Title>
        <Title variant="default" className="bl-text-center bl-whitespace-wrap">
          {t(`${faucetI18nKey}.title`)}
        </Title>
        <Text
          variant="secondary"
          className="bl-max-w-3xl bl-mt-4 bl-text-center bl-w-72 sm:bl-w-full"
        >
          {t(`${faucetI18nKey}.description`)}
        </Text>

        <FormProvider {...form}>
          <fetcher.Form
            ref={formRef}
            method="post"
            onSubmit={handleSubmit}
            className="bl-w-full md:bl-w-[900px] bl-bg-background bl-border bl-border-divider bl-p-4 md:bl-p-7 bl-mt-16"
          >
            <div className="bl-w-full bl-flex bl-flex-col md:bl-flex-row bl-gap-4 bl-mb-5">
              <SelectField
                name="token"
                options={tokens}
                label={t(`${faucetI18nKey}.selectField.label`)}
                placeholder={t(`${faucetI18nKey}.selectField.placeholder`)}
                className="bl-w-full bl-h-28 md:bl-w-72 bl-min-w-0 bl-shrink-0"
                control={form.control}
                item={(option) => {
                  const Icon = tokenIcons[option.value];
                  return (
                    <div className="bl-flex bl-items-center">
                      <Icon className="bl-w-5 bl-h-5 bl-mr-2" />
                      {option.label}
                    </div>
                  );
                }}
              ></SelectField>
              <TextField
                name="address"
                label={t(`${faucetI18nKey}.textFiled.label`)}
                placeholder={t(`${faucetI18nKey}.textFiled.placeholder`)}
                className="bl-grow bl-h-28"
                inputClassName="bl-font-address placeholder:bl-font-sans"
                spellCheck={false}
                control={form.control}
              />
            </div>
            <div
              className={cn('bl-flex bl-justify-center -bl-translate-y-5 bl-duration-200', {
                'bl-h-0 bl-opacity-0': !interactive,
              })}
            >
              <Turnstile
                ref={turnstileRef}
                className="bl-bg-background"
                options={{
                  theme: 'dark',
                  refreshExpired: 'manual',
                  execution: 'execute',
                  appearance: 'interaction-only',
                }}
                siteKey={pageData.siteKey}
                onExpire={() => turnstileRef.current?.reset()}
                onBeforeInteractive={() => setInteractive(true)}
                onAfterInteractive={() => setInteractive(false)}
              />
            </div>

            <Button
              variant={expanded ? 'outline' : 'secondary'}
              className={cn(
                'bl-w-full bl-duration-500 bl-py-0 bl-transition-all disabled:bl-opacity-100 bl-mb-5',
                {
                  'bl-h-40 bl-border-primary': expanded,
                  'disabled:bl-opacity-50': challengeStatus === 'loading',
                },
              )}
              outlineClassName={cn({
                'bl-border-primary': expanded,
              })}
              disabled={expanded || challengeStatus === 'loading'}
              type="submit"
            >
              {expanded ? (
                <ExpandedButtonContent
                  submitted={submitted}
                  error={error}
                  tokens={expandedTokenText}
                />
              ) : (
                <div className="bl-flex bl-items-center bl-gap-2">
                  {challengeStatus === 'loading' && (
                    <LoaderIcon className={cn('bl-w-5 bl-h-5 bl-animate-spin')} />
                  )}
                  <span>
                    <Trans i18nKey={'common.send'} /> {tokenText()}
                  </span>
                </div>
              )}
            </Button>

            <div className="bl-w-full bl-h-fit bl-overflow-hidden bl-mb-5">
              <Button
                type="button"
                variant="secondary"
                className={cn('bl-w-full bl-duration-500 bl-transition-all bl-opacity-100', {
                  'bl-translate-y-full bl-opacity-0': !submitted,
                })}
                onClick={resetForm}
              >
                <span>
                  <Trans i18nKey={'common.return'} />
                </span>
              </Button>
            </div>
          </fetcher.Form>
        </FormProvider>
      </section>
    </AnimatePageLayout>
  );
}

export { ErrorBoundary } from '@/components/featured/error-boundary';
