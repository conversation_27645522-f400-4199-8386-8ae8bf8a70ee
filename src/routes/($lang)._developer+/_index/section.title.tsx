import { Button } from '@/components/ui/button';
import { <PERSON>Wrapper } from '@/components/ui/title';

import { NavLink } from '@/components/i18n/link';
import { LoaderIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useMediaQuery } from '@react-hook/media-query';
import { ClientOnly } from 'remix-utils/client-only';
import { cn } from '@/lib/utils';

const NavButton = ({
  to,
  name,
  isMobile,
  isOutlint,
}: {
  to: string;
  name: string;
  isMobile?: boolean;
  isOutlint?: boolean;
}) => {
  return (
    <NavLink to={to} target="_blank" rel="noreferrer">
      {({ isPending }) => (
        <Button
          variant={isOutlint ? 'outline-2' : isMobile ? 'default' : 'secondary'}
          overlayFrom={isMobile ? 'none' : undefined}
          className={cn(
            'bl-w-[153px] hover:bl-text-black md:bl-w-[269px] bl-h-9 bl-text-[15px] md:bl-h-12 md:bl-text-2xl bl-rounded-tr-sm md:bl-rounded-tr-md bl-rounded-bl-sm md:bl-rounded-bl-md',
            {
              'btn-xs': isMobile,
            },
          )}
          disabled={isPending}
        >
          <div className="bl-flex bl-items-center bl-gap-2 bl-font-body bl-font-normal">
            {isPending && !isMobile && (
              <LoaderIcon className="bl-size-4 md:bl-size-6 bl-animate-spin" />
            )}
            <span>{name}</span>
          </div>
        </Button>
      )}
    </NavLink>
  );
};

export default function TitleSection() {
  const { t } = useTranslation();
  const isMobile = useMediaQuery('only screen and (max-width: 768px)');

  return (
    <section className="bl-container bl-pt-[69px] bl-h-[493px] md:bl-h-screen md:bl-min-h-[900px] bl-w-screen bl-overflow-x-hidden">
      <div className="bl-container bl-w-full bl-h-full bl-flex bl-flex-col bl-items-center bl-justify-center">
        <div className="bl-flex bl-gap-2 md:bl-gap-3 bl-text-[90px] bl-items-center bl-justify-center bl-relative ">
          <MaskWrapper
            variant="secondary"
            className="bl-text-[42px]/[1.1] bl-font-[400] md:bl-text-[100px]/[1.1]"
            delay={0.2}
            duration={0.2}
          >
            {t('pages.home.title.the')}
          </MaskWrapper>
          <MaskWrapper
            delay={0.2}
            duration={0.2}
            variant="default"
            className="bl-text-[42px]/[1.1] bl-font-[400] md:bl-text-[100px]/[1.1] md:bl-max-w-5xl xl:bl-max-w-6xl xl:bl-whitespace-nowrap"
          >
            {t('pages.home.title.first')}
          </MaskWrapper>
          <MaskWrapper
            delay={0.2}
            duration={0.2}
            variant="secondary"
            className="bl-text-[42px]/[1.1] md:bl-text-[100px]/[1.1] bl-font-[400]"
          >
            {t('pages.home.title.bitvm')}
          </MaskWrapper>
        </div>
        <p className="bl-text-secondary bl-text-center bl-w-full bl-text-lg md:bl-text-[35px] bl-mt-3">
          {t('pages.home.title.powering')}
        </p>
        <ClientOnly>
          {() => (
            <div className="bl-grid bl-pt-5 md:bl-pt-9 bl-grid-cols-2 bl-gap-4 lg:bl-gap-5 bl-mt-7 md:bl-mt-[45px] bl-bottom-20 bl-justify-items-center">
              <NavButton
                to="https://finality.bitlayer.org/testnet/bridge/mint"
                name={t('pages.home.title.explore')}
                isMobile={isMobile}
              />
              <div className="bl-relative bl-group">
                <NavButton
                  to="/flash-bridge"
                  name={t('pages.home.title.btrBridge')}
                  isMobile={isMobile}
                  isOutlint
                />
                <div className="bl-w-full bl-h-8 bl-invisible md:group-hover:bl-visible bl-text-center bl-pt-2 bl-bottom-0">
                  {t('pages.home.title.extra')}
                </div>
              </div>
            </div>
          )}
        </ClientOnly>
        <div className="bl-text-center bl-text-xl -bl-mt-2 md:bl-mt-1">
          <a
            rel="noreferrer"
            className="bl-border-b bl-border-dashed"
            href="https://static.bitlayer.org/Bitlayer-Technical-Whitepaper.pdf"
            target="_blank"
          >
            {t('navigation.links.whitepaper')}
          </a>
        </div>
      </div>
    </section>
  );
}
