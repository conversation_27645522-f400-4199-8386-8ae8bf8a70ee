import React from 'react';
import { useTranslation } from 'react-i18next';
import { Carousel, CarouselApi, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import { ScrambleSectionBadge } from '@/components/ui/section-badge';
import { Title } from '@/components/ui/title';
import { useMotionValueEvent, useScroll } from 'framer-motion';
import useChange from '@react-hook/change';
import { cn } from '@/lib/utils';
import { NetworkRoadmapSection } from './section.roadmap';
import { use100vh } from '@/hooks/100vh';
import { Button } from '@/components/ui/button';

import {
  CarouselDock,
  Description,
  LearnMoreButton,
  MainTitle,
  SubTitle,
  useConditionalYSnap,
} from './section-bitvm-bridge';
import { staticAsset } from '@/lib/static';
import { ClientOnly } from 'remix-utils/client-only';
import { useMediaQuery } from '@react-hook/media-query';

export function BitlayerNetworkSection() {
  const isMobile = useMediaQuery('(max-width: 640px)');

  return (
    <>
      <ClientOnly>
        {() => (isMobile ? <BitlayerNetworkMobile /> : <BitlayerNetworkDesktop />)}
      </ClientOnly>
      <NetworkRoadmapSection />
    </>
  );
}

function BitlayerNetworkMobile() {
  const { t } = useTranslation();
  const key = 'pages.home.bitlayerNetwork';
  const ref = React.useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({ target: ref, offset: ['start end', 'end end'] });
  const [offset, setOffset] = React.useState(0);

  const [api, setApi] = React.useState<CarouselApi>();

  useMotionValueEvent(scrollYProgress, 'change', (latest) => {
    // console.log('scrollYProgress', latest);
    if (0 < latest && latest <= 0.34) {
      setOffset(0);
    } else if (latest > 0.34 && latest <= 0.67) {
      setOffset(1);
    } else if (latest > 0.67 && latest <= 1) {
      setOffset(2);
    }
  });

  useChange(offset, (current) => {
    console.log('offset', current);
    api?.scrollTo(current);
  });

  // useConditionalYSnap(ref, {
  //   topMultiplier: 0.3,
  //   bottomMultiplier: 0.2,
  // });

  const handleDockButtonSelect = (i: number) => {
    const p = ref.current?.children.item(i);
    p?.scrollIntoView({ behavior: 'instant' });
  };

  const debug = false;

  const items = [
    {
      title: `${key}.items.0.title`,
      subtitle: `${key}.items.0.subtitle`,
      text: `${key}.items.0.text`,
      containerClassName: 'bl-items-center',
      image: '/images/home/<USER>/bitlayer-network-m-01.20240525.png',
      imageClassName: 'bl-w-full',
      bg: staticAsset('/images/home/<USER>'),
      learnMore: 'https://docs.bitlayer.org/docs/Learn/Bitlayer%20Rollup/overview',
    },
    {
      title: `${key}.items.1.title`,
      subtitle: `${key}.items.1.subtitle`,
      text: `${key}.items.1.text`,
      containerClassName: 'bl-flex-row-reverse bl-items-center bl-gap-16',
      image: '/images/home/<USER>/bitlayer-network-m-02.20240525.png',
      imageClassName: 'bl-w-full',
      learnMore: 'https://docs.bitlayer.org/docs/BitVMBridge/overview',
      bg: staticAsset('/images/home/<USER>'),
    },
    {
      title: `${key}.items.2.title`,
      subtitle: `${key}.items.2.subtitle`,
      text: `${key}.items.2.text`,
      containerClassName: 'bl-items-center',
      image: '/images/home/<USER>/bitlayer-network-m-03.20240525.png',
      imageClassName: 'bl-h-[320px]',
      bg: staticAsset('/images/home/<USER>'),
      imageWrapper: (children: React.ReactNode) => (
        <div>
          <div className="bl-w-full bl-flex bl-gap-15 bl-text-xl bl-font-extralight bl-relative bl-left-40 bl-mb-16">
            <p className="bl-w-80">{t('pages.home.innovations.items.2.imageDesc1')}</p>
            <p className="bl-w-80">{t('pages.home.innovations.items.2.imageDesc2')}</p>
          </div>
          {children}
        </div>
      ),
    },
  ];

  const height = use100vh();

  return (
    <section id="bitlayer-network" className="">
      <div className="bl-w-full bl-pt-30 bl-pb-9 bl-flex bl-flex-col bl-items-center bl-bg-black">
        <ScrambleSectionBadge text={t(`${key}.bitlayerNetwork`)}></ScrambleSectionBadge>
        <Title
          variant="secondary"
          className="bl-text-[36px]/[1.2] bl-uppercase bl-w-60 md:bl-w-auto md:bl-text-[60px]/[1.2] bl-mt-8 bl-text-center bl-max-w-4xl"
        >
          {t(`${key}.title`)}
        </Title>
      </div>
      <div
        className="bl-w-full bl-top-0 bl-sticky"
        style={{
          height,
        }}
      >
        <Carousel
          setApi={setApi}
          opts={{
            watchDrag: false,
          }}
          orientation="vertical"
          className="bl-relative bl-border-b bl-border-[#393D44]"
        >
          <CarouselContent
            className="bl-size-full bl-ml-0 bl-mt-0 bl-flex-col"
            style={{
              height,
            }}
          >
            {items.map((item, index) => (
              <CarouselItem className="bl-basis-full bl-pl-0 bl-pt-0" key={index}>
                <div
                  className={cn(
                    'bl-size-full bl-px-10 bl-flex bl-flex-col bl-items-center bl-relative',
                    {
                      'bl-bg-black': !debug,
                    },
                  )}
                >
                  <img
                    src={item.bg}
                    alt="bg"
                    className="bl-w-full bl-h-full bl-object-cover bl-absolute bl-opacity-10"
                  />
                  <div
                    className={cn(
                      'bl-size-full bl-flex bl-flex-col bl-gap-6 bl-items-center bl-justify-center bl-relative bl-z-10',
                    )}
                  >
                    <div className="bl-items-center bl-text-center">
                      <MainTitle>{t(item.title)}</MainTitle>
                      <SubTitle>{t(item.subtitle)}</SubTitle>
                      <Description>{t(item.text)}</Description>
                    </div>
                    <img src={item.image} alt="chart" className={item.imageClassName} />
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselDock
            className="bl-absolute bl-top-1/3 bl-left-3"
            n={3}
            onSelect={handleDockButtonSelect}
          />
        </Carousel>
      </div>
      <div className="bl-w-screen" ref={ref}>
        <div
          className={cn('bl-w-full bl-min-h-[638px] bl-snap-start bl-snap-always', {
            'bl-bg-red-950': debug,
          })}
          style={{
            height,
          }}
        ></div>
        <div
          className={cn('bl-w-full bl-min-h-[638px] bl-snap-start bl-snap-always', {
            'bl-bg-green-950': debug,
          })}
          style={{
            height,
          }}
        ></div>
        <div
          className={cn('bl-w-full bl-min-h-[638px] bl-snap-start bl-snap-always', {
            'bl-bg-yellow-950': debug,
          })}
          style={{
            height,
          }}
        ></div>
      </div>
    </section>
  );
}

function BitlayerNetworkDesktop() {
  const { t } = useTranslation();
  const key = 'pages.home.bitlayerNetwork';
  const ref = React.useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({ target: ref, offset: ['start end', 'end end'] });
  const [offset, setOffset] = React.useState(0);

  const [api, setApi] = React.useState<CarouselApi>();

  useMotionValueEvent(scrollYProgress, 'change', (latest) => {
    // console.log('scrollYProgress', latest);
    if (0 < latest && latest <= 0.34) {
      setOffset(0);
    } else if (latest > 0.34 && latest <= 0.67) {
      setOffset(1);
    } else if (latest > 0.67 && latest <= 1) {
      setOffset(2);
    }
  });

  useChange(offset, (current) => {
    console.log('offset', current);
    api?.scrollTo(current);
  });

  useConditionalYSnap(ref);

  const handleDockButtonSelect = (i: number) => {
    const p = ref.current?.children.item(i);
    p?.scrollIntoView({ behavior: 'instant' });
  };

  const debug = false;

  const items = [
    {
      title: `${key}.items.0.title`,
      subtitle: `${key}.items.0.subtitle`,
      text: `${key}.items.0.text`,
      containerClassName: 'bl-items-center',
      image: '/images/home/<USER>/bitlayer-network-01.20250425.png',
      imageClassName: 'bl-h-[394px] 3xl:bl-h-[440px] 3xl:bl-ml-[100px]',
      bg: staticAsset('/images/home/<USER>'),
      learnMore: (
        <div className="bl-flex bl-gap-3">
          <LearnMoreButton href="https://docs.bitlayer.org/docs/Learn/Bitlayer%20Rollup/overview" />
          <Button
            variant="secondary"
            asChild
            className={cn(
              'bl-text-base bl-w-30 bl-h-9 md:bl-w-60 md:bl-h-[50px] md:bl-text-2xl bl-rounded-tr-sm md:bl-rounded-tr-md bl-rounded-bl-sm md:bl-rounded-bl-md',
            )}
          >
            <a
              href="https://static.bitlayer.org/Bitlayer-Technical-Whitepaper.pdf"
              target="_blank"
              rel="noreferrer"
            >
              <span>{t('navigation.links.whitepaper')}</span>
            </a>
          </Button>
        </div>
      ),
    },
    {
      title: `${key}.items.1.title`,
      subtitle: `${key}.items.1.subtitle`,
      text: `${key}.items.1.text`,
      containerClassName: 'bl-flex-row-reverse bl-items-center bl-gap-16',
      image: '/images/home/<USER>/bitlayer-network-02.20250425.png',
      imageClassName: 'bl-h-[370px] 3xl:bl-mr-20',
      bg: staticAsset('/images/home/<USER>'),
      learnMore: 'https://docs.bitlayer.org/docs/BitVMBridge/overview',
    },
    {
      title: `${key}.items.2.title`,
      subtitle: `${key}.items.2.subtitle`,
      text: `${key}.items.2.text`,
      containerClassName: 'bl-items-center',
      image: '/images/home/<USER>/bitlayer-network-03.20250425.png',
      imageClassName: 'bl-h-[260px] 3xl:bl-h-[300px]',
      bg: staticAsset('/images/home/<USER>'),
      imageWrapper: (children: React.ReactNode) => (
        <div>
          <div className="bl-w-full bl-flex bl-gap-15 bl-text-xl bl-font-extralight bl-relative bl-left-20 3xl:bl-left-[110px] bl-mb-4">
            <p className="bl-w-80">{t('pages.home.innovations.items.2.imageDesc1')}</p>
            <p className="bl-w-80">{t('pages.home.innovations.items.2.imageDesc2')}</p>
          </div>
          {children}
        </div>
      ),
    },
  ];

  return (
    <section id="bitlayer-network">
      <div className="bl-w-full bl-h-screen bl-top-0 bl-sticky">
        <div className="bl-w-full bl-pt-30 bl-pb-0 bl-flex bl-flex-col bl-items-center md:bl-justify-end md:bl-pb-10 md:bl-h-[360px] bl-bg-black">
          <ScrambleSectionBadge text={t(`${key}.bitlayerNetwork`)}></ScrambleSectionBadge>
          <Title
            variant="secondary"
            className="bl-text-[36px]/[1.2] bl-uppercase md:bl-text-[60px]/[1.2] bl-mt-8 bl-text-center bl-max-w-4xl"
          >
            {t(`${key}.title`)}
          </Title>
        </div>
        <Carousel
          opts={{
            watchDrag: false,
          }}
          setApi={setApi}
          orientation="vertical"
          className="bl-relative"
        >
          <CarouselContent className="bl-size-full bl-h-[calc(100vh-360px)] bl-ml-0 bl-mt-0 bl-flex-col">
            {items.map((item, index) => (
              <CarouselItem className="bl-basis-full bl-pl-0 bl-pt-0" key={index}>
                <div
                  className={cn('bl-size-full bl-flex bl-items-center bl-relative', {
                    'bl-bg-black': !debug,
                  })}
                >
                  <img
                    src={item.bg}
                    alt="bg"
                    className="bl-hidden md:bl-block bl-w-full bl-h-full bl-object-cover bl-absolute bl-opacity-10"
                  />
                  <div
                    className={cn(
                      'bl-w-full bl-flex bl-gap-6 bl-justify-center bl-items-center bl-relative bl-z-10',
                      item.containerClassName,
                    )}
                  >
                    <div className="bl-w-[580px]">
                      <MainTitle>{t(item.title)}</MainTitle>
                      <SubTitle>{t(item.subtitle)}</SubTitle>
                      <Description>{t(item.text)}</Description>
                      {item.learnMore && typeof item.learnMore == 'string' ? (
                        <LearnMoreButton href={item.learnMore} />
                      ) : (
                        item.learnMore
                      )}
                    </div>
                    {item.imageWrapper ? (
                      item.imageWrapper(
                        <img src={item.image} alt="chart" className={item.imageClassName} />,
                      )
                    ) : (
                      <img src={item.image} alt="chart" className={item.imageClassName} />
                    )}
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselDock
            className="bl-absolute bl-top-1/2 -translate-y-1/2 bl-right-8"
            n={3}
            onSelect={handleDockButtonSelect}
          />
        </Carousel>
      </div>
      <div className="bl-w-screen" ref={ref}>
        <div
          className={cn('bl-w-full bl-h-screen bl-min-h-[638px] bl-snap-start bl-snap-always', {
            'bl-bg-red-950': debug,
          })}
        ></div>
        <div
          className={cn('bl-w-full bl-h-screen bl-min-h-[638px] bl-snap-start bl-snap-always', {
            'bl-bg-green-950': debug,
          })}
        ></div>
        <div
          className={cn('bl-w-full bl-h-screen bl-min-h-[638px] bl-snap-start bl-snap-always', {
            'bl-bg-yellow-950': debug,
          })}
        ></div>
      </div>
    </section>
  );
}
