export default {
  title: 'Bitlayer',
  meta: {
    description: 'Первая сеть уровня 2 на BitVM, которая так же безопасна, как и Bitcoin.',
  },
  common: {
    readMore: 'Прочитать подробности',
    learnMore: 'Подробнее',
    upload: 'Загрузить',
    submit: 'Отправить',
    return: 'Вернуться',
    send: 'Отправить',
    back: 'Назад',
    coming: 'Скоро',
    connect: 'Связать кошелек',
    disconnect: 'Отключение',
    switchChain: 'Switch to {{chainName}}',
    insufficientBalance: 'Недостаточный баланс',
    search: 'поиск',
    toTop: 'Наверх',
    getGas: 'Get Bitlayer Gas',
    bridge: 'Мост+доход',
    connectDesc: 'Для перевода токенов подключите свой кошелек',
    MesonDes: 'Надежный, бесплатный и быстрый кроссчейн-протокол для обмена стейблкоинов',
    OwltoDes: 'Owlto Finance — это интероперабельный протокол с ориентацией на намерения',
    OrbiterDes: 'Orbiter Finance — это децентрализованный кроссроллап-мост',
    BoolBridgeDes: 'Bool Bridge is a decentralized bridge that connects Bitcoin and Layer 2.',
    OmnityBridgeDes:
      'Omnity — это простой в использовании мост для держателей Bitcoin-токенов, полностью работающий на блокчейне.',
    login: 'Вход',
    noData: 'Данных нет',
    confirm: 'Подтвердить',
    Ok: 'OK',
    cancel: 'Отмена',
    reject: 'Reject',
    website: 'Сайт',
    share: 'Поделиться',
    more: 'More',
    less: 'Less',
    flash: 'Flash Bridge-In',
    getIt: 'Получить',
    congratulations: 'Поздравляем!',
    error: {
      gasLess: 'Недостаточно средств на газ для этой транзакции',
      transactionFailed: 'Блокчейн-транзакция не прошла',
    },
  },
  resources: {
    github: 'https://github.com/bitlayer-org',
    discord: 'https://discord.gg/bitlayer',
    x: 'https://twitter.com/BitLayerLabs',
    linkedin: 'https://bit.ly/42B6v15',
    telegram: 'https://t.me/bitlayerofficial',
    medium: 'https://medium.com/@Bitlayer',
    rankRule:
      'https://medium.com/@Bitlayer/bitlayer-provides-20m-airdrop-for-dapp-leaderboard-competition-rewarding-ecosystem-projects-and-87ed3dc76b94',
  },
  navigation: {
    links: {
      opvm: 'OpVM',
      developers: 'Создатели',
      users: 'Участники',
      userCenter: 'Центр гонщика',
      resources: 'Ресурсы',
      contact: 'Связаться с нами',
      mainnet: 'Основная сеть',
      bridgeEarn: 'Bridge & Earn',
      usdcChange: 'Изменение USDC',
      gas: 'Газ',
      startBuilding: 'Начать создавать',
      btrScan: 'Bitlayer(BTR) Scan',
      addMainnet: 'Add Bitlayer Mainnet',
      testnet: 'Тестовая сеть',
      faucet: 'Кран',
      testnetBridge: 'Мост к тестовой сети',
      testnetBridgeHint: 'Мост для сети Bitlayer Testnet',
      testnetScan: 'Сканер тестсети',
      addTestnet: 'Add Bitlayer Testnet',
      language: 'Язык',
      getGas: 'Get Bitlayer Gas',
      readyPlayerOne: 'Ready Player One',
      luckyHelmet: 'Шлем удачи',
      dAppsLeaderboard: 'DApps Leaderboard',
      hint1: 'Эирдроп 50 000 000 долларов США',
      hint2: 'Жмите на газ с Bitlayer',
      hint3: 'Проголосуйте за проект, который вам нравится',
      brandKit: 'Фирменный набор',
      whitepaper: 'Белая книга 2.0',
      leaderboard: 'Лидеры',
      dappCenter: 'Центр Dapp',
      dappCenterHint: 'Рейтинг Dapp Bitlayer',
      miningGala: 'Праздник майнинга',
      bridge: 'Мост+доход',
      flash: 'Flash Bridge-In',
      developersSection: {
        hint: 'Start building on Bitlayer chain',
        documentation: 'Документация',
        documentationHint: 'Supporting docs for developers',
        tools: {
          title: 'Инструменты Boost',
          hint: 'Developer tools for your projects',
          mainnetScan: 'Сканирование сети Mainnet',
          mainnetScanHint: 'Bitlayer(BTR) Mainnet Scan',
          testnetScan: 'Сканирование сети Testnet',
          testnetScanHint: 'Bitlayer(BTR) Testnet Scan',
          faucet: 'Кран',
          faucetHint: 'Токены Pilot на чейне Bitlayer',
          theGraph: 'The Graph',
          theGraphHint: 'Index and search your data on Bitlayer',
          multisigWallet: 'Кошелек Multisig',
          multisigWalletHint: 'Store your private keys, safe and easy',
        },
        security: {
          title: 'Безопасность',
          hint: 'Поддержка безопасности',
          dappSecurityManual: 'Руководство по обеспечению безопасности Dapp',
          dappSecurityManualHint: 'Пособие по созданию безопасного Dapp',
          auditAlliance: 'Аудиторский альянс',
          auditAllianceHint: 'Аудиторский альянс',
          securityNetwork: 'Сеть безопасности',
          securityNetworkHint: 'Сеть безопасности',
        },
        developerSupport: {
          title: 'Operation Supports',
          hint: 'Grants, funding, incentive and supports',
          readyPlayerOne: 'Ready player I',
          readyPlayerOneHint: 'Торгуйте и выигрывайте призовой пул до $100 000',
          luckyHelmet: 'Lucky Helmet',
          luckyHelmetHint: 'Разблокируйте $50 000 000 для ранних строителей',
          miningGala: 'Mining Gala',
          miningGalaHint: 'Выиграйте вознаграждения Bitlayer и аирдропы dapp',
          leaderboard: 'Таблица лидеров Leaderboard',
          leaderboardHint: 'Таблица лидеров Leaderboard Bitlayer на конкурсе эпохи 1',
        },
        hackathon: 'Hackathon',
        hackathonHint: 'Вспомогательная документация для разработчиков',
        github: 'Github',
        githubHint: 'Explore Bitlayer tech repositories',
        devCommunities: 'Сообщества разработчиков',
        devCommunitiesHint: 'Join developers chats',
        telegram: 'Telegram',
        opvm: 'OpVM',
        opvmHint: 'Make Bitcoin verifiable',
      },
    },
    footer: {
      Participate: 'Участвовать',
      ReadyPlayerOne: 'Ready Player One',
      Build: 'Создать',
      BuildingBitlayer: 'Создание на основе Bitlayer',
      Faucet: 'Кран',
      SupportedWallets: 'Подходящие кошельки',
      TheGraph: 'The Graph',
      MainScan: 'Bitlayer(BTR) Scan',
      TestScan: 'Сканер тестсети',
      About: 'Информация',
      Solutions: 'Решения',
      Roadmap: 'Дорожная карта',
      EventNews: 'Новости и мероприятия',
      Jobs: 'Вакансии',
      Community: 'Сообщество',
      blog: 'Блог',
      Podcast: 'Подкаст',
      Operation: 'Операция',
    },
    walletDrawer: {
      wrongNet: 'Switch to {{chainName}}',
      noNFT: 'Сбора нет',
    },
  },
  pages: {
    home: {
      title: {
        notice:
          'Программа «Первому игроку приготовиться» от Bitlayer началась! Голосуйте и претендуйте на очки и включение в белые списки!',
        slogon1: 'Расширяйте',
        slogon2: 'Возможности',
        slogon3: 'Bitcoin',
        startBuilding: 'Начать создавать',
        bridgeEarn: 'Мост+доход',
        bridge: 'Мост',
        competition: 'Bitlayer Ready Player I Competition Stage',
        dapp100:
          "Earn Rewards from <span class='bl-text-xs md:bl-text-2xl bl-text-primary bl-font-[600]'>100+</span> Dapps!",
        gasFee: 'Медианная газовая комиссия:',
        whitePaper: 'Bitlayer White Paper',
        leaderboard:
          "Проверьте прямо сейчас, можете ли вы претендовать на <span class='bl-text-xs md:bl-text-2xl bl-text-white bl-font-[600] bl-px-1'>эирдроп алмазов</span>!",
      },
      spotlight: {
        spotlight: 'Spotlight',
        readMore: 'Подробнее',
        carousel: {
          item1: {
            type: 'Блог',
            title: 'OpVM',
            content: 'Современный уровень верификации биткоина',
          },
          item2: {
            type: 'Оффлайн-мероприятие',
            title: 'Bitcoin Bar',
            content:
              'Приготовьтесь погрузиться в захватывающий мир криптовалют на Bitcoin BAR, организованном Bitlayer и Pizza Ninjas!',
          },
          item3: {
            type: 'Оффлайн-мероприятие',
            title: 'VIP-ужин Bitcoin Next',
            content:
              'Эксклюзивный званый ужин, организованный Bitlayer в рамках недели KBW, приглашаем зарегистрироваться!',
          },
          item4: {
            type: 'Оффлайн-мероприятие',
            title: 'Bitlayer Formula Night',
            content:
              'Bitlayer проводит Bitlayer Night во время недели Token 2049 в Сингапуре, приглашаем зарегистрироваться!',
          },
          item5: {
            type: 'Аирдроп-мероприятие',
            title: 'Bitlayer DApp Center',
            content:
              'Отпразднуйте запуск Bitlayer DApp Center эксклюзивным аирдропом до 100 000 000 очков Bitlayer.',
          },
          item6: {
            type: 'Аирдроп-мероприятие',
            title: 'Bitlayer Racer Center',
            content:
              'Bitlayer Racer Center теперь открыт! Выполняйте задания и мини-игры, чтобы заработать драгоценные камни и очки Bitlayer!',
          },
        },
      },
      liveData: {
        liveData: 'Оперативные данные',
        explore: 'изучить',
        'the community': 'сообщество',
        'of bitlayer': 'bitlayer',
      },
      solutions: {
        solutions: 'Решения',
        title: 'Будущее уровня 2 для Bitcoin должно быть таким',
        subtitle: 'Ключевые решения, которые определят будущее экосистемы Bitcoin',
        items: [
          {
            title: 'Верификация на уровне 1',
            text: 'Наследует безопасность Bitcoin благодаря BitVM',
          },
          {
            title: 'Бездоверительная двусторонняя привязка',
            text: 'Революционные модели, сочетающие DLC и BitVM, превосходят традиционную мультиподпись',
          },
          {
            title: 'Полнота по Тьюрингу',
            text: 'Поддержка разных виртуальных машин и абсолютная EVM-совместимость',
          },
        ],
        whitepaper: {
          title: 'ЗАГРУЗИТЬ ВАЙТПЕЙПЕР',
          button: 'ВайтПейпер',
        },
      },
      innovations: {
        innovations: 'Инновации',
        items: [
          {
            title: 'БЕЗОПАСНОСТЬ БИТКОИНА',
            subtitle: '- OpVM -',
            text: 'OpVM – это бездоверительный сервис финализации, помогающий блокчейн-приложениям завершать работу с биткоином',
          },
          {
            title: 'БЕЗДОВЕРИТЕЛЬНЫЙ МОСТ',
            subtitle: '- Мост финализации -',
            text: 'Мост финализации – это Bitcoin-мост с минимальным уровнем доверия и режимом front-and-reimberse, работающий на базе сервиса OpVM.',
            imageDesc:
              'В сочетании с атомарным свопом BTC можно перемещать между Bitcoin и L2s без доверия, заменяя старый способ multisig',
          },
          {
            title: 'REALTIME EVM',
            subtitle: '- RtEVM -',
            text: 'Благодаря параллельному исполнению и быстрому доступу к состоянию RtEVM обеспечивает сверхвысокую пропускную способность и подтверждение транзакций практически в режиме реального времени.',
            imageDesc1:
              'Планировщик оптимально распределяет транзакции по нескольким исполнителям EVM, полностью используя многопоточность',
            imageDesc2:
              'Присоединение разделенных KV-хранилищ к масштабируемому MPT-дереву обеспечивает неограниченную емкость и чрезвычайно быстрый доступ к состоянию',
          },
        ],
      },
      roadmap: {
        roadmap: 'Дорожная карта',
        title: 'Путь Bitcoin',
        title2: 'Путь Bitlayer',
        points: [
          {
            title: 'Colored Coins',
            description:
              'Технология Colored Coins позволяет маркировать токены в блокчейне, чтобы закреплять за ними реальные активы для прозрачного и безопасного управления.',
            date: '2012',
          },
          {
            title: 'Counterparty',
            description:
              'Counterparty дает возможность добавлять в блокчейн Bitcoin собственные токены и смарт-контракты, чтобы создавать цифровые активы и торговать ими.',
            date: '2014',
          },
          {
            title: 'Spells of Genesis',
            description:
              'Spells of Genesis — это мобильная игра на блокчейне с коллекционными карточками и элементами аркады в фэнтезийном мире.',
            date: '2015',
          },
          {
            title: 'Rare PEPE',
            description:
              'В блокчейне Bitcoin создаются уникальные произведения криптоискусства — цифровые карточки с лягушонком Пепе, которые можно коллекционировать и обменивать.',
            date: '2016',
          },
          {
            title: 'Обновление SegWit',
            description:
              'Протокольное обновление Segregated Witness отделяет данные подписей от данных транзакций, тем самым повышая вместимость блока и масштабируемость блокчейна.',
            date: '2017',
          },
          {
            title: 'Обновление Taproot',
            description:
              'Обновление протокола Bitcoin, получившее название Taproot, повышает конфиденциальность и возможности смарт-контрактов благодаря новой фирменной схеме подписей Шнорра.',
            date: '2021',
          },
          {
            title: 'Ordinals',
            description:
              'Протокол Ordinals записывает уникальные цифровые артефакты на определенные Satoshi в блокчейне Bitcoin — и они работают наподобие NFT.',
            date: '2023',
          },
        ],
        journeys: [
          {
            title: 'Осн. сеть, V1',
            texts: [
              'Оптимальная модель безопасной регистрации пользователей и разработчиков: PoS-сайдчейн + MPC-TSS/встроенная мультиподпись',
            ],
            subtitle: '04/2024',
          },
          {
            title: 'Осн. сеть, V2',
            texts: [
              'Преобразование в модель, эквивалентную роллапам: революционная модель безопасности для перевода активов по кроссчейн-мосту. Тщательная верификация уровня 1 без BitVM',
              'Мост финализации',
            ],
            subtitle: '09/2024',
          },
          {
            title: 'Осн. сеть, V3',
            texts: [
              'Полная картина: Тщательная верификация на уровне 1 со схемой BitVM, эквивалентная Bitcoin по безопасности',
              'Realtime EVM',
            ],
            subtitle: '06/2025',
          },
        ],
      },
      events: {
        tag: 'Новости и мероприятия',
        title: 'Новости и мероприятия экосистемы Bitlayer',
        subtitle: 'Следите за нашими новостями',
        items: [
          'Bitcoin 2024: Нашвилл',
          'Токен 2049',
          'Вечер Bitlayer в Корее',
          'Bitcoin: Сингапур',
        ],
      },
      jobs: {
        tag: 'Вакансии',
        title: 'Открытые вакансии',
        subtitle: 'Давайте творить историю Bitcoin вместе!',
        positions: [
          {
            title: 'Глава отдела отношений с разработчиками_',
            skills: [
              'Расширение и поддержание отношений с разработчиками в публичной экосистеме блокчейна.',
              'Организация технической коммуникации и образования разработчиков в разных международных технических сообществах и экосистемах разработки.',
            ],
          },
          {
            title: 'Специалист по исследованию протоколов_',
            skills: [
              'Проведение передовых исследований в сфере блокчейна для решения проблем в отрасли.',
              'Написание качественных исследовательских отчетов и научных работ.',
            ],
          },
        ],
        more: 'Подробнее о вакансиях',
      },
      community: {
        tag: 'Сообщество',
        title: 'Cтаньте частью сообщества будущего',
        subtitle: 'Действуйте вместе с Bitlayer',
        items: [
          {
            title: 'Bнесите вклад в Bitlayer',
            description: 'Создавайте вместе с нами',
          },
          {
            title: 'Присоединяйтесь к нам в Discord',
            description: 'Давайте общаться',
          },
          {
            title: 'Подпишитесь на нас в X',
            description: 'Давайте взаимодействовать',
          },
          {
            title: 'Добавляйтесь в LinkedIn',
            description: 'Оставайтесь на связи',
          },
          {
            title: 'Присоединяйтесь к нам в Telegram',
            description: 'Беседуйте с нами',
          },
          {
            title: 'Узнавайте новое через Medium',
            description: 'Будьте в курсе событий',
          },
        ],
      },
      investors: {
        investors: 'Наши инвесторы',
        'Co-lead': 'СОРУКОВОДИТЕЛЬ',
      },
      ecosystem: {
        ecosystem: 'Экосистема',
        top: 'Топ',
        Dapps: 'Dapps',
        on: 'на',
        Bitlayer: 'Bitlayer',
        'Dapp Center': 'Dapp Center',
        lending: 'Лендинг',
        dex: 'DEX',
        staking: 'Стейкинг',
        gaming: 'Гейминг',
        'stable coins': 'Стейблкоины',
      },
    },
    developers: {
      title: {
        titleLine1: 'Приветствуем вас в разделе',
        titleLine2: 'документов Bitlayer',
        subtitle: 'От создателей для создателей: узнайте, как присоединиться к экосистеме Bitlayer',
        buildNow: 'Build Now',
        blog: 'Блог',
      },
      faq: {
        title: 'Частые вопросы',
        name1: 'Что такое Bitlayer?',
        desc1:
          'Bitlayer — это решение для Bitcoin уровня 2, полностью совместимое с EVM и инструментарием Ethereum. Нативный токен сети Bitlayer — BTC (это газовый токен). Приложения и разработчики могут мигрировать в Bitlayer из существующих экосистем на базе Ethereum с минимальными затратами. Для этого не требуется существенно модифицировать или переписывать код.',
        name2: 'Совместима ли сеть Bitlayer с существующими Bitcoin-кошельками?',
        desc2:
          'Да, сеть Bitlayer совместима с Metamask, Unisat и другими кошельками для Bitcoin или Ethereum, поэтому ее пользователи могут без проблем распоряжаться своими денежными средствами и активами.',
        name3: 'Могут ли разработчики осуществлять миграцию своих проектов в Bitlayer?',
        desc3:
          'Да, благодаря совместимости с EVM разработчики могут без проблем и больших затрат переносить и развертывать в Bitlayer уже существующие проекты. Доступна удобная миграция смарт-контрактов, написанных с помощью Solidity, Vyper или любого другого языка, который компилируется в байт-код EVM, непосредственно в Bitlayer с использованием знакомого вам инструментария: Ethereum JSON-RPC, Hardhat и т. д.',
        name4: 'Как поддержать Bitlayer?',
        desc4: `Поддерживать Bitlayer можно несколькими способами. Вы можете активно участвовать в обсуждениях сообщества, делиться отзывами и предложениями, присоединиться к разработке приложений и инструментов на платформе или рассказывать о сети Bitlayer людям, которым могут пригодиться ее возможности. Кроме того, вас могут заинтересовать определенные инициативы и программы поддержки, которые действуют в Bitlayer. Ознакомьтесь с программой <span data-link-index='0' class='bl-underline bl-cursor-pointer'>«Первому игроку приготовиться»</span> с поощрительным фондом для первых создателей и участников в размере 50 000 000 долларов США.`,
      },
      feature: {
        title: 'В чем наша сила',
        title1: 'Полнота по Тьюрингу',
        desc1: 'Поддержка разных виртуальных машин и абсолютная EVM-совместимость',
        title2: 'Бездоверительная двусторонняя привязка',
        desc2:
          'Революционные модели, сочетающие DLC и BitVM, превосходят традиционную мультиподпись',
        title3: 'Верификация на уровне 1',
        desc3: 'Наследует безопасность Bitcoin благодаря BitVM',
      },
      quickstart: {
        title: 'Developer Quickstart',
        subtitle:
          'Станьте частью экосистемы Bitlayer. Ознакомьтесь с руководством от создателей для создателей',
        title1: 'Подключение кошелька к тестовой сети Bitlayer',
        skill1:
          'Добавьте конфигурацию тестовой сети Bitlayer в свой кошелек — и взаимодействуйте в ней с децентрализованными приложениями',
        title2: 'Компиляция, запуск и развертывание',
        skill2:
          'Это руководство поможет вам скомпилировать и запустить Bitlayer, чтобы начать работу',
        title3: 'Свежие идеи и новости',
        skill3:
          'Узнавайте в подробностях, как меняются технологии и что происходит на рынке, через наш блог',
        readMore: 'Прочитать подробности',
      },
      intro: {
        title: 'ПАКЕТ TRACK PACK',
        subtitle:
          'Руководство для строителей по присоединению к экосистеме Bitlayer: создано строителями, для строителей.',
        buildNow: 'Стройте сейчас',
      },
      tools: {
        title: 'Developer Tools',
        subtitle:
          'Пользуйтесь инструментами экосистемы Bitlayer, чтобы максимально раскрыть свой потенциал',
        name1: 'Кран',
        desc1: 'Занимайтесь разработкой и получайте токены в тестовой сети Bitlayer каждые 24 часа',
        name2: 'Сканер осн.сети',
        desc2: `Мощный инструмент для изучения и анализа данных блокчейна в основной сети Bitlayer. Копните глубже и исследуйте транзакции, блоки и адреса.`,
        name3: 'Сканер тестсети',
        desc3:
          'Мощный инструмент для изучения и анализа данных блокчейна в тестовой сети. Копните глубже и исследуйте транзакции, блоки и адреса в тестовой сети',
        name4: 'The Graph',
        desc4: 'Индексируйте и получайте доступ к данным блокчейна в реальном времени на Bitlayer.',
        website: 'Сайт',
        more: 'Изучить другие инструменты',
      },
      security: {
        title: 'Безопасность',
        subtitle:
          'Используйте набор программ безопасности для быстрого создания безопасных и надежных приложений',
        name1: 'Dapp Security Manual',
        desc1: `Повысьте эффективность разработки модулей безопасности вашего приложения, следуя руководству по обеспечению безопасности.`,
        buttonLabel1: 'Read doc',
        // name2: 'Audit alliance',
        name2: 'Сеть безопасности',
        desc2: `Быстрое подключение к известным поставщикам услуг безопасности для защиты и повышения ценности вашего приложения.`,
        buttonLabel2: 'Read doc',
        name3: 'Инструменты с открытым исходным кодом',
        desc3:
          'Быстрое самообнаружение с помощью инструментов повышения безопасности с открытым исходным кодом.',
        buttonLabel3: 'Изучить',
      },
      operationalSupport: {
        title: 'Operation Supports',
        subtitle: 'Гранты, финансирование, поощрения и поддержка',
        name1: 'Грант Ready Player',
        desc1:
          'Раскройте потенциал Bitlayer: поиск инновационных проектов и эксклюзивных команд для развития экосистемы BTC.',
        name2: 'Программа поощрения',
        desc2: `Ready Player I – $50 000 000 в виде поощрений для первых строителей и участников.`,
        name3: 'Источник Ops & MKT',
        desc3:
          'Поддержка официальных ресурсов и поддержка ресурсов глобального рынка. Leaderboard, DApp Center, Racer Center.',
        name4: 'Кампания Eco Growth Campaign',
        desc4: 'Программы Mining Gala, The Voice of Bitlayer, Global Crypto Conference.',
        participate: 'Участвовать',
        more: 'Подробнее о поддержке',
      },
      cards: {
        name1: 'Быстрый старт',
        desc1:
          'Bitlayer – это первое решение уровня 2 для биткоина, обеспечивающее безопасность, эквивалентную биткоину, и полноту по тьюрингу. Узнайте, как создавать на его основе децентрализованные приложения (dApp).',
        name2: 'Архитектура Bitlayer',
        desc2:
          'Начните работу с сетью Bitlayer и узнайте о ее уникальных особенностях. Узнайте об архитектуре сети и о том, чем она отличается от других блокчейнов.',
        name3: 'Дорожная карта Bitlayer',
        desc3: `Концепция Bitlayer будет реализована путем развертывания сети Mainnet в несколько этапов, причем каждый этап был направлен на улучшение пользовательского опыта.`,
        readDoc: 'Читать документ',
      },
      connect: {
        title: 'оставайтесь на связи',
        subtitle: 'Будьте в курсе последних новостей и достижений в сообществе Bitlayer',
      },
    },
    bridge: {
      // transfer
      bridgeTitle: 'Мост+доход',
      gasTitle: 'Get Bitlayer Gas',
      gasPromotion: 'Быстрый старт: газ Bitlayer за 1 минуту!',
      newToBridge: 'Используете мост впервые?',
      from: 'Откуда',
      to: 'Куда',
      amount: 'Сумма',
      balance: 'Баланс: {{balance}}',
      available: 'Available: {{balance}}',
      transferable: 'Transferable inscriptions',
      max: 'Макс',
      recipientAddress: 'Адрес получателя',
      recipientTip: 'Для получения токенов подключите кошелек',
      est: 'Примерно: {{time}}',
      fee: 'Комиссия:',
      total: 'Общая сумма:',
      receivepPlaceholder: 'Введите адрес в {{chainName}}',
      transfer: 'Перевод',
      transferProgress: 'Перевод выполняется',
      approve: 'Одобрить',
      approveInProgress: 'Ожидание вашего одобрения',
      checkingAllowance: 'Проверка разрешения',
      minLimit: 'Минимальная транзакция: {{amount}} {{symbol}}',
      maxLimit: 'Максимальная транзакция: {{amount}} {{symbol}}',
      invalidAmount: 'Некорректная сумма',
      invalidAddress: 'Некорректный адрес',
      switchChainDesc:
        'Для переключения на сеть {{chainName}} требуется использовать кошелек {{networkType}}.',
      transferFailed: 'Ошибка перевода',
      connectDesc: 'Для перевода токенов подключите свой кошелек',
      bridgeTip:
        'Используйте мост к Bitlayer, выиграйте Шлем удачи и получите долю фонда в 400 тысяч очков BWB!',
      historyTab: 'История',
      getGas: 'Экспресс-газ',
      swap: 'Обмен',
      gasLimit: `Balance ≥{{maxBalance}} can't be swaped`,
      dayLimit: `Остаток BTC на сегодня равняется {{remainSwapAmount}} долл. США`,
      // history
      loadingData: 'Загрузка данных',
      noData: 'Данных нет',
      sender: 'sender',
      receiver: 'receiver',
      transactionHash: 'Хеш транзакции',
      Pay: 'Вы платите',
      gasPay: 'К выплате',
      gasGet: 'К получению',
      Balance: 'Баланс:',
      Max: 'Макс.',
      SwitchToBitlayer: 'Переключиться на Bitlayer',
      Maximum: 'Максимальная транзакция: {{maxSwapAmount}} BTC',
      History: 'История',
      Receive: 'Вы получаете',
      Fee: 'Комиссия:',
      Total: 'Общая сумма:',
      Slippage: 'Slippage:',
      ThirdPartyBridge: 'Сторонний мост',
      ConfirmSwap: 'Подтвердить обмен',
      ApproveToken: 'Одобрить токен',
      ApproveGas: 'Одобрение подписи без уплаты газа',
      ConfirmSwapRadio: 'Подтвердить курс обмена',
      ConfirmGas: 'Подтверждение курса обмена без уплаты газа',
      Processing: 'Обработка...',
      Completed: 'Выполнено',
      From: 'Откуда',
      To: 'Куда',
      Return: 'вернуться',
      thirdParty: {
        tipTitle: 'Напоминание',
        tipContent:
          'Текущий EVM-мост Bitlayer является бета-версией. Просим использовать сторонний мост, если сумма маленькая.',
      },
      btcHint: 'Поддерживаются только активы протокола Ordinals и BTC',
      btcCaution: {
        title: 'Confirm Transaction',
        message:
          'This transaction will use the following Inputs. Please confirm that these Inputs do not carry other assets.',
      },
      inscribeTransfer: 'Inscribe TRANSFER',
      inscribedTitle: 'Transaction Sent',
      inscribedTip: 'Inscribe will be initiated after confirmation.',
      viewOnExplorer: 'View on Explorer',
      refresh: 'Refresh',
      pending: 'Pending',
      selectAll: 'Select All',
      usdc: {
        change: 'Изменение USDC',
        changeLabel: 'Изменения вашего токена:',
        changeDesc:
          'Этот токен был обновлен на основе собственного смарт-контракта Circle. Новый адрес контракта USDC:',
        confirm: 'Подтвердить и вознаградить',
        learnMore: 'Посмотреть подробнее',
        bonusForYou: 'Бонусные баллы Bitlayer для вас!',
        tips: 'Добрые советы',
        lendingDesc:
          'Мы обнаружили, что у вас есть займы в USDC на кредитной платформе. Пожалуйста, сначала погасите их.',
        borrowingTitle: 'Ситуация с займами USDC.e',
        viewOn: 'Просмотр на {{ platform }}',
        insufficentBalance: 'Недостаточный баланс USDC. Пожалуйста, свяжитесь с нами.',
      },
      errors: {
        invalidRequest: 'Invalid request.',
        insufficentBtc:
          'Insufficient BTC balance or no available UTXO. If you have unconfirmed transactions, please wait for the transaction to be confirmed and try again.',
        insufficentRune:
          'Insufficient Rune balance or no available UTXO. If you have unconfirmed transactions, please wait for the transaction to be confirmed and try again.',
        insufficentBrc20:
          'Insufficient BRC20 balance or no available UTXO. If you have unconfirmed transactions, please wait for the transaction to be confirmed and try again.',
        buildTxFailed: 'Failed to build transaction.',
        transferError: 'Failed to transfer.',
        internalError: 'Internal service error.',
        swapError: 'Не удалось произвести свопинг.',
        insufficientFunds:
          'Недостаточно средств: общая стоимость (газ × газовая комиссия + ценность) выполнения этой транзакции превышает баланс аккаунта.',
      },
    },
    faucet: {
      title: 'Кран тестовой сети',
      description:
        'Занимайтесь разработкой и получайте токены тестовой сети Bitlayer каждые 24 часа. У токенов тестовой сети нет финансовой ценности, и ими нельзя торговать по реальной цене.',
      selectField: {
        label: 'Выбор токена',
        placeholder: 'Выберите токен',
      },
      textFiled: {
        label: 'Адрес кошелька',
        placeholder: 'Enter your Bitlayer Testnet address',
      },
      result: {
        gotTip: 'Вы получили {{tokens}}!',
        gotAnother: 'Вы можете запросить еще {{token}} через 24 часа.',
        sending: 'Отправка...',
      },
      error: {
        testAddress: 'Введите адрес своей тестовой сети Bitlayer',
        verifyCaptcha: 'Пройдите верификацию CAPTCHA',
        verifyFailed: 'Верификация CAPTCHA не пройдена',
        exceededLimit:
          'Вы уже запрашивали токены менее 24 часов назад. Подождите {{h}} {{m}} {{s}} и повторите попытку.',
        invalidAddress: 'Введите корректный адрес тестовой сети Bitlayer',
        unexpectedError: 'Произошла неожиданная ошибка сервера. Повторите попытку позже.',
      },
    },
    readyplayone: {
      title: 'Первому игроку приготовиться',
      description:
        'Разблокируйте доступ к поощрительному фонду в <text>$50,000,000</text> долларов США для первых создателей и участников. Подробнее',
      register: 'зарегистрироваться',
      time: 'С 29 марта до 10 мая 2024 года',
      airdrop: 'До $1M поощрений для каждого проекта!',
      Volume: 'Объем:',
      rules: {
        title: 'Правила',
        ruleContents: [
          {
            title: 'зарегистрироваться',
            texts: ['Регистрация доступна до 10 мая.'],
            date: 'март — апрель',
          },
          {
            title: 'запуск',
            texts: ['Начните запуск в основной сети и приготовьтесь к гонке.'],
            date: 'апрель — май',
          },
          {
            title: 'Эирдроп',
            texts: [
              'Соревнуйтесь за место в таблице лидеров и эирдроп в токенах стоимостью свыше 50 миллионов долларов США.',
            ],
            date: 'Июнь — июль',
          },
        ],
      },
      colead: 'Соруководители',
      investors: 'Инвесторы',
      partners: 'Партнеры',
      setp: {
        register: {
          schemaName: 'Укажите название проекта',
          schemaWebsite: 'Укажите сайт проекта',
          schemaProjectDemo: 'Предоставьте демо-версию проекта',
          schemaContact: 'Укажите имя контактного лица',
          schemaEmail: 'Укажите адрес электронной почты проекта',
          schemaTelegram: 'Укажите Telegram проекта',
          schemaAgree: 'Примите условия и положения',
          newBuilderType: 'Строитель-новичок',
          experiencedBuilderType: 'Опытный строитель',
          formValidError:
            'Мы нашли в вашей форме ошибки. Проверьте и исправьте выделенные поля перед отправкой.',
          apiClientError:
            'Мы нашли в вашей форме ошибки. Проверьте поля и попробуйте отправить форму снова. ({{code}})',
          apiServerError:
            'Похоже, у нас временные неполадки. Обновите страницу или повторите попытку через несколько минут. ({{code}})',
          formOne: {
            title: 'Информация о проекте',
            name: {
              label: 'Название проекта',
              placeholder: 'Введите название',
            },
            builderType: {
              label: 'Тип строителя',
              placeholder: 'Выберите тип строителя',
            },
            fundInfo: {
              label:
                'Информация о фонде проекта: (Опишите статус финансирования проекта в 500 символов или укажите ссылку на подробную информацию.)',
              placeholder: 'Введите информацию о фонде',
            },
            projectDemo: {
              label: 'Демоверсия проекта',
              placeholder: 'Укажите ссылку на демоверсию',
            },
            pitchDeck: {
              label: 'Презентация проекта в слайдах (поддерживается только формат pdf)',
              placeholder: 'Выберите файл для загрузки',
            },
            twitter: {
              label: 'Twitter-аккаунт проекта',
              placeholder: 'Введите идентификатор',
            },
            website: {
              label: 'Сайт проекта',
              placeholder: 'Введите URL-адрес',
            },
            logo: {
              label: 'Логотип проекта <span>(рекомендуется соотношение сторон 1 к 1)</span>',
              placeholder: 'Выберите файл для загрузки',
            },
            description: {
              label:
                'Описание / презентация проекта: кратко опишите ваш проект (цель, решение проблемы, дорожная карта, способ работы) в 500 символов или укажите ссылку на презентацию.',
              placeholder: 'Введите описание',
            },
            category: {
              label: 'К какой категории относится проект?',
              placeholder: 'Выберите категорию',
              selects: [
                'Инфраструктура',
                'Децентрализованные финансы',
                'Социальные взаимодействия',
                'Надписи',
                'NFT',
                'Токен MEME',
                'DAO',
                'Инструменты',
                'Кошелек',
                'Кроссчейн',
                'Конфиденциальные вычисления',
                'Конфиденциальный токен',
                'Рынок предсказаний',
                'Хранилище',
                'Криптоигра',
                'Развлечения',
                'Метавселенная',
                'Другое',
              ],
            },
            stage: {
              label: 'Этап проекта',
              placeholder: 'Выберите категорию',
              selects: ['Генерация идей', 'В разработке', 'Доступен прототип', 'Выпущено/бета'],
            },
            coin: {
              label: 'Адреса токенов в Bitlayer <span>(если недоступны, укажите «N/A»)</span>',
              placeholder: 'Введите адреса токенов',
            },
            lockUp: {
              label: 'Адреса блокировки в Bitlayer <span>(если недоступны, укажите «N/A»)</span>',
              placeholder: 'Введите адреса блокировки',
            },
            contract: {
              label:
                'Адреса контрактов децентрализованного приложения в Bitlayer <span>(если недоступны, укажите «N/A»)</span>',
              placeholder: 'Введите адреса контрактов',
            },
            defilama: {
              label: 'Ссылка на проект в DefiLlama <span>(если недоступна, укажите «N/A»)</span>',
              placeholder: 'Введите ссылку в DefiLlama',
            },
          },
          formTwo: {
            title: 'Информация о команде',
            team: {
              label:
                'Сколько всего человек? Укажите имена, должности, квалификацию и опыт основных участников команды.',
              placeholder: 'Введите описание',
            },
          },
          formThree: {
            title: 'Контактное лицо',
            name: {
              label: 'Имя',
              placeholder: 'Введите имя',
            },
            email: {
              label: 'Адрес электронной почты',
              placeholder: 'Введите адрес электронной почты',
            },
            telegram: {
              label: 'Telegram',
              placeholder: 'Введите URL-адрес',
            },
            twitter: {
              label: 'Профиль в Twitter',
              placeholder: 'Введите URL-адрес',
            },
          },
          consent: {
            title: 'Согласие',
            label:
              'Я принимаю действующие в рамках хакатона условия и положения, политику конфиденциальности и кодекс поведения.',
          },
        },
        launch: {
          title: 'Поздравляем, вы зарегистрировались!',
          documents: 'Документы разработчиков',
          deploy: 'Теперь вы можете развернуть проект в Bitlayer!',
          scanCode: 'Отсканируйте, чтобы присоединиться к сообществу',
          next: 'Далее',
        },
        airdrop: {
          deploymentTip: 'Если развертывание вашего проекта завершено, вы молодец!',
          description:
            'Вы сможете принять участие в первичных выборах! Победители финала получат гранты в размере от $10K до $300K. Дополнительные бонусы включают почетные награды в размере от $3K до $5K и доступ к призовому пулу в $1M за проведение мероприятий!',
        },
      },
      gems: {
        title: 'Bitlayer Gems Airdrop',
        gems: ' — столько алмазов',
        tips: ' будет распределено совсем скоро!',
        button: 'Могу ли я претендовать',
        claimGems: {
          button: 'Забрать алмазы',
          label1: 'Вы можете претендовать на ',
          label2: 'эирдроп алмазов!',
          'failed-title': 'Все впереди!',
          'label-failed':
            'В этом раунде вы не можете претендовать на алмазы. Следите за новостями о предстоящих мероприятиях!',
          'button-failed': 'Пусть Dapp приносят вам награды',
        },
      },
      subTitle: 'Программа грантов Bitlayer Booster',
      descriptionV4:
        'Справедливая и прозрачная, ежемесячная проверка, до <text> $1Million </text> поощрений для каждого проекта!',
      incentives: {
        tag: 'Поощрения',
        'New Builder': {
          title: 'Строитель-новичок',
          bonus: 'Почетные звания и вознаграждения в размере <span>{{amount}}</span>',
        },
        'Experienced builder': {
          title: 'Опытный строитель',
          bonus: 'Эксклюзивный призовой пул в размере <span>{{amount}}</span>',
        },
        grants: 'Гранты',
        bonus: 'Бонус',
      },
      rulesV4: {
        tag: 'Правила',
        ruleContents: [
          {
            title: 'Регистрация',
            texts: [
              'Проекты, зарегистрированные до 30-го числа каждого месяца, будут оцениваться в текущем месяце',
              'Проекты, зарегистрированные после 30-го числа, будут оцениваться в следующем месяце',
              'Убедитесь, что при регистрации предоставлена достаточная информация',
            ],
            date: 'Ежемесячно',
          },
          {
            title: 'Первичные выборы',
            texts: [
              'Комитет по проверке Bitlayer будет проводить скользящую проверку на основе предоставленной информации.',
              'Дополнительная проверка может включать технические проверки, исследование рынка и анализ продукта.',
            ],
            date: 'Расч. срок 7 дней',
          },
          {
            title: 'Окончательные выборы',
            texts: [
              'Bitlayer Foundation совместно с судьей из инвестиционной организации оценит проекты, прошедшие первичный тур.',
              'Каждый проект выступит с 10-15-минутной презентацией.',
              'Проекты-победители получат гранты в размере от $10 000 до $300 000',
            ],
            date: 'Расч. срок 14 дней',
          },
        ],
      },
      currentShortlist: {
        tag: 'Текущий шорт-лист',
        primaryElection: 'Первичные выборы',
        finalElection: 'Окончательный список',
        like: 'Нравится',
        grant: 'Грант',
      },
      updates: {
        tag: 'Обновления',
      },
    },
    luckyhelmet: {
      title: 'Шлем удачи',
      description: 'Жмите на газ с Bitlayer',
      toGet: 'Как получить?',
      miningBtn: 'Минтинг открывается 4 мая 2024 г.',
      assetTitle: 'Актив на уровне 1',
      assetDesc: 'Нативный актив Bitcoin Ordinals на уровне 1 Bitcoin',
      mintTitle: 'Минтинг на уровне 2',
      mintDesc: 'Газовая комиссия ниже, а эффективность выше, чем на уровне 1',
      desc1: 'Гибкое сочетание уровней 1 и 2',
      desc2: 'Золотая жила преимуществ от Bitlayer',
      minting: 'Минтинг',
      mint: 'Минтинг',
      checkWhitelist: 'Проверить белый список',
      minted: 'отчеканено',
      mintingSuccess: 'Минтинг выполнен',
      congratulations: 'Поздравляем!',
      success: 'Вы в белом списке.',
      sorry: 'Сожалеем',
      failure: 'Вы не в белом списке.',
      finished: 'Завершено',
      whitelistOnly: 'Только белый список',
      trade: 'Торгуйте Шлемами удачи',
    },
    rank: {
      head: {
        head1: 'Призовой фонд',
        head2: 'Децентрализованные приложения',
        head3: 'Всего голосов',
        head4: 'Голосов сегодня',
      },
      banner: {
        tag: 'Этап подготовки',
        title: 'ПЕРВОМУ ИГРОКУ ПРИГОТОВИТЬСЯ',
        subtitle: 'токен для создателей',
        link: 'Узнайте наши правила!',
        all: 'Все категории',
        switch: 'Переключение на',
        register: 'Добавить Dapp',
      },
      list: {
        votes: 'голос(-а/-ов)',
        vote: 'Проголосовать',
        out: 'Голосов не осталось',
        invite: 'Приглашение за очки',
      },
      carouse: {
        title: 'Мои очки «Первому игроку приготовиться»',
        note: 'Обратите внимание: очки «Первому игроку приготовиться» не эквивалентны очкам Bitlayer. Их можно будет обменять по установленной ставке. Следите за нашими новостями!',
        accepted: 'Приглашение принято',
        taskCenter: 'Центр заданий',
        now: 'Подписаться',
        vote: 'Каждый голос',
        invite: 'Каждое приглашение',
        follow: 'Подписаться в X',
        hint1: 'До 3 раз в день',
        hint2: 'Разовое задание',
      },
      dialog: {
        band: {
          title: 'Пригласите друзей, чтобы прокачаться',
          sorry:
            'Сожалеем! Ваш Twitter-аккаунт уже привязан к другому адресу. Используйте другой Twitter-аккаунт',
        },
        invite: {
          need: 'Сначала нужно привязать аккаунт X (Twitter)! Зарабатывайте по 500 очков за каждое успешное приглашение!',
          auth: 'Авторизация в X',
          attention:
            'Внимание: во избежание ошибок один Twitter-аккаунт разрешается привязать только к одному адресу',
        },
        note: {
          please: 'Обратите внимание:',
          note: 'Очки «Первому игроку приготовиться» не эквивалентны очкам Bitlayer. Их можно будет обменять по установленной ставке. Следите за нашими новостями!',
        },
        rules: {
          rules1: 'Зарабатывайте по 300 очков в день за три голосования.',
          rules2: 'Подпишитесь на официальный Twitter-аккаунт — и получите 500 очков.',
          rules3:
            'За каждого нового пользователя, которого вы приглашаете проголосовать, вам присуждается по 500 очков.',
          rules4:
            'Будет разыграно 100 Шлемов удачи от Bitlayer. Чем выше место, тем больше шансов.',
          vote: 'Голосование за понравившиеся проекты',
          note: 'Присоединяйтесь к голосованию и поддерживайте понравившиеся проекты в списке популярности Bitlayer! Период мероприятия: С 23 апреля до 10 мая 2024 года. Заходите каждый день на страницу таблицы лидеров. Вам доступно три голоса в сутки. Заработайте дополнительные очки за подписку на официальный Twitter-аккаунт и приглашение новых пользователей. Активные участники копят очки «Первому игроку приготовиться» и могут рассчитывать на включение в белый список претендентов на Шлем удачи от Bitlayer.',
          rewards: 'Награды:',
          boost: `Повышайте популярность своего проекта, влияйте на итоговую таблицу лидеров и проявляйте активность, чтобы выиграть официальную NFT-награду!`,
          gotIt: 'Понятно',
        },
        share: {
          copy: 'Copy Link',
          share: 'Share On',
          invite: 'Пригласить на голосование',
          more: 'Поучить больше голосов от друзей',
          connect: 'Подключение X (Twitter)',
        },
        vote: {
          vote: 'Проголосовать',
          voteFor: 'Проголосовать за проект',
          daily: '3 голоса в день',
          votes: 'голос(-а/-ов)',
        },
        expire: {
          late: 'К сожалению, вы опоздали!',
          expired: 'Голосование завершилось.',
          tuned:
            'Подробности будут опубликованы в нашем официальном Twitter-аккаунте, следите за новостями!',
          thx: 'Спасибо!',
          follow: 'Подпишитесь на нас',
        },
      },
    },
    getGas: {
      recipient: 'Адрес получателя',
      placeholder: 'Введите адрес Bitlayer',
      invalid: 'Некорректный адрес',
      purchase: 'Покупка',
      time: 'Примерное время: около 1 минуты после выполнения платежа',
      getGas: 'Экспресс-газ',
      getRegular: 'Стандарт-газ',
      after: 'После перевода вы сможете просмотреть заявки',
      history: 'История',
      payment: 'Платежные адреса',
      timeout: 'Срок действия QR-кода истек',
      warning:
        'Не выполняйте отправку по адресам с истекшим сроком, иначе вы рискуете потерять токены',
      amount: 'Сумма платежа:',
      cancelWarning:
        'Если заявка отменена, не переводите токены по платежному адресу, поскольку это может привести к потере средств.',
      ok: 'OK',
      cancel: 'Cancel Order',
      Complete: 'Выполнено',
      getBitlayerGas:
        'Вы можете получать газ в Bitlayer, переводя токены по платежному адресу из любого подходящего кошелька или биржи.',
      promotionNote1: 'Акция: комиссия за экспресс-газ —',
      fee: 'всего 1 доллар!',
      promotionNote2: 'Не пропустите!',
      addressNotion:
        'The payment address for each order an only be used once, do not transfer funds more than one time.',
      promotion: 'Комиссия со скидкой:',
      estimated: 'Примерное время: ~1 минута',
      ensure: 'Проверьте следующую информацию',
      content: `Убедитесь, что <strong>блокчейн, токен и сумма</strong> верны. Каждый платежный
                адрес можно использовать только <strong>один раз.</strong>
                <div>Иначе вы рискуете потерять свой депозит.</div>`,
      aboutFlash: 'About Flash Bridge-In',
      aboutInfo: `a. Все средства переводятся с флэш-моста через официальный мост Bitlayer.<br>b. Это делает Bitlayer быстрее и выгоднее.`,
      get: 'Get',
      right: 'Сделайте все правильно!',
      otherwise: 'Иначе',
      lose: 'можете лишиться активов',
      Blockchain: 'Блокчейн',
      Amount: ' Сумма',
      token: 'Токен',
      pay: '1 адрес для 1 платежа',
      kind: 'Советы',
      hasBridge:
        'Вы собираетесь перейти по сторонней ссылке, которая не относится к Bitlayer. Ваше использование сторонней ссылки регулируется политикой конфиденциальности и пользовательским соглашением, связанными со сторонней ссылкой, — и третья сторона несет в отношении вас единоличную ответственность. Просим учитывать эти обстоятельства и их последствия.',
      notBridge:
        'Это эксклюзивная ссылка для мероприятий PEPE. Вы можете перейти по ней, чтобы забрать награды после транзакции через мост.',
      commonRisk: 'Распространенные риски и виды обмана',
      phishing: 'Фишинговый сайт',
      authorization: 'Риск авторизации',
      highYield: 'Хайп-проект',
      ponzischeme: 'Финансовая пирамида',
      freeAirdrop: '«Бесплатный» эирдроп',
      contractLoophole: 'Лазейка в контракте',
      aware: 'Я осознаю риски',
    },
    leaderBoard: {
      title: 'Соревнование в Bitlayer с таблицей лидеров. Эпоха 1',
      support: 'в помощь с листингом',
      incentives: 'на поощрения',
      liquidity: 'Помощь с ликвидностью',
      forPartners: 'для экопартнеров',
      rules: 'Узнайте наши правила!',
      topTvl: 'Топ по TVL',
      topTransation24: 'Top Transactions',
      topTransaction: 'Top Transaction',
      topPopularity: 'Популярность',
      viewAll: 'View All',
      tryNow: 'Сайт',
      countdown: 'Epoch Countdown',
      countEnded: 'Epoch ended',
      stage3: 'Stage 3 Competiton Epoch',
      stage2: 'Stage 2 Preparation Stage',
      gemsMinted: 'Total Gems Minted',
      locked: 'Total Value Locked',
      transactionCount: 'Transaction Count',
      totalLikes: 'Total Likes',
      tvlRanking: 'Рейтинг по TVL в экосистеме основной сети Bitlayer',
      discoverTvl:
        'Узнайте о децентрализованных приложениях на базе основной сети Bitlayer в топе по TVL',
      txnRanking: 'Рейтинг по транзакциям в экосистеме основной сети Bitlayer',
      discoverTransactions:
        'Узнайте о децентрализованных приложениях на базе основной сети Bitlayer с большинством транзакций',
      rankingList: 'Рейтинг по популярности в экосистеме основной сети Bitlayer',
      discoverPopular:
        'Узнайте о самых популярных децентрализованных приложениях на базе основной сети Bitlayer',
      top10: 'Таблица лидеров: топ-10',
      tvl: 'TVL',
      transactions: 'Транзакции',
      likes: 'Лайки',
      valueUpdate:
        'Общая заблокированная стоимость (TVL —total value locked) действующих активов в протоколах децентрализованных приложений в основной сети Bitlayer. Обновляется ежедневно.',
      numberUpdate:
        'Количество успешных транзакций пользователей, взаимодействующих с контрактом децентрализованного приложения. Обновляется ежедневно.',
      likesUpdate:
        'Общее количество лайков проекта за период соревнования. Обновляется в реальном времени.',
      name: 'Название',
      category: 'Категория',
      gemMinted: 'Отчеканено алмазов',
      gemsPoints:
        'Количество алмазов рассчитывается на основе показателя TVL (total value locked — общая заблокированная стоимость), количества успешных транзакций, числа активных зрителей, таблицы лидеров по популярности в Bitlayer и качества продукта. Данные обновляются каждый день в 00:00 (UTC).',
      boost: 'Усилить',
      bindX: 'Привяжите аккаунт X (Twitter)',
      bindNeed: 'Сначала нужно привязать аккаунт X (Twitter)!',
      start: 'Начало 23 мая',
      airdrop: 'Эирдроп в разгаре!',
      reward: 'Reward',
      rank: 'Рейтинг Dapp',
      dappCenter: 'Центр Dapp',
      more: 'Еще',
      introduce: 'Знакомство с активностью',
      gems: '100% алмазов',
      distributed: 'Will Be Distributed To Users!',
      rewards: 'Награды',
      completed: 'Завершено',
      winnerList: 'Список победителей',
      slots: ' победителей',
      win: 'Complete to Win',
      startAt: 'Начало:',
      eventCountdown: 'Event countdown',
      ended: 'Мероприятие кончилось',
      verify: 'подтвердить',
      verified: 'верифицированные',
      tutorial: 'Руководство',
      participated: ' — количество участников',
      tasks: 'Задания',
      congratulation: 'Поздравляем!',
      foru: 'Бонусные баллы Bitlayer для вас!',
      export: 'Изучайте приложения Bitlayer dApps и получайте больше вознаграждений!',
      phase: 'сезон {{num}}',
      'Free Drop': 'Бесплатный дроп',
      '$1 Sweeptake': 'Свипстейк $1',
    },
    dappDetail: {
      reward: 'Reward:',
      activity: 'Активность',
      join: 'Присоединяйтесь!',
      introduce: 'Знакомство с активностью',
      whatIs: 'Что такое {{name}}?',
      overView: 'Обзор',
      dappIntro: 'Представляем наше децентрализованное приложение!',
      event: `Как принять участие в эирдропе`,
      team: 'Команда',
      twitterText:
        'Присоединяйтесь и попробуйте {{name}} в @BitlayerLabs прямо сейчас! Больше 100 децентрализованных приложений в экосистеме #Bitlayer — это ваш шанс получить награду!',
      shareTitle: 'Делитесь с друзьями',
      shareText:
        'Рассказывайте друзьям о проектах, которые вам нравятся, и исследуйте их вместе! Будьте ранними пташками и хватайте награды!',
    },
    miningGala: {
      meta: {
        title: 'Win Bitlayer rewards & dapp airdrops',
      },
      head: {
        time: '27 мая 2024 г., 13:00 по UTC — 10 июня 2024 г., 13:00 по UTC',
        doubleGain: 'Двойная выгода',
      },
      miningPolls: {
        title: 'Майнинг-пулы',
        claim: 'Забрать',
        minted: 'Отчеканено',
        taskTitle: 'Выполняйте задания, чтобы отчеканить Значок первопроходца',
        taskTip: 'Выполните по меньшей мере одну транзакцию через {{project}}',
        shareText: 'Поделитесь с друзьями',
        or: 'или',
        hasHelmet: 'Будьте обладателем Шлема удачи',
        auditReport: 'аудиторский отчет',
        miningGuide: 'Mining Guide',
        airdrop: 'Эирдроп',
        detailRules: 'Подробные правила',
        claimSuccess: 'Успех!',
        claimFailed: 'Не удалось забрать. Попробуйте позже.',
        bitlayerTask: 'Участвуйте в празднике майнинга в Bitlayer',
        lorenze: {
          desc: 'Первоклассная платформа для выпуска токенов, торговли ими и урегулирования транзакций через Babylon с ликвидным рестейкингом Bitcoin.',
          tasks: [
            'Размещайте BTC в рамках предварительного стейкинга в Babylon и получайте stBTC.',
            'Используйте мост для перевода stBTC в основную сеть Bitlayer.',
            'Используйте stBTC в экосистеме децентрализованных финансов Bitlayer.',
          ],
        },
        bitsmiley: {
          desc: 'Стейблкоин-протокол на базе BTC, который изначально финансировали OKX Ventures и ABCDELabs.',
          tasks: [
            'Чеканьте bitUSD через BitSmiley.',
            'Поставляйте ликвидность для пар bitUSD-USDT и bitUSD-WBTC через bitCow.',
            'Стейкинг bitUSD через протоколы децентрализованных финансов.',
          ],
        },
        avalon: {
          desc: 'Avalon Finance стремится стать лучшим протоколом децентрализованного кредитования на базе Bitcoin уровня 2.',
          tasks: [
            'Поставляйте активы в Bitlayer — и заработайте минимум 1000 очков поставки.',
            'Занимайте активы в Bitlayer — и заработайте минимум 500 очков займа.',
            'Наладьте цикл поставок и займов в Avalon Finance.',
          ],
        },
        bitcow: {
          desc: 'Автоматический маркет-мейкер с концентрированной ликвидностью и стейблкоином на базе BTC.',
          tasks: [
            'Поставьте ликвидность для торговых пар bitUSD-WBTC или bitUSD-USDT.',
            'Примите участие в торговле bitUSD-USDT или другими парами.',
            'Создайте новые торговые пары и повысьте объем торговли.',
          ],
        },
        pell: {
          desc: 'Pell — это децентрализованная доверительная торговая площадка, которая диверсифицирует доходность в BTC и LSD и призвана повысить безопасность сетей BTC уровня 2.',
          tasks: [
            'Чтобы войти, подключите кошелек.',
            'Выполните стейкинг на сумму от 0,001 BTC на 7 дней.',
            'Розыгрыш: карта постоянных очков с множителем 1,5 для 1000, 100 USDT для 10.',
          ],
        },
        enzo: {
          desc: 'Enzo Finance — это превосходный протокол децентрализованного кредитования на блокчейне Bitlayer с передовыми алгоритмами.',
          tasks: [
            'Осуществите депозит или стейкинг своих активов Bitlayer через Enzo Finance.',
            'Займите активы Bitlayer через Enzo Finance.',
            'Ежедневная раздача 1 BTC (требование: общая сумма депозитов или займов > $100 USDT).',
          ],
        },
        bitparty: {
          desc: 'BitParty — это первая сеть сообщества с геймификацией активов в экосистеме BTC!',
          tasks: [
            'Получите активы Bitlayer по кроссчейн-мосту.',
            'Осуществите стейкинг своих активов Bitlayer в Bitparty и заработайте очки, чтобы получить $BTPX.',
            'Вступайте в группы и занимайте территорию!',
          ],
        },
      },
      shareDialog: {
        title: 'Делитесь с друзьями',
        desc: 'Делитесь с друзьями, участвуйте в празднике майнинга от Bitlayer и собирайте эксклюзивные Значки первопроходца уже сейчас!',
      },
      tipDialog: {
        title: 'Советы',
        desc: 'недостаточный баланс BTC. Его можно пополнить следующими способами.',
        depositBtn: 'Быстрый депозит',
        bridgeBtn: 'Мост',
      },
      twitterShare: {
        projectShare: `Давайте вместе претендовать на часть майнинг-пула {{name}} на празднике майнинга #MiningGala в @BitlayerLabs прямо сейчас! Эирдроп в {{money}} ждет нас!\n`,
        badgeShare:
          'У меня появился Значок первопроходца на празднике #MiningGala в @BitlayerLabs! Переходите на эту страницу, чтобы отчеканить такой же! Давайте разделим эирдроп в 24 350 000 долларов США!\n',
      },
      bottomTip: {
        title: 'Useful Tutorials for Bitlayer Mining Gala!',
        desc: 'Добро пожаловать на праздник майнинга в Bitlayer! Прочитайте руководства ниже и начните увлекательное майнинг-приключение!',
        tips: [
          'Газ Bitlayer за 1 минуту.',
          'Как переводить активы по мосту в Bitlayer.',
          'Лучшее руководство по участию в празднике майнинга в Bitlayer.',
          'Руководство по минтингу для первопроходцев на празднике майнинга в Bitlayer.',
        ],
      },
    },
    userCenter: {
      title: 'Racer Center',
      dailyTasks: 'ЗАДАНИЯ ДНЯ',
      bitlayerPoints: 'ОЧКИ BITLAYER',
      bitlayerGems: 'АЛМАЗЫ BITLAYER',
      tasks: 'Tasks',
      badges: 'Badges',
      bitlayerDays: '<span>{{ days }}</span> ДН. В BITLAYER',
      txn: 'Транз.',
      bridged: 'Мост',
      unlocked: 'Lv{{ level }} unlocked',
      locked: 'Lv{{ level }} locked',
      unlocking: 'Lv{{ level }} unlocking <span>{{ progress }}%</span>',
      tabs: {
        newRacerTasks: 'Новые задания гонщика',
        advancedTasks: 'Продвинутые задания',
        ecoTasks: 'Задания Eco',
        myBadges: 'Мои значки',
      },
      task: {
        complete: 'ВЫПОЛНЕНО',
        claim: 'ПОЛУЧИТЬ',
        pointsAcquired: 'Получено столько очков Bitlayer: <span>{{ points }}</span>.',
        check: 'Проверить',
      },
      claimGems: {
        title: 'Поздравляем! У вас есть драгоценные камни к получению.',
        action: 'Перейти к получению',
      },
      badge: {
        coming: 'СКОРО',
        inProgress: 'В ПРОЦЕССЕ',
        finished: 'ЗАВЕРШЕНО',
        claim: 'ЗАБРАТЬ ОЧКИ',
        join: 'ПРИСОЕДИНИТЬСЯ',
        owned: 'ЗНАЧОК ЕСТЬ',
        notOwned: 'ЗНАЧКА НЕТ',
        rewardCanClaim: 'можно забрать',
        rewardClaimed: 'забрали',
      },
      reward: {
        points: 'Очки',
        gems: 'Алмазы',
        btr: 'BTR',
      },
      errors: {
        twitter: {
          accountBinded:
            'Этот Twitter-аккаунт уже привязан к другому адресу. Используйте другой Twitter-аккаунт.',
        },
        unexpectedError: 'Произошла неожиданная ошибка. Повторите попытку позже.',
      },
      rankTitle: 'Рейтинг МОИХ очков',
      topRacers: 'Топ-гонщики',
      racer: 'Гонщик',
      gains: 'Набирает',
      invite: {
        discover: 'Откройте для себя bitlayer',
        and: 'и получите',
        invite: 'Приглашайте друзей',
        refer: 'ПРИГЛАСИТЬ ДРУЗЕЙ',
        shareText:
          'Поделитесь своим реферальным кодом/ссылкой с друзьями. Так как они регистрируются на bitlayer.org и отправляют токены на Bitlayer более одного раза, вы можете получить вознаграждение. (Обновление каждые 12 часов)',
        my: 'МОИ РЕФЕРАЛЫ',
      },
      twitter: {
        bindTitle: 'Привязать свой X (Twitter)',
        bindDesc: 'Сначала вам нужно привязать учетную запись X (Twitter)!',
        bindAction: 'Авторизуйтесь на X',
        tips: 'Внимание: во избежание ошибок одна учетная запись Twitter может быть привязана только к одному адресу.',
      },
      draw: {
        history: {
          history: 'История розыгрыша',
          time: 'Время розыгрыша',
          reward: 'Вознаграждение',
        },
        error: 'У вас 0 шансов на выигрыш, пригласите друзей, чтобы получить больше',
        up: 'Вознаграждение до $10K',
        unit: 'Розыгрыш',
        invite: 'Invite for more chances and upgrade your lucky level !!!',
        card: 'Разыграть карту',
        wait: 'Ожидание подтверждения на блокчейне, ~30с',
        warning: 'Предупреждение',
        sorry:
          'Извините! За каждый розыгрыш необходимо набрать 1000 очков, у вас недостаточно очков.',
        ok: 'OK',
      },
    },
    activities: {
      guessingGame: {
        title: 'Guess to earn',
        subtitle: 'Мастер прогнозирования цен',
        olympics2024: {
          title: 'Угадывай и выигрывай',
          subtitle: 'Олимпиада Париж-2024',
        },
        status: {
          inProgress: 'Выполняется',
          coming: 'Скоро',
          waiting: 'Ожидание',
          finished: 'Готово',
          releasing: 'Публикация результата через 24 часа',
          endIn: 'Окончание в',
        },
        placeBet: 'СДЕЛАТЬ СТАВКУ',
        bet: 'Ставка',
        betUnit: '{{ step }} {{ rewardType }} на единицу',
        betFor: '<icon /> <span>{{ amount }}</span> для {{ text }}',
        wonRewardFor: 'Выигрыш <icon /> <span>{{ amount }}</span> для {{ text }}',
        myBets: 'МОИ СТАВКИ',
        won: 'ВЫИГРЫШ',
        return: 'Возврат',
        pool: 'Пул',
        reward: {
          claim: 'ПОЛУЧИТЬ',
          claimed: 'ПОЛУЧЕНО',
          claimAll: 'Получить все награды',
          congratulations: 'Поздравляем!',
          claimedAllPoints: 'Вы выиграли <point /> <span>{{ points }}</span> в racer center.',
          claimedAllGems: 'Вы выиграли <gem /> <span>{{ gems }}</span> в racer center.',
          claimedAllBoth:
            'Вы выиграли <point /> <span>{{ points }}</span> и <gem /> <span>{{ gems }}</span> в racer center.',
        },
        messages: {
          placedBet: 'Вы успешно сделали ставку. Удачи!',
          failedToPlaceBet: 'Не удалось сделать ставку. Повторите попытку позже.',
          claimedReward: 'Вы успешно получили награду!',
          failedToClaim: 'Не удалось получить награду. Повторите попытку позже.',
          insufficient:
            'Вам нужно как минимум {{ amount }} {{ rewardType }}, чтобы сделать ставку.',
          alreadyClaimedAll: 'Вы уже получили все награды.',
          failedToClaimAll: 'Не удалось получить все награды. Повторите попытку позже.',
        },
      },
    },
    whiteList: {
      reward: 'Мои награды',
      address: 'Адрес',
      xp: 'Очки Bitlayer',
      lucky: 'Награда удачи',
      points: 'оч.',
      winnerList: 'Список победителей',
      description: `Notes: Users completing and verifying all tasks will be rewarded with Bitlayer Points within 7 days after the events ends. Project rewards will be distributed according to the project's official announcements. `,
    },
    opvm: {
      title: {
        slogon1: 'OpVM',
        slogon2: 'сделать биткоин',
        slogon3: 'verifiable',
        description: 'Первый уровень верификации биткоина',
        whitePaper: 'ВайтПейпер',
        earlyAccess: 'Ранний доступ',
        comingSoon: 'Скоро',
      },
      feature: {
        badge: 'Характеристики',
        title1: 'Перенятая безопасность биткоина',
        description1: 'При минимальных затратах на инжиниринг',
        title2: 'Модульный и составимый',
        description2: 'Сервис на основе API, стандарт Plug And Play',
        title3: 'Современная архитектура',
        description3: 'Защита от мошенничества + защита от недействительности',
        title4: 'Настраиваемый и модернизируемый',
        description4: 'Открытый исходный код и возможность аудита',
      },
      advantages: {
        badge: 'Преимущества',
        num1: 'Ускоренный срок разработки',
        num2: 'Низкая стоимость газа благодаря агрегированию',
        num3: 'Безопасность уровня 1, как у биткоина',
      },
      usecase: {
        badge: 'Сценарий использования',
        case1: 'Уровень 2 биткоина',
        case2: 'Обертывание биткоина',
        case3: 'Мост для биткоина',
        case4: 'Протокол стейкинга биткоина',
        start: 'начните прямо сейчас!',
        early: 'Ранний доступ',
      },
    },
  },
};
