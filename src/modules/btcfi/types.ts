export interface XIntent {
  type: string;
  params: Record<string, string>;
}

export interface TaskAction {
  type: string;
  payload?: {
    path?: string;
    timeOnPage?: number;
    isBind?: boolean;
    template?: string;
    intent?: XIntent;
    contract_addr?: `0x${string}` | null | undefined;
    project_id?: number;
    task_name?: string;
    progress_cfg?: { key: number; value: number }[];
    sub_title?: string;
  };
}

export type TaskItemProps = {
  title: string;
  rewardPoints: 0;
  action: TaskAction;
  logoType: string;
  isCompleted: boolean;
  completedCount: number;
  targetCount: number;
  taskId: number;
  rank: number;
  mainTitle: string;
  canClaim: boolean;
  taskType: number;
  extraData: {
    cur_claimed_progress?: number;
    rewards: string;
    cur_done_progress?: number;
    interrupted_at?: number;
    is_tx_pending?: boolean;
    today_progress: string;
    rechecked_in_days: string;
    max_query_timestamp?: number;
    min_query_timestamp?: number;
    my_rewards?: number;
    total_rewards_issued?: number;
    missing_days?: string;
    red_point?: boolean;
    activity_ended?: boolean;
  };
};

export interface BtcFiTaskResponse {
  btcfiTasks: TaskItemProps[];
}

export interface DailyTaskResponse {
  taskInfo: TaskItemProps;
}

type InfoType = {
  timestamp: number;
  status: 'future_check' | 'miss_check' | 'cur_check' | 'checked' | 'cur_check';
  rewardAmount: number;
};

export interface RecheckResponse {
  isTxPending?: boolean;
  reCheckDays?: string;
  infos?: InfoType[];
  reCheckReward?: string;
}

export interface DailyResponse {
  claimablePoints: string;
}

export type WinnerItemProps = {
  address: string;
  rewardAmount: string;
  rewardLevel: string;
};

export interface LotteryResponse {
  timestamp: string;
  winners: WinnerItemProps[];
}

export interface InviteCodeResponse {
  inviteCode: string;
}

export interface InviteInfoResponse {
  claimableRewardAmount: number;
  hasCheckedIn: boolean;
  inviteeNum: number;
  maxInviteeNum: number;
  totalRewardFromInvitee: number;
}

export type InviteItem = {
  invitee_address: string;
  has_signed_in: boolean;
  has_checked_in: boolean;
  reward_for_inviter: number;
};

export interface InviteListResponse {
  currentPage: number;
  inviteeList: InviteItem[];
}

export interface HelmetResponse {
  holdNum: number;
  claimableNum: number;
  btrPerNft: number;
  weekNum: number;
}
