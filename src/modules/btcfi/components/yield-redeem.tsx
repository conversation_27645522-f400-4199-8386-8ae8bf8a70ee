import { useTranslation } from 'react-i18next';
import { ChangeEvent, useEffect, useState } from 'react';
import { Dialog, DialogCloseRounded, DialogContent } from '@/components/ui/dialog';
import BTCIcon from '@/components/icons/coins/BTCIcon';
import DollarIcon from '@/components/icons/DollarIcon';
import { Input } from '@/components/ui/input';
import { useRevalidator } from '@remix-run/react';
import { Loading } from './Loading';
import Decimal from 'decimal.js';
import { BaseError, UserRejectedRequestError } from 'viem';

import { bitlayerTestnet, bitlayerMainnet } from '@/wallets/config/chains';
import { useMediaQuery } from '@react-hook/media-query';
import {
  UserDataResponse,
  ApproveResponse,
  AllowanceResponse,
  RedeemListResponse,
} from '@/modules/btcfiyield/types';
import { cn } from '@/lib/utils';
import { controller } from '@/modules/btcfiyield/const';

import {
  useRedeem,
  useRedeemList,
  useRedeemParam,
  useUpdate,
  usePollTransaction,
} from '@/modules/btcfiyield/hooks/index';
import { useConfig } from 'wagmi';
import { sendTransaction } from '@wagmi/core';
import { truncateTo8Decimals } from '@/lib/utils';
import ky from 'ky';
import { toast } from 'sonner';

interface RedeemDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children?: React.ReactNode;
  userData?: UserDataResponse;
  address?: string;
}

interface ApproveRes {
  data: ApproveResponse;
}

interface AllowanceProps {
  data: AllowanceResponse;
}

interface RedeemPage {
  list: RedeemListResponse['list'];
}

const isDev = import.meta.env.MODE === 'development';

export const chain = isDev ? bitlayerTestnet : bitlayerMainnet;

export function RedeemDialog({ open, onOpenChange, userData, address }: RedeemDialogProps) {
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const { t } = useTranslation('', { keyPrefix: 'pages.btcfi.yield' });
  const { mutateAsync: applyRedeem } = useRedeem();
  const { mutateAsync: getRedeemParam } = useRedeemParam();
  const { mutateAsync: update } = useUpdate();
  const [loadingId, setIsLoadingId] = useState('');
  const [maxAmount, setMaxAmount] = useState('');
  const [applyLoading, setApplyLoading] = useState(false);
  const revalidator = useRevalidator();
  const { pollTransaction } = usePollTransaction();
  const [insufficientFundsError, setInsufficientFundsError] = useState(false);

  const config = useConfig();
  const [amount, setAmount] = useState('');

  useEffect(() => {
    const _max = truncateTo8Decimals(
      Number(userData?.canRedeemAmount) > 0 ? userData?.canRedeemAmount : '0',
    );
    setMaxAmount(_max);
  }, [userData]);

  const { data: redeemInfo, trigger: nextPageTrigger, rootRef, refetch } = useRedeemList();

  const redeemList = (redeemInfo?.pages as RedeemPage[])?.map((page) => page.list).flat();

  useEffect(() => {
    refetch();
  }, [address]);

  const refresh = () => {
    revalidator.revalidate();
  };

  const handeWithdraw = async (id: string, shares: string) => {
    setIsLoadingId(id);
    try {
      const allowance = (await ky.get('/api/btcfiyield/allowance').json()) as AllowanceProps;
      const isEnough =
        BigInt(allowance.data.allowance[controller]) >=
        BigInt(new Decimal(shares).mul(new Decimal(10).pow(18)).toFixed(0));

      if (!isEnough) {
        const approve = (await ky.get('/api/btcfiyield/approve').json()) as ApproveRes;
        const txData = approve?.data?.tx;
        delete txData?.txId;
        delete txData?.gasPrice;
        if (isDev) {
          delete txData.gas;
          delete txData.maxFeePerGas;
          delete txData.maxPriorityFeePerGas;
        }
        await sendTransaction(config, {
          ...txData,
        });
      }
      const RedeemParam = await getRedeemParam(id);
      const redeemTx = RedeemParam?.data?.tx;
      if (!redeemTx) {
        throw Error('no tx');
      }
      const txId = redeemTx?.txId;
      delete redeemTx?.txId;
      delete redeemTx?.gasPrice;
      if (isDev) {
        delete redeemTx?.gas;
        delete redeemTx?.maxFeePerGas;
        delete redeemTx?.maxPriorityFeePerGas;
      }
      if (!redeemTx) {
        return;
      }
      const hash = await sendTransaction(config, {
        ...redeemTx,
      });
      await pollTransaction(hash, async () => {
        await update({ tx_id: txId!, tx_hash: hash });
        refresh();
        refetch();
        setIsLoadingId('');
      });
    } catch (e) {
      if (e instanceof BaseError) {
        const userRejected = e.walk(
          (error) => error instanceof UserRejectedRequestError,
        ) as UserRejectedRequestError | null;
        if (!userRejected) {
          toast('Network error, please try again');
        }
      } else {
        toast('Network error, please try again');
      }
      setIsLoadingId('');
    }
  };

  const inputChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.value.length > 10) {
      return;
    }
    setInsufficientFundsError(Number(e.target.value) > Number(userData?.canRedeemAmount));
    setAmount(e.target.value);
  };

  const handleRedeem = async () => {
    setApplyLoading(true);
    if (amount <= maxAmount) {
      try {
        await applyRedeem(amount);
        await refresh();
        setAmount('0');
        toast('Apply Success');
        refetch();
        setApplyLoading(false);
      } catch {
        setApplyLoading(false);
      }
    } else {
      setAmount(maxAmount);
      setApplyLoading(false);
    }
  };

  const handleMax = async () => {
    if (!!loadingId || applyLoading) {
      return;
    }
    await refresh();
    setAmount(maxAmount);
    setInsufficientFundsError(Number(maxAmount) > Number(userData?.canRedeemAmount));
  };

  const hasRedeem = redeemList?.length > 0;
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bl-w-[calc(100%-40px)] md:bl-w-[750px] bl-max-w-[750px] bl-flex bl-flex-col bl-items-center bl-gap-0 bl-py-5 sm:bl-pt-8 sm:bl-pb-[52px] bl-px-3 sm:bl-px-[50px] bl-bg-white bl-font-['Tomkin_Narrow'] bl-text-black">
        <DialogCloseRounded className="bl-bg-transparent bl-border-secondary bl-text-secondary hover:bl-border-primary bl-absolute bl-top-2 bl-right-4" />
        <h3 className="bl-text-xl md:bl-text-2xl/[30px] bl-text-center bl-font-bold">
          {t('redeemTitle')}
        </h3>
        <div className="bl-text-left bl-mt-8 md:bl-mt-7 bl-w-full bl-pb-2 bl-text-xs sm:bl-text-base">
          <span className="bl-text-[#666]">Available: {maxAmount} BTC</span>
          <button className="bl-text-primary bl-pl-2 md:bl-pl-3" onClick={handleMax}>
            {t('max')}
          </button>
        </div>
        <div className="bl-relative bl-w-full bl-h-14 md:bl-h-[82px] bl-flex bl-items-center sm:bl-gap-1 md:bl-gap-5 bl-mb-8 md:bl-mb-7">
          <img
            className="bl-absolute bl-left-0 bl-top-0 bl-w-full bl-h-full bl-object-cover bl-object-right bl-z-[-1]"
            src={
              isDesktop
                ? '/images/btcfi/bg-border-black-197.svg'
                : '/images/btcfi/bg-border-black-197-m.png'
            }
            alt="bg"
          />
          <img
            className="bl-absolute -bl-left-[1px] bl-top-[-2px] bl-w-[34px] bl-p-[2px] bl-pr-0 bl-h-15 bl-bg-white md:bl-hidden bl-z-[-1]"
            src="/images/btcfi/bg-border-left-m.png"
            alt="bg-left"
          />
          {/* left */}
          <div className="bl-pl-2.5 sm:bl-pl-4 bl-flex bl-items-center bl-grow">
            <BTCIcon className="bl-size-6 sm:bl-size-9 bl-shrink-0" />
            <span className="bl-ml-1 bl-mr-1.5 sm:bl-ml-4 sm:bl-mr-6 bl-text-sm sm:bl-text-xl bl-font-light md:bl-font-medium">
              BTC
            </span>
            <Input
              required
              onChange={inputChange}
              type="number"
              className="bl-w-full bl-text-base sm:!bl-text-xl bl-px-0 md:bl-mr-4 !bl-text-black !bl-bg-transparent !bl-border-0 !bl-ring-0 !bl-ring-transparent !bl-ring-offset-0 !bl-ring-offset-transparent placeholder:!bl-text-secondary bl-duration-0"
              placeholder="0.0"
              spellCheck={false}
              autoComplete="off"
              value={amount}
            />
          </div>
          {/* right */}
          <button
            className="bl-h-full bl-flex bl-items-center bl-justify-center bl-shrink-0 bl-w-[123px] md:bl-w-[198px] bl-pl-4 md:bl-pl-4.5 bl-text-white bl-text-sm md:bl-text-xl bl-font-medium bl-rounded-md"
            style={{
              clipPath: `polygon(${isDesktop ? '19.4' : '22'}% 0, 100% 0, 100% 100%, 0 100%)`,
            }}
            disabled={
              Number(amount) === 0 ||
              Number(userData?.canRedeemAmount) === 0 ||
              !!loadingId ||
              applyLoading ||
              insufficientFundsError
            }
            onClick={handleRedeem}
          >
            {applyLoading ? <Loading /> : t('applyToRedeem')}
          </button>
          {insufficientFundsError && (
            <div className="bl-text-[#D32424] bl-absolute bl-left-0 bl-bottom-[-22px] md:bl-bottom-[-24px] bl-text-xs md:bl-text-sm bl-w-full bl-text-left">
              Insufficient balance to pay gas fee
            </div>
          )}
        </div>
        <div
          className="bl-relative bl-w-full bl-pl-2.5 sm:bl-pl-3 bl-mb-2.5 md:bl-mb-3.5 bl-text-left bl-text-xs md:bl-text-sm bl-text-[#7F7F7F] bl-font-normal before:bl-absolute before:bl-left-0 before:bl-top-1.5 before:bl-size-1 before:bl-bg-[#393D44] [&>span]:bl-text-[#393D44]"
          dangerouslySetInnerHTML={{ __html: t('redeemDesc') }}
        ></div>
        <div
          className="bl-relative bl-w-full bl-pl-2.5 sm:bl-pl-3 bl-mb-2.5 md:bl-mb-3.5 bl-text-left bl-text-xs md:bl-text-sm bl-text-[#7F7F7F] bl-font-normal before:bl-absolute before:bl-left-0 before:bl-top-1.5 before:bl-size-1 before:bl-bg-[#393D44] [&>span]:bl-text-[#393D44]"
          dangerouslySetInnerHTML={{ __html: t('redeemDesc1') }}
        ></div>
        <div
          className="bl-relative bl-w-full bl-pl-2.5 sm:bl-pl-3 bl-text-left bl-text-xs md:bl-text-sm bl-text-[#7F7F7F] bl-font-normal before:bl-absolute before:bl-left-0 before:bl-top-1.5 before:bl-size-1 before:bl-bg-[#393D44]"
          dangerouslySetInnerHTML={{ __html: t('redeemDesc2') }}
        ></div>
        {hasRedeem && (
          <div className="bl-w-full bl-mt-4 md:bl-mt-7 bl-pt-4 md:bl-pt-6 bl-border-t bl-border-border">
            <div className="bl-flex bl-items-center bl-gap-1 bl-text-sm md:bl-text-base">
              <DollarIcon className="bl-size-4 md:bl-size-5" />
              {t('myRedeem')}
            </div>
            <div
              className="scrollBar-black-primary bl-w-[calc(100%+10px)] sm:bl-w-[calc(100%+13px)] bl-max-h-[220px] bl-pr-1 sm:bl-pr-[7px] bl-mt-2.5 bl-overflow-y-auto"
              ref={rootRef}
            >
              {redeemList?.map((item) => (
                <div
                  className="bl-flex bl-mb-3 bl-justify-between bl-items-center bl-h-16 md:bl-h-[86px] bl-shrink-0 bl-px-3.5 md:bl-px-6 bl-border bl-border-[#D4D4D4] bl-rounded-md"
                  key={item.redeemId}
                >
                  <div>
                    {truncateTo8Decimals(item.principalAmount)} {item.tokenSymbol}
                  </div>
                  {item.status === 'unwithdraw' ? (
                    <button
                      onClick={() => handeWithdraw(item.redeemId.toString(), item.shares)}
                      disabled={!!loadingId || applyLoading}
                      className={cn(
                        'bl-flex bl-items-center bl-w-20 md:bl-w-[116px] bl-justify-center bl-h-7 md:bl-h-[44px] bl-bg-[#E36E1B] bl-rounded-[2px] md:bl-rounded-sm bl-text-xs md:bl-text-base bl-text-black bl-border bl-border-black hover:bl-text-white',
                        {
                          'bl-bg-secondary hover:bl-text-black':
                            !!loadingId && loadingId !== item.redeemId.toString(),
                        },
                      )}
                    >
                      {loadingId === item.redeemId.toString() ? <Loading /> : t('redeemNow')}
                    </button>
                  ) : (
                    <div className="bl-text-sm md:bl-text-base">
                      <div className="bl-text-[#171717] bl-font-[350] md:bl-font-normal">
                        {t('waitApproval')}
                      </div>
                      <div className="bl-text-primary bl-text-right">
                        {t('waitDays', { count: 7 })}
                      </div>
                    </div>
                  )}
                </div>
              ))}
              <div className="bl-w-full bl-h-1" ref={nextPageTrigger}></div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
