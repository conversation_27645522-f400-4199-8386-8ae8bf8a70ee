import { useTranslation } from 'react-i18next';
import { Dialog, DialogCloseRounded, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import DSNIcon from '@/components/icons/coins/DSNIcon';
import BTRIcon from '@/components/icons/coins/BTRIcon';
import { UserDataResponse } from '@/modules/btcfiyield/types';
import { truncateTo8Decimals, cn } from '@/lib/utils';
import { useClaim, usePollTransaction } from '@/modules/btcfiyield/hooks/index';
import { chain as BitlayerNetwork } from '@/modules/user-center/config';
import { sendTransaction } from '@wagmi/core';
import { useConfig, useBalance } from 'wagmi';
import { useAccount } from '@/hooks/wallet/account';
import { useState } from 'react';
import dayjs from 'dayjs';
import { useToast } from '@/hooks/toast';
import { useRevalidator } from '@remix-run/react';
import Decimal from 'decimal.js';
import { BtcDialog } from '@/modules/mining-gala/components/btc-dialog.tsx';
import { BaseError, UserRejectedRequestError } from 'viem';

interface ClaimDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  claimSuccess?: () => void;
  children?: React.ReactNode;
  userData?: UserDataResponse;
}

const gas = new Decimal('84001');
const gasPrice = new Decimal('********');
const precision = new Decimal('1e18');
const gasFeeWei = gas.mul(gasPrice);
const gasFeeEth = gasFeeWei.div(precision);

export function ClaimDialog({ open, onOpenChange, userData }: ClaimDialogProps) {
  const { address } = useAccount({ network: BitlayerNetwork.networkType });
  const { t } = useTranslation('', { keyPrefix: 'pages.btcfi.yield' });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const revalidator = useRevalidator();
  const { pollTransaction } = usePollTransaction();
  const [noBalance, setNoBalance] = useState(false);
  const config = useConfig();

  const { data: btcBalance } = useBalance({
    address: address as `0x${string}`,
    config,
    chainId: BitlayerNetwork.chain.id as 200901,
  });

  const { mutateAsync } = useClaim();

  const refresh = () => {
    revalidator.revalidate();
  };

  const isDev = import.meta.env.MODE === 'development';

  const getTotal = () => {
    const a = new Decimal(String(userData?.canClaimBtcRewardsNow));
    const b = new Decimal(String(userData?.canClaimDsnRewardsNow));
    const c = new Decimal(String(userData?.canClaimBtrRewardsNow));
    const total = a.plus(b).plus(c).toFixed(20);
    return truncateTo8Decimals(total);
  };

  const total = getTotal();

  const getClaim = async () => {
    if (Number(total) === 0 || loading) {
      return;
    }
    setLoading(true);
    if (!btcBalance?.formatted || new Decimal(btcBalance.formatted).lt(gasFeeEth)) {
      setNoBalance(true);
      setLoading(false);
      return;
    }
    const claimData = await mutateAsync();
    const txData = claimData?.data?.tx;
    const isEnough = claimData.data.isEnough;
    // balance not enouth
    if (!isEnough) {
      setNoBalance(true);
      setLoading(false);
      return;
    }
    if (!txData) {
      setLoading(false);
      throw Error('no tx');
    }
    delete txData?.txId;
    delete txData?.gasPrice;
    if (isDev) {
      delete txData?.gas;
      delete txData?.maxFeePerGas;
      delete txData?.maxPriorityFeePerGas;
    }
    try {
      const hash = await sendTransaction(config, {
        ...txData,
      });
      pollTransaction(hash, async () => {
        toast('Claim Success');
        refresh();
        setLoading(false);
      });
    } catch (e) {
      if (e instanceof BaseError) {
        const userRejected = e.walk(
          (error) => error instanceof UserRejectedRequestError,
        ) as UserRejectedRequestError | null;
        if (!userRejected) {
          toast('Network error, please try again');
        }
      } else {
        toast('Network error, please try again');
      }
      console.log(e);
    }
    setLoading(false);
  };

  const getCurrentTotal = () => {
    const a = new Decimal(String(userData?.nextBtcRewards));
    const b = new Decimal(String(userData?.canClaimBtcRewardsNow));
    const total = a.plus(b).toFixed(20);
    return truncateTo8Decimals(total);
  };

  const rewards = [
    {
      symbol: 'WBTC',
      icon: DSNIcon,
      value: getCurrentTotal(),
    },
    {
      symbol: 'DSN',
      icon: DSNIcon,
      value: truncateTo8Decimals(userData?.dsnBtcvalueRewards || 0),
    },
    {
      symbol: 'BTR',
      icon: BTRIcon,
      value: <span className="bl-text-secondary">Ended</span>,
    },
  ];
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bl-w-[calc(100%-40px)] md:bl-w-[750px] bl-max-w-[750px] bl-flex bl-flex-col bl-items-center bl-gap-0 bl-py-5 sm:bl-pt-8 sm:bl-pb-6 bl-px-3 sm:bl-px-[50px] bl-bg-white bl-font-['Tomkin_Narrow'] bl-text-black">
        <DialogCloseRounded className="bl-bg-transparent bl-border-secondary bl-text-secondary hover:bl-border-primary bl-absolute bl-top-2 bl-right-4" />
        <h3 className="bl-text-xl md:bl-text-2xl/[30px] bl-text-center bl-font-bold">
          {t('claimTitle')}
        </h3>
        <div className="bl-relative bl-container bl-flex bl-flex-col md:bl-flex-row bl-items-center md:bl-gap-[50px] bl-mt-9 bl-px-4 sm:bl-px-8 sm:bl-py-5 bl-border bl-border-[#A9A9A9] bl-rounded-md bl-z-10 before:bl-absolute before:-bl-z-[2] before:bl-left-[-1px] before:bl-bottom-[-1px] before:bl-w-7 before:bl-h-7 before:bl-bg-white before:bl-bg-[url('/images/btcfi/angle-black-m.svg')] before:bl-bg-[length:100%] after:bl-absolute after:-bl-z-[2] after:bl-right-[-1px] after:bl-top-[-1px] after:bl-w-7 after:bl-h-7 after:bl-bg-white after:bl-bg-[url('/images/btcfi/angle-black-m.svg')] after:bl-bg-[length:100%] after:bl-rotate-180">
          <div className="bl-mt-2 sm:bl-mt-0 bl-text-sm bl-font-medium bl-shrink-0">
            {t('rewards')}
          </div>
          <div className="bl-w-full md:bl-w-[1px] bl-h-[1px] md:bl-h-6 bl-mt-2.5 bl-mb-5 md:bl-mb-0 bl-bg-secondary"></div>
          <div className="bl-w-full bl-flex bl-justify-between bl-items-center bl-mb-4.5 sm:bl-mb-0 sm:bl-gap-5 bl-grow">
            {rewards.map((item, index) => {
              const Icon = item.icon;
              return (
                <div key={item.symbol}>
                  <div className="bl-flex bl-items-center bl-gap-1 bl-w-[50px] bl-h-4 bl-px-1 bl-bg-black bl-rounded-[2px] bl-text-primary">
                    {index > 0 ? (
                      <Icon className="bl-size-2.5" />
                    ) : (
                      <div className="bl-bg-white bl-rounded-full">
                        <img
                          src="/images/btcfi/wrapped-bitcoin-wbtc-icon.png"
                          alt="wbtc"
                          className="bl-size-2.5"
                        />
                      </div>
                    )}
                    <span className="bl-text-xs/3 bl-font-[350] bl-mt-[1px]">{item.symbol}</span>
                  </div>
                  <div className="bl-mt-1.5 sm:bl-mt-2.5 bl-text-sm sm:bl-text-xl bl-font-bold">
                    {item.value}
                    <p className={cn('bl-font-medium', { 'bl-invisible': index > 1 })}>
                      {`${index > 0 ? `BTC(in ${item.symbol})` : 'WBTC'}`}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <div className="bl-w-full bl-max-w-[586px] bl-border-[0.5px] bl-border-secondary bl-my-7 sm:bl-mt-9 sm:bl-mb-7.5" />
        <div
          className="bl-font-normal  sm:[&>span]:bl-font-bold [&>span]:bl-text-primary [&>span]:bl-font-medium"
          dangerouslySetInnerHTML={{
            __html: t('claimTip', { total: truncateTo8Decimals(total) }),
          }}
        ></div>
        <div
          className="bl-max-w-[267px] sm:bl-max-w-full bl-h-9 sm:bl-h-auto bl-my-4 bl-font-normal bl-text-sm bl-text-center"
          dangerouslySetInnerHTML={{
            __html: t('estimatedTip', {
              next: truncateTo8Decimals(userData?.nextBtcRewards),
              time: dayjs(Number(userData?.nextClaimDate) * 1000).format('MMM DD YYYY'),
            }),
          }}
        ></div>
        <Button
          variant="outline-5"
          keepOverlay
          onClick={getClaim}
          loading={loading}
          disabled={Number(total) === 0 || loading}
          className={cn(
            'bl-w-[138px] bl-h-10 sm:bl-w-[236px] sm:bl-h-[51px] bl-mt-4 sm:bl-mt-8 bl-mb-4 hover:bl-text-white',
            { 'hover:bl-text-black': Number(total) === 0 || loading },
          )}
        >
          <span className="bl-font-normal bl-uppercase">{t('claim')}</span>
        </Button>
        <div className="bl-relative bl-text-sm bl-text-[#7F7F7F] bl-font-normal before:bl-absolute before:-bl-left-3 before:bl-top-[7px] before:bl-size-1 before:bl-bg-primary">
          {t('cliamOtherTip')}
        </div>
      </DialogContent>
      <BtcDialog open={noBalance} flashOnly onOpenChange={setNoBalance} />
    </Dialog>
  );
}
