import React from 'react';

interface DataPoint {
  title: string;
  value: number;
  isEmpty?: boolean; // Optional property to indicate if the point is empty
}

interface LineChartProps {
  data: DataPoint[];
  width?: number;
  height?: number;
  lineColor?: string;
  showDots?: boolean;
  verticalLines?: boolean;
  isMobile: boolean;
}

export const LineChart: React.FC<LineChartProps> = ({
  data,
  width = 600,
  height = 300,
  lineColor = '#E36E1B',
  showDots = true,
  verticalLines = true,
  isMobile,
}) => {
  const margin = { top: 25, right: isMobile ? 20 : 45, bottom: 10, left: isMobile ? 20 : 45 };
  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;

  const values = data.map((item) => item.value);
  const maxValue = Math.max(...values) || 1;
  const minValue = Math.min(...values) || 0;
  const valueRange = (maxValue - minValue) * 1.12;

  const points = data.map((item, index) => {
    const x = (0.05 + (index / (data.length - 1)) * 0.9) * innerWidth;
    const y = ((maxValue - item.value) / valueRange) * innerHeight;
    return { x, y, ...item, value: Math.floor(item.value * 100000000) / 100000000 };
  });

  const linePath = points
    .map((point, i) => `${i === 0 ? 'M' : 'L'}${point.x},${point.y}`)
    .join(' ');

  return (
    <div style={{ width, height, border: '0.5px solid #f0f0f0', borderRadius: 5 }}>
      <svg width="100%" height="100%" viewBox={`0 0 ${width} ${height}`}>
        <rect width={width} height={height} fill="white" rx="4" />
        <g transform={`translate(${margin.left},${margin.top})`}>
          {verticalLines &&
            points.map((point, index) => (
              <line
                key={`v-line-${index}`}
                x1={point.x}
                y1={0}
                x2={point.x}
                y2={innerHeight}
                stroke="#F0F0F0"
                strokeWidth="1"
              />
            ))}

          {points.map((point, index) => (
            <text
              key={`x-label-${index}`}
              x={point.x}
              y={innerHeight + 20}
              textAnchor="middle"
              fill="#666"
              fontSize="12"
            >
              {point.title}
            </text>
          ))}

          <path
            d={linePath}
            fill="none"
            stroke={lineColor}
            strokeWidth={0.5}
            strokeLinecap="round"
            strokeLinejoin="round"
          />

          {showDots &&
            points.map((point, index) => {
              if(point.isEmpty) return null;
              const labelY = point.y + (index % 2 === 0 ? -10 : 15);
              return (
                <g key={`point-${index}`}>
                  <circle
                    cx={point.x}
                    cy={point.y}
                    r={index === points.length - 1 ? 5 : 3}
                    fill="#E36E1B"
                    stroke={lineColor}
                    strokeWidth={2}
                  />
                  <text
                    x={point.x}
                    y={labelY}
                    textAnchor="middle"
                    fill="#333"
                    fontSize={index === points.length - 1 ? (isMobile ? 7 : 12) : isMobile ? 6 : 8}
                    fontWeight="thin"
                  >
                    <tspan fill="#000000">{point.value.toFixed(8)}</tspan>
                    <tspan fill="#E36E1B"> BTC</tspan>
                  </text>
                </g>
              );
            })}
        </g>
      </svg>
    </div>
  );
};
