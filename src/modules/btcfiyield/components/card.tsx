import { cn } from '@/lib/utils';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  topRightBlack?: boolean;
}

export const Card = ({ children, className, topRightBlack = true }: CardProps) => {
  return (
    <div
      className={cn(
        "bl-relative bl-w-[calc(100%-40px)] md:bl-w-full bl-bg-white md:bl-px-4 md:bl-p-0 bl-mx-auto md:bl-mt-0 bl-mb-[10px] bl-z-10 bl-border md:bl-border-0 bl-border-[#A9A9A9] bl-rounded-md before:bl-content-[''] md:before:bl-content-none before:bl-absolute before:-bl-z-[2] before:bl-left-[-1px] before:bl-bottom-[-1px] before:bl-w-7 before:bl-h-7 before:bl-bg-white before:bl-bg-[url('/images/btcfi/angle-black-m.svg')] before:bl-bg-[length:100%] after:bl-content-[''] md:after:bl-content-none after:bl-absolute after:-bl-z-[2] after:bl-right-[-1px] after:bl-top-[-1px] after:bl-w-7 after:bl-h-7 after:bl-bg-black after:bl-bg-[url('/images/btcfi/angle-white-m.svg')] after:bl-bg-[length:100%]",
        className,
        {
          "after:bl-bg-white after:bl-bg-[url('/images/btcfi/angle-black.svg')]": topRightBlack,
        },
      )}
    >
      {children}
    </div>
  );
};
