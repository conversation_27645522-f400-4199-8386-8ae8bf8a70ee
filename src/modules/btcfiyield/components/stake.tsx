import { useTranslation } from 'react-i18next';
import { ChangeEvent, useState, Fragment, useEffect } from 'react';
import { Dialog, DialogCloseRounded, DialogContent } from '@/components/ui/dialog';
import BTCIcon from '@/components/icons/coins/BTCIcon';
import EqualIcon from '@/components/icons/EqualIcon';
import Plus from '@/components/icons/Plus';
import { Input } from '@/components/ui/input';
import { useWalletBalance } from '@/hooks/wallet/balance';
import { NetworkType } from '@/wallets/config/type';
import { useAccount } from '@/hooks/wallet/account';
import { bitlayerBTC } from '@/wallets/config/tokens';
import { bitlayerTestnet, bitlayerMainnet } from '@/wallets/config/chains';
import { useMediaQuery } from '@react-hook/media-query';
import { AprResponse } from '@/modules/btcfiyield/types';
import { toPercent, truncateTo8Decimals, cn } from '@/lib/utils';
import { useRevalidator } from '@remix-run/react';
import { BtcDialog } from '@/modules/mining-gala/components/btc-dialog.tsx';
import { useStakeParam } from '@/modules/btcfiyield/hooks/btcfi';

import { useToast } from '@/hooks/toast';
import { Loading } from '@/modules/btcfi/components/Loading';
import { staticAsset } from '@/lib/static';

interface StakeDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children?: React.ReactNode;
  aprData?: AprResponse;
  refetch: () => void;
}
const isDev = import.meta.env.MODE === 'development';

export const chain = isDev ? bitlayerTestnet : bitlayerMainnet;

export function StakeDialog({ open, onOpenChange, aprData, refetch }: StakeDialogProps) {
  const { t } = useTranslation('', { keyPrefix: 'pages.btcfi.yield' });

  const isDesktop = useMediaQuery('(min-width: 768px)');
  const aprs = [
    {
      title: 'BFBTC Yield',
      symbol: <span className="bl-font-light">BTC</span>,
      value: `${toPercent(aprData?.btcApr.min)}~${toPercent(aprData?.btcApr.max)}`,
    },
    {
      title: 'Bitlayer',
      symbol: (
        <span className="bl-flex-center bl-gap-1">
          <img
            className="bl-size-4 md:bl-size-7"
            src={staticAsset('/images/user-center/bitlayer-golden.10071acb9b.gif')}
            alt=""
          />
          Points
        </span>
      ),
      value: '',
    },
  ];

  const { mutateAsync: getParams } = useStakeParam();
  const [noBalance, setNoBalance] = useState(false);
  const [insufficientFundsError, setInsufficientFundsError] = useState(false);
  const { address } = useAccount({ network: NetworkType.evm });
  const [isLimit, setIsLimit] = useState(false);
  const { data: btcBalance, refetch: refetchLegacy } = useWalletBalance({
    network: NetworkType.evm,
    address,
    token: bitlayerBTC,
    chain,
  });
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const revalidator = useRevalidator();

  const { toast } = useToast();

  const maxValue = truncateTo8Decimals(btcBalance?.formatted ?? '0');

  const setMaxValue = async () => {
    if (loading) {
      return;
    }
    const { data } = await refetchLegacy();
    const _max = truncateTo8Decimals(data?.formatted);
    setAmount(_max);
    setInsufficientFundsError(true);
    setIsLimit(false);
  };

  useEffect(() => {
    refetchLegacy();
    setAmount('');
    setInsufficientFundsError(false);
    setIsLimit(false);
  }, [address]);

  const inputChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.value.length > 10) {
      return;
    }
    setInsufficientFundsError(e.target.value >= maxValue);
    setAmount(e.target.value);
    setIsLimit(Number(e.target.value) < 0.0001);
  };

  const handleStake = async () => {
    setLoading(true);
    const result = await getParams(amount);
    setNoBalance(result?.isInsufficient ?? false);
    if (result?.success === true) {
      revalidator.revalidate();
      refetch();
      toast('stake success');
    }
    if (result?.success === false) {
      toast('Network error, please try again');
    }
    await refetchLegacy();
    setAmount('');
    setLoading(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bl-w-[calc(100%-40px)] md:bl-w-[750px] bl-max-w-[750px] bl-flex bl-flex-col bl-items-center bl-gap-0 bl-py-5 sm:bl-pt-8 sm:bl-pb-6 bl-px-3 sm:bl-px-[50px] bl-bg-white bl-font-['Tomkin_Narrow'] bl-text-black">
        <DialogCloseRounded className="bl-bg-transparent bl-border-secondary bl-text-secondary hover:bl-border-primary bl-absolute bl-top-2 bl-right-4" />
        <h3 className="bl-text-xl md:bl-text-2xl/[30px] bl-text-center bl-font-bold">
          {t('stakeTitle')}
        </h3>
        <div className="bl-mt-1.5 bl-mb-[22px] md:bl-mb-[50px] bl-text-sm md:bl-text-base">
          {t('btcfi.stakeDesc')}
        </div>
        <div className="bl-text-left bl-w-full bl-pb-2 bl-text-xs sm:bl-text-base">
          <span className="bl-text-[#666]">Available: {maxValue} BTC</span>
          <button className="bl-text-primary bl-pl-2 md:bl-pl-3" onClick={setMaxValue}>
            {t('max')}
          </button>
        </div>
        <div className="bl-relative bl-w-full bl-h-14 md:bl-h-[82px] bl-flex bl-items-center sm:bl-gap-1 md:bl-gap-5 bl-z-10">
          <img
            className="bl-absolute bl-left-0 bl-top-0 bl-w-full bl-h-full bl-z-[-1] bl-object-cover bl-object-right"
            src={
              isDesktop
                ? '/images/btcfi/bg-border-black.svg'
                : '/images/btcfi/bg-border-black-m.png'
            }
            alt="bg"
          />
          <img
            className="bl-absolute -bl-left-[1px] bl-top-[-2px] bl-w-[34px] bl-p-[2px] bl-pr-0 bl-h-15 bl-bg-white md:bl-hidden bl-z-[-1]"
            src="/images/btcfi/bg-border-left-m.png"
            alt="bg"
          />
          {/* left */}
          <div className="bl-pl-2.5 sm:bl-pl-4 bl-flex bl-items-center bl-grow">
            <BTCIcon className="bl-size-6 sm:bl-size-9 bl-shrink-0" />
            <span className="bl-ml-1 bl-mr-1.5 sm:bl-ml-4 sm:bl-mr-6 bl-text-sm sm:bl-text-xl">
              BTC
            </span>
            <Input
              required
              type="number"
              className={cn(
                'bl-w-full bl-text-base sm:!bl-text-xl bl-px-0 bl-mr-4 !bl-text-black !bl-bg-transparent !bl-border-0 !bl-ring-0 !bl-ring-transparent !bl-ring-offset-0 !bl-ring-offset-transparent placeholder:!bl-text-secondary bl-duration-0',
              )}
              placeholder="0.0"
              spellCheck={false}
              autoComplete="off"
              onChange={inputChange}
              value={amount}
              max={maxValue}
            />
          </div>
          {/* right */}
          <button
            className="bl-h-full hover:bl-text-primary bl-flex bl-items-center bl-justify-center bl-shrink-0 bl-w-[88px] md:bl-w-[172px] bl-pl-4 md:bl-pl-[30px] bl-text-white bl-text-sm md:bl-text-xl bl-font-medium bl-rounded-md md:bl-uppercase"
            style={{
              clipPath: `polygon(${isDesktop ? '22.5' : '30'}% 0, 100% 0, 100% 100%, 0 100%)`,
            }}
            disabled={Number(amount) === 0 || loading || insufficientFundsError || isLimit}
            onClick={handleStake}
          >
            {loading ? <Loading /> : t('stake')}
          </button>
          {insufficientFundsError ? (
            <div className="bl-text-[#D32424] bl-absolute bl-left-0 bl-bottom-[-22px] md:bl-bottom-[-24px] bl-text-xs md:bl-text-sm bl-w-full bl-text-left">
              Insufficient balance to pay gas fee
            </div>
          ) : isLimit ? (
            <div className="bl-text-[#D32424] bl-absolute bl-left-0 bl-bottom-[-22px] md:bl-bottom-[-24px] bl-text-xs md:bl-text-sm bl-w-full bl-text-left">
              The minimum deposit amount is 0.0001 BTC
            </div>
          ) : (
            ''
          )}
        </div>

        <div className="bl-w-full bl-max-w-[586px] bl-border-[0.5px] bl-border-[#D4D4D4] bl-my-[30px]" />
        <div className="bl-relative md:bl-pl-10 bl-container bl-flex bl-gap-8 md:bl-gap-[52px] bl-items-center bl-px-4 md:bl-px-5 bl-py-4 bl-mb-6 bl-border bl-border-[#A9A9A9] bl-rounded-md bl-z-10 before:bl-absolute before:-bl-z-[2] before:bl-left-[-1px] before:bl-bottom-[-1px] before:bl-w-7 before:bl-h-7 before:bl-bg-white before:bl-bg-[url('/images/btcfi/angle-black-m.svg')] before:bl-bg-[length:100%] after:bl-absolute after:-bl-z-[2] after:bl-right-[-1px] after:bl-top-[-1px] after:bl-w-7 after:bl-h-7 after:bl-bg-white after:bl-bg-[url('/images/btcfi/angle-black-m.svg')] after:bl-bg-[length:100%] after:bl-rotate-180">
          <div className="bl-text-xs md:bl-text-sm bl-text-[#333333] md:bl-text-[#7F7F7F] bl-font-light">
            APY
          </div>
          <EqualIcon className="bl-w-3 bl-text-secondary" />
          <div className="bl-w-full bl-pr-6 md:bl-pr-[150px] bl-flex bl-justify-between bl-items-center bl-grow">
            {aprs.map((item, index) => (
              <Fragment key={index}>
                <div className="">
                  <div>
                    <div className="bl-text-xs md:bl-text-sm bl-text-[#7F7F7F] bl-font-light">
                      {item.title}
                    </div>
                    <div className="bl-mt-2 md:bl-mt-4 bl-text-sm/[14px] bl-whitespace-nowrap md:bl-text-xl/6 bl-text-black">
                      <span className="bl-font-bold">{item.value}</span>
                      <span className="bl-font-medium"> {item.symbol}</span>
                    </div>
                  </div>
                </div>
                {index !== 1 && <Plus className="bl-text-secondary bl-w-3" />}
              </Fragment>
            ))}
          </div>
        </div>
        <div className="bl-py-4 bl-pl-[10px] bl-pr-4 bl-w-full bl-bg-[#F7F7F7] bl-rounded-lg">
          {[1, 2, 3, 4]?.map((item, index) => (
            <div
              key={index}
              className={cn(
                'bl-relative bl-w-full bl-mx-4 md:bl-ml-8 md:bl-mb-3.5 bl-mb-1 bl-text-left bl-text-sm bl-text-[#7F7F7F] bl-font-normal before:bl-absolute before:-bl-left-3 before:bl-top-[7px] before:bl-size-1 before:bl-bg-secondary',
                {
                  'bl-text-black': item === 4,
                },
              )}
            >
              {t(`btcfi.stakeDesc${item}`)}
            </div>
          ))}
        </div>
      </DialogContent>
      <BtcDialog open={noBalance} flashOnly onOpenChange={setNoBalance} />
    </Dialog>
  );
}
