import { useTranslation } from 'react-i18next';
import { ChangeEvent, useEffect, useState } from 'react';
import { Dialog, DialogCloseRounded, DialogContent } from '@/components/ui/dialog';
import { BFBTCIcon } from '@/components/icons/coins/BTCIcon';
import DollarIcon from '@/components/icons/DollarIcon';
import { Input } from '@/components/ui/input';
import { useRevalidator } from '@remix-run/react';
import { Loading } from '@/modules/btcfi/components/Loading';
import Decimal from 'decimal.js';
import { btcfiAbi } from '@/modules/btcfiyield/abi';
import { bitlayerTestnet, bitlayerMainnet } from '@/wallets/config/chains';
import { useMediaQuery } from '@react-hook/media-query';
import { useApplyReedem, useRedeemList, useClaim } from '@/modules/btcfiyield/hooks/btcfi';
import { readContract } from 'wagmi/actions';
import { BtcDialog } from '@/modules/mining-gala/components/btc-dialog.tsx';
import { UserDataResponse, BtcFiListResponse } from '@/modules/btcfiyield/types';
import { btcfi_address } from '@/modules/btcfiyield/const';
import { useConfig, useReadContract } from 'wagmi';
import { truncateTo8Decimals, cn } from '@/lib/utils';
import { useToast } from '@/hooks/toast';
import { useRequest } from 'ahooks';

interface RedeemDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children?: React.ReactNode;
  userData?: UserDataResponse;
  address?: string;
  token?: string;
}

interface RedeemPage {
  list: BtcFiListResponse['list'];
}

const isDev = import.meta.env.MODE === 'development';

export const chain = isDev ? bitlayerTestnet : bitlayerMainnet;

export function RedeemDialog({ open, onOpenChange, address, token }: RedeemDialogProps) {
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const { t } = useTranslation('', { keyPrefix: 'pages.btcfi.yield' });
  const { mutateAsync: applyRedeem } = useApplyReedem();
  const { mutateAsync: getClaim } = useClaim();
  const [loadingId, setIsLoadingId] = useState('');
  const [noBalance, setNoBalance] = useState(false);
  const [maxAmount, setMaxAmount] = useState('');
  const [applyLoading, setApplyLoading] = useState(false);
  const [btcNum, setBtcNum] = useState('');
  const [insufficientFundsError, setInsufficientFundsError] = useState(false);
  const [isLimit, setIsLimit] = useState(false);
  const [amount, setAmount] = useState('');
  const { toast } = useToast();
  const revalidator = useRevalidator();

  const config = useConfig();

  const init = () => {
    setIsLoadingId('');
    setApplyLoading(false);
    setAmount('');
    setInsufficientFundsError(false);
    setIsLimit(false);
  };

  const { refetch: getBfbtc } = useReadContract({
    abi: btcfiAbi,
    address: btcfi_address,
    functionName: 'balanceOf',
    args: [address],
  });

  useEffect(() => {
    refeshBfbtc();
  }, [open]);

  const refeshBfbtc = async () => {
    const { data: _bfbtcAmount } = await getBfbtc();
    const bfbtcAmount = _bfbtcAmount
      ? new Decimal(_bfbtcAmount.toString()).div(Decimal.pow(10, 8)).toString()
      : '0';
    setMaxAmount(bfbtcAmount);
  };

  const { data: redeemInfo, trigger: nextPageTrigger, rootRef, refetch } = useRedeemList();

  const redeemList =
    (redeemInfo?.pages as unknown as RedeemPage[])?.map((page) => page.list).flat() || [];

  useEffect(() => {
    refetch();
    refeshBfbtc();
    init();
  }, [token, address]);

  const getBtcAmount = async (bfbtcAmount: string) => {
    if (!bfbtcAmount) {
      return BigInt(0);
    }
    const _bfbtcAmount = new Decimal(bfbtcAmount.toString()).mul(Decimal.pow(10, 8)).toString();
    const btcAmount = (await readContract(config, {
      address: btcfi_address,
      abi: btcfiAbi,
      functionName: 'previewWithdraw',
      args: [_bfbtcAmount, false],
    })) as [bigint, bigint];
    return btcAmount[0];
  };

  const handeWithdraw = async (id: string) => {
    setIsLoadingId(id);
    const result = await getClaim({ id });
    setNoBalance(result?.isInsufficient ?? false);
    if (result?.success === true) {
      revalidator.revalidate();
      refetch();
      toast('claim success');
    }
    if (result?.success === false) {
      toast('Network error, please try again');
    }
    await refeshBfbtc();
    setIsLoadingId('');
  };

  const { run: fetchBtcAmount } = useRequest(
    async (value: string) => {
      const _amount = await getBtcAmount(value);
      const amount = new Decimal(_amount.toString()).div(Decimal.pow(10, 18)).toString();
      setBtcNum(truncateTo8Decimals(amount));
    },
    {
      throttleWait: 500,
      manual: true,
    },
  );

  const inputChange = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.value.length > 10) {
      return;
    }
    setInsufficientFundsError(Number(e.target.value) > Number(maxAmount));
    setAmount(e.target.value);
    fetchBtcAmount(e.target.value);
    setIsLimit(Number(e.target.value) < 0.0001);
  };

  const handleRedeem = async () => {
    if (amount <= maxAmount) {
      setApplyLoading(true);
      const btcAmount = BigInt(new Decimal(amount.toString()).mul(Decimal.pow(10, 8)).toString());
      const result = await applyRedeem(btcAmount);
      setNoBalance(result?.isInsufficient ?? false);
      if (result?.success === true) {
        revalidator.revalidate();
        refetch();
        setAmount('');
        toast('redeem success');
      }
      if (result?.success === false) {
        toast('Network error, please try again');
      }
      await refeshBfbtc();
      setApplyLoading(false);
    }
  };

  const handleMax = async () => {
    if (!!loadingId || applyLoading) {
      return;
    }
    setAmount(maxAmount);
    const _amount = await getBtcAmount(maxAmount);
    const amount = new Decimal(_amount.toString()).div(Decimal.pow(10, 18)).toString();
    setBtcNum(truncateTo8Decimals(amount));
    setIsLimit(false);
    setIsLimit(Number(maxAmount) < 0.0001);
  };

  const hasRedeem = redeemList?.length > 0;
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bl-w-[calc(100%-40px)] md:bl-w-[750px] bl-max-w-[750px] bl-flex bl-flex-col bl-items-center bl-gap-0 bl-py-5 sm:bl-pt-8 sm:bl-pb-[52px] bl-px-3 sm:bl-px-[50px] bl-bg-white bl-font-['Tomkin_Narrow'] bl-text-black">
        <DialogCloseRounded className="bl-bg-transparent bl-border-secondary bl-text-secondary hover:bl-border-primary bl-absolute bl-top-2 bl-right-4" />
        <h3 className="bl-text-xl md:bl-text-2xl/[30px] bl-text-center bl-font-bold">
          {t('btcfi.redeemTitle')}
        </h3>
        <div className="bl-text-left bl-mt-8 md:bl-mt-7 bl-w-full bl-pb-2 bl-text-xs sm:bl-text-base">
          <span className="bl-text-[#666]">Available: {maxAmount} BFBTC</span>
          <button className="bl-text-primary bl-pl-2 md:bl-pl-3" onClick={handleMax}>
            {t('max')}
          </button>
        </div>
        <div className="bl-relative bl-w-full bl-h-14 md:bl-h-[82px] bl-flex bl-items-center sm:bl-gap-1 md:bl-gap-5 bl-mb-8 md:bl-mb-7">
          <img
            className="bl-absolute bl-left-0 bl-top-0 bl-w-full bl-h-full bl-object-cover bl-object-right bl-z-[-1]"
            src={
              isDesktop
                ? '/images/btcfi/bg-border-black-197.svg'
                : '/images/btcfi/bg-border-black-197-m.png'
            }
            alt="bg"
          />
          <img
            className="bl-absolute -bl-left-[1px] bl-top-[-2px] bl-w-[34px] bl-p-[2px] bl-pr-0 bl-h-15 bl-bg-white md:bl-hidden bl-z-[-1]"
            src="/images/btcfi/bg-border-left-m.png"
            alt="bg-left"
          />
          {/* left */}
          <div className="bl-pl-2.5 sm:bl-pl-4 bl-flex bl-items-center bl-grow">
            <BFBTCIcon className="bl-size-6 sm:bl-size-9 bl-shrink-0" />
            <span className="bl-ml-1 bl-mr-1.5 sm:bl-ml-4 sm:bl-mr-6 bl-text-sm sm:bl-text-xl bl-font-light md:bl-font-medium">
              BFBTC
            </span>
            <Input
              required
              onChange={inputChange}
              type="number"
              className="bl-w-full bl-text-base sm:!bl-text-xl bl-px-0 md:bl-mr-4 !bl-text-black !bl-bg-transparent !bl-border-0 !bl-ring-0 !bl-ring-transparent !bl-ring-offset-0 !bl-ring-offset-transparent placeholder:!bl-text-secondary bl-duration-0"
              placeholder="0.0"
              spellCheck={false}
              autoComplete="off"
              value={amount}
            />
          </div>
          {/* right */}
          <button
            className="bl-h-full hover:bl-text-primary bl-flex bl-items-center bl-justify-center bl-shrink-0 bl-w-[123px] md:bl-w-[198px] bl-pl-4 md:bl-pl-4.5 bl-text-white bl-text-sm md:bl-text-xl bl-font-medium bl-rounded-md"
            style={{
              clipPath: `polygon(${isDesktop ? '19.4' : '22'}% 0, 100% 0, 100% 100%, 0 100%)`,
            }}
            disabled={
              Number(amount) === 0 ||
              Number(maxAmount) === 0 ||
              !!loadingId ||
              applyLoading ||
              insufficientFundsError ||
              isLimit
            }
            onClick={handleRedeem}
          >
            {applyLoading ? <Loading /> : t('applyToRedeem')}
          </button>
          {insufficientFundsError ? (
            <div className="bl-text-[#D32424] bl-absolute bl-left-0 bl-bottom-[-22px] md:bl-bottom-[-24px] bl-text-xs md:bl-text-sm bl-w-full bl-text-left">
              Insufficient balance
            </div>
          ) : isLimit ? (
            <div className="bl-text-[#D32424] bl-absolute bl-left-0 bl-bottom-[-22px] md:bl-bottom-[-24px] bl-text-xs md:bl-text-sm bl-w-full bl-text-left">
              The minimum redeem amount is 0.0001 BFBTC
            </div>
          ) : (
            ''
          )}
        </div>

        <div
          className={cn(
            'bl-h-12 bl-rounded-[6px] bl-font-medium bl-text-[#171717] bl-px-4 bl-bg-[rgba(227,110,27,0.08)] bl-w-full bl-flex bl-items-center',
          )}
        >
          <div>
            <span className="bl-pr-2 bl-text-[#393D44] bl-font-normal">You will get</span>
            <span>{Number(amount) <= 0 || insufficientFundsError ? '--' : btcNum}BTC</span>
          </div>
        </div>

        <div className="bl-relative bl-text-sm bl-w-full bl-ml-8 bl-text-left bl-mt-4 bl-text-[#7F7F7F] bl-font-normal before:bl-absolute before:-bl-left-3 before:bl-top-[7px] before:bl-size-1 before:bl-bg-secondary">
          {/* {t('stakeDesc1')} */}
          During the time from application to approval, earnings will continue to accrue, and the
          actual amount may vary.
        </div>

        {hasRedeem && (
          <div className="bl-w-full bl-mt-4 md:bl-mt-4 bl-pt-4 md:bl-pt-6 bl-border-t bl-border-border">
            <div className="bl-flex bl-items-center bl-gap-1 bl-text-sm md:bl-text-base">
              <DollarIcon className="bl-size-4 md:bl-size-5" />
              {t('myRedeem')}
            </div>
            <div
              className="scrollBar-black-primary bl-w-[calc(100%+10px)] sm:bl-w-[calc(100%+13px)] bl-max-h-[220px] bl-pr-1 sm:bl-pr-[7px] bl-mt-2.5 bl-overflow-y-auto"
              ref={rootRef}
            >
              {redeemList?.map((item) => (
                <div
                  className="bl-flex bl-mb-3 bl-justify-between bl-items-center bl-h-16 md:bl-h-[86px] bl-shrink-0 bl-px-3.5 md:bl-px-6 bl-border bl-border-[#D4D4D4] bl-rounded-md"
                  key={item.id}
                >
                  <div>{item.btcAmount}BTC</div>
                  {item.status === 2 && (
                    <button
                      onClick={() => handeWithdraw(item.id)}
                      disabled={!!loadingId || applyLoading}
                      className={cn(
                        'bl-flex bl-items-center bl-w-20 md:bl-w-[116px] bl-justify-center bl-h-7 md:bl-h-[44px] bl-bg-[#E36E1B] bl-rounded-[2px] md:bl-rounded-sm bl-text-xs md:bl-text-base bl-text-black bl-border bl-border-black hover:bl-text-white',
                        {
                          'bl-bg-secondary hover:bl-text-black':
                            !!loadingId && loadingId !== item.id.toString(),
                        },
                      )}
                    >
                      {loadingId === item.id.toString() ? (
                        <Loading />
                      ) : (
                        <span>
                          {t('claim')} <span className="bl-hidden md:bl-inline">Now</span>
                        </span>
                      )}
                    </button>
                  )}
                  {item.status === 1 && (
                    <div className="bl-text-sm md:bl-text-base">
                      <div className="bl-text-[#171717] bl-font-[350] md:bl-font-normal">
                        {t('waitApproval')}
                      </div>
                      <div className="bl-text-primary bl-text-right">
                        {t('waitDays', { count: 7 })}
                      </div>
                    </div>
                  )}
                  {item.status === 4 && (
                    <div className="bl-text-sm bl-w-20 md:bl-w-[116px] bl-text-center md:bl-text-base">
                      Claimed
                    </div>
                  )}
                </div>
              ))}
              <div className="bl-w-full bl-h-1" ref={nextPageTrigger}></div>
            </div>
          </div>
        )}
      </DialogContent>
      <BtcDialog open={noBalance} flashOnly onOpenChange={setNoBalance} />
    </Dialog>
  );
}
