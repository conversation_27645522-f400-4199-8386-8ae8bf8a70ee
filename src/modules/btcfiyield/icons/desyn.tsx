import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    {...props}
  >
    <circle cx="10" cy="10.5" r="10" fill="white" />
    <path
      d="M9.27373 3.66612C9.72156 3.44463 10.2616 3.44463 10.7094 3.66612L17.2953 6.90626C17.6641 7.08978 17.9341 7.43152 17.9802 7.83021C18.0329 8.31116 17.7761 8.76048 17.3348 8.97565L10.7029 12.2348C10.4789 12.3424 10.2353 12.3993 9.985 12.3993C9.74132 12.3993 9.49106 12.3424 9.26714 12.2348L3.65598 9.47559C3.53743 9.41863 3.39913 9.50091 3.39913 9.62747V10.3426C3.39913 10.4059 3.43865 10.4628 3.49133 10.4945L9.83352 13.6207C9.88621 13.646 9.94548 13.646 9.99158 13.6207L16.2284 10.6337C16.6433 10.4312 17.1373 10.4691 17.5192 10.7476C17.8354 10.9754 18 11.3488 18 11.7222V13.0638C18 13.5068 17.7432 13.9118 17.3348 14.1143L10.6172 17.3355C10.3999 17.443 10.1562 17.5 9.90597 17.5C9.66229 17.5 9.41861 17.4494 9.1947 17.3418L2.21367 13.9624C2.09513 13.9055 2.02268 13.7916 2.02268 13.6587V12.6904C2.02268 12.5638 2.16099 12.4816 2.27295 12.5385L9.8467 16.1267C9.89938 16.1521 9.95866 16.1521 10.0048 16.1267L16.5248 13.0005C16.584 12.9689 16.6236 12.9119 16.6236 12.8486V12.2221C16.6236 12.0955 16.4853 12.0133 16.3733 12.0702L10.6436 14.8168C10.2023 15.0319 9.66229 15.0319 9.21445 14.8104L2.68127 11.5893C2.66151 11.5766 2.64176 11.5703 2.622 11.564C2.47052 11.5007 2.00293 11.2982 2.00293 10.7096V8.096C1.99634 8.04537 2.00293 8.00107 2.00293 7.95045C2.00293 7.52012 2.25319 7.12775 2.65493 6.92525L9.27373 3.66612ZM10.0706 4.86852C10.0179 4.8432 9.95866 4.8432 9.91255 4.86852L3.95893 7.79856C3.8338 7.86185 3.8338 8.03904 3.95893 8.096L9.91255 11.026C9.96524 11.0514 10.0245 11.0514 10.0706 11.026L16.0242 8.096C16.1494 8.03272 16.1494 7.85552 16.0242 7.79856L10.0706 4.86852Z"
      fill="black"
    />
  </svg>
);

export default SvgComponent;

export const DesynIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      {...props}
    >
      <circle cx="11" cy="11" r="10.5" fill="black" />
      <path
        d="M10.426 5.63052C10.7779 5.45649 11.2022 5.45649 11.5541 5.63052L16.7287 8.17635C17.0185 8.32054 17.2306 8.58905 17.2668 8.9023C17.3082 9.2802 17.1064 9.63323 16.7597 9.80229L11.5489 12.363C11.373 12.4476 11.1815 12.4923 10.9849 12.4923C10.7934 12.4923 10.5968 12.4476 10.4208 12.363L6.01206 10.1951C5.91892 10.1504 5.81025 10.215 5.81025 10.3144V10.8763C5.81025 10.926 5.8413 10.9708 5.8827 10.9956L10.8658 13.452C10.9072 13.4719 10.9538 13.4719 10.99 13.452L15.8904 11.105C16.2164 10.9459 16.6045 10.9758 16.9046 11.1945C17.153 11.3735 17.2824 11.6669 17.2824 11.9603V13.0144C17.2824 13.3625 17.0806 13.6807 16.7597 13.8398L11.4816 16.3707C11.3109 16.4552 11.1194 16.5 10.9228 16.5C10.7313 16.5 10.5398 16.4602 10.3639 16.3757L4.87882 13.7205C4.78568 13.6757 4.72876 13.5862 4.72876 13.4818V12.721C4.72876 12.6216 4.83743 12.557 4.9254 12.6017L10.8762 15.421C10.9176 15.4409 10.9642 15.4409 11.0004 15.421L16.1233 12.9647C16.1698 12.9398 16.2009 12.8951 16.2009 12.8453V12.3531C16.2009 12.2536 16.0922 12.189 16.0042 12.2338L11.5023 14.3917C11.1556 14.5608 10.7313 14.5608 10.3794 14.3868L5.24622 11.8559C5.2307 11.8459 5.21517 11.8409 5.19965 11.836C5.08063 11.7862 4.71324 11.6271 4.71324 11.1647V9.11114C4.70806 9.07136 4.71324 9.03656 4.71324 8.99678C4.71324 8.65866 4.90987 8.35038 5.22552 8.19126L10.426 5.63052ZM11.0521 6.57526C11.0107 6.55537 10.9642 6.55537 10.9279 6.57526L6.2501 8.87744C6.15178 8.92717 6.15178 9.06639 6.2501 9.11114L10.9279 11.4133C10.9693 11.4332 11.0159 11.4332 11.0521 11.4133L15.73 9.11114C15.8283 9.06142 15.8283 8.92219 15.73 8.87744L11.0521 6.57526Z"
        fill="#FFF6F0"
      />
    </svg>
  );
};
