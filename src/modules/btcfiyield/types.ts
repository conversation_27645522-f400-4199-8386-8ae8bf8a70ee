export interface RuleDataResponse {
  minTotalApr: string;
  maxTotalApr: string;
  minBtcApr: string;
  maxBtcApr: string;
  dsnApr: string;
  btrApr: string;
}

export interface AprResponse {
  btcApr: { min: number; max: number };
  btrApr: number;
}

export type ChartsItem = {
  timestamp: number;
  balance: string;
};

export interface AssetResponse {
  list: ChartsItem[];
}

export type TxProps = {
  data: string;
  from: string;
  gas?: number;
  gasPrice?: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  nonce: number;
  to: string;
  tx_hash_data: string;
  txId?: string;
  value: string;
};

export interface UserDataResponse {
  totalBtcAmount: string;
  canRedeemAmount: string;
  canClaimBtcRewardsNow: string;
  canClaimDsnRewardsNow: string;
  canClaimBtrRewardsNow: string;
  nextBtcRewards: string;
  nextDsnRewards: string;
  nextBtrRewards: string;
  btrBtcvalueRewards: string;
  nextClaimDate: number;
  dsnBtcvalueRewards: string;
}

export interface StakeRequest {
  pool_address: string;
  issueToken: string;
  slippage: string;
  issueAmount: string;
}

export interface StakeResponse {
  tx: TxProps;
  sendApprove: boolean;
  swapRouter: string;
  isEnough: boolean;
}

export interface PoolAddressRequest {
  pool_address: string;
}

export interface ClaimResponse {
  tx: TxProps;
  isEnough: boolean;
}

export interface ApplyRedeemRequest {
  amount: string;
  pool_address: string;
}

export interface ApplyRedeemResponse {}

export interface TvlResponse {
  maxTvl: string;
  tvl: string;
  tvlSwitch: number;
  maxTvlV2: string;
  tvlV2: string;
  tvlSwitchV2: number;
}

export interface RedeemListRequest {
  pool_address: string;
  page: string;
  page_size: string;
  status?: string;
}

export interface BtrPointsResponse {
  reward: string;
}

export interface StatusResponse {
  data: boolean;
}

export interface FinishResponse {}

type RedeemListItem = {
  redeemId: number;
  walletAddress: string;
  shares: string;
  tokenAddress: string;
  tokenSymbol: string;
  principalAmount: string;
  status: 'freeze' | 'unwithdraw' | 'completed';
  canRedeemTimestamp: number;
  createdAt: number;
};

type BtcFiListItem = {
  btcAmount: string;
  id: string;
  reward: string;
  status: number;
  user: string;
  withdrawId: string;
};

export interface RedeemListResponse {
  list: RedeemListItem[];
  curPage: number;
}

export interface BtcFiInfoResponse {
  user: {
    btc?: string;
    bfbtc?: string;
    noWithdrawReward?: number;
    withdrawReward?: number;
    depositCount: number;
  };
}

export interface BtcFiListResponse {
  list: BtcFiListItem[];
}

export interface ReportRequest {
  tx_hash: string;
}

export interface RedeemParamRequest {
  redeem_id: string;
}

export interface RedeemParamResponse {
  tx: TxProps;
}

export interface AllowanceRequest {
  token_list: string[]; //******************************************
  pool_address: string;
}

export interface AllowanceResponse {
  allowance: {
    [address: string]: string;
  };
}
export interface ApproveRequest {
  pool_address: string;
  token: string;
  amount: string;
}

export interface TransactionRequest {
  tx_hash: string;
}
export interface TransactionResponse {
  desynExcuteStatus: 0 | 1;
}

export interface ApproveResponse {
  resetFlag: boolean;
  tx: TxProps;
}

export interface UpdateRedeemTxRequest {
  tx_id: string;
  tx_hash: string;
}

export interface UpdateRedeemTxResponse {}
