import { Client, DataWrapper } from '@/lib/api/client';
import { ActionFunctionArgs } from '@remix-run/cloudflare';
import { getSession } from '@/modules/session';
import {
  RuleDataResponse,
  UserDataResponse,
  PoolAddressRequest,
  StakeRequest,
  StakeResponse,
  ClaimResponse,
  ApplyRedeemRequest,
  ApplyRedeemResponse,
  RedeemListRequest,
  RedeemListResponse,
  RedeemParamRequest,
  RedeemParamResponse,
  AllowanceRequest,
  AllowanceResponse,
  ApproveRequest,
  ApproveResponse,
  UpdateRedeemTxRequest,
  UpdateRedeemTxResponse,
  TransactionRequest,
  TvlResponse,
  AprResponse,
  BtcFiInfoResponse,
  BtcFiListResponse,
  ReportRequest,
  AssetResponse,
} from './types';

class BTCFIAPI {
  protected client: Client;
  protected baseRoute: string = import.meta.env.VITE_USER_CENTER_BASE_ROUTE;

  constructor(client: Client) {
    this.client = client;
  }

  protected buildRoute(route: string) {
    return `${this.baseRoute}${route}`;
  }

  async getRuleData(): Promise<DataWrapper<RuleDataResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/rule_data'));
  }

  async getAprData(): Promise<DataWrapper<AprResponse>> {
    return this.client.request('get', this.buildRoute('/v1/task/yield/bitfi/apr'));
  }

  async getUserData(params: PoolAddressRequest): Promise<DataWrapper<UserDataResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/user_data'), params);
  }

  async getStakeParam(params: StakeRequest): Promise<DataWrapper<StakeResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/stake_param'), params);
  }

  async getClaimParam(params: PoolAddressRequest): Promise<DataWrapper<ClaimResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/claim_param'), params);
  }

  async getApplyRedeem(params: ApplyRedeemRequest): Promise<DataWrapper<ApplyRedeemResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/apply_redeem'), params);
  }

  async getTvl(params: PoolAddressRequest): Promise<DataWrapper<TvlResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/get_tvl'), params);
  }

  async claimPoint(): Promise<DataWrapper<TvlResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/bitfi/claim_point'));
  }

  async cancelClaim(params: PoolAddressRequest): Promise<DataWrapper<TvlResponse>> {
    return this.client.request(
      'post',
      this.buildRoute('/v1/task/yield/bitfi/cancel_claim_point'),
      params,
    );
  }

  async getAsset(): Promise<DataWrapper<AssetResponse>> {
    return this.client.request('get', this.buildRoute('/v1/task/yield/balance_changes'));
  }

  async getRedeemList(params: RedeemListRequest): Promise<DataWrapper<RedeemListResponse>> {
    return this.client.request(
      'post',
      this.buildRoute('/v1/task/yield/applied_redeem_list'),
      params,
    );
  }

  async getBtcfiList({
    page,
    limit,
  }: {
    page: string;
    limit: string;
  }): Promise<DataWrapper<BtcFiListResponse>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });
    return this.client.request(
      'get',
      this.buildRoute('/v1/task/yield/bitfi/user_order_history'),
      params,
    );
  }

  async getBtcfiInfo(): Promise<DataWrapper<BtcFiInfoResponse>> {
    return this.client.request('get', this.buildRoute('/v1/task/yield/bitfi/user'));
  }

  async getReportStake(params: ReportRequest): Promise<DataWrapper<BtcFiListResponse>> {
    return this.client.request(
      'post',
      this.buildRoute('/v1/task/yield/bitfi/report_stake'),
      params,
    );
  }

  async getFinish({
    tx_hash,
    type,
  }: {
    tx_hash: string;
    type: string;
  }): Promise<DataWrapper<BtcFiListResponse>> {
    const params = new URLSearchParams({ tx_hash });
    if (type) {
      params.set('type', type.toString());
    }
    return this.client.request(
      'get',
      this.buildRoute('/v1/task/yield/bitfi/check_withdraw_request_finish'),
      params,
    );
  }

  async getRedeemParam(params: RedeemParamRequest): Promise<DataWrapper<RedeemParamResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/redeem_param'), params);
  }

  async getAllowance(params: AllowanceRequest): Promise<DataWrapper<AllowanceResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/allowance'), params);
  }

  async getApprove(params: ApproveRequest): Promise<DataWrapper<ApproveResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/approve'), params);
  }

  async getReceipt(params: TransactionRequest): Promise<DataWrapper<ApproveResponse>> {
    return this.client.request(
      'post',
      this.buildRoute('/v1/task/yield/get_transaction_receipt'),
      params,
    );
  }

  async getUpdateRedeemTx(
    params: UpdateRedeemTxRequest,
  ): Promise<DataWrapper<UpdateRedeemTxResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/yield/update_redeem_tx'), params);
  }
}

export const createAPIRquest = async (request: ActionFunctionArgs['request']) => {
  const session = await getSession(request);
  const token = session.get('user.token') as string | undefined;

  const cookieHeader = request.headers.get('Cookie');

  const cookies = cookieHeader
    ? Object.fromEntries(
        cookieHeader?.split(';').map((cookie) => {
          const [key, value] = cookie.trim().split('=');
          return [key, decodeURIComponent(value)];
        }),
      )
    : {};
  const visitorId = cookies['user.visitorId'] || null;
  const cfConnectingIp = request.headers.get('CF-Connecting-IP');
  const ip = cfConnectingIp || '';
  const endpoint = import.meta.env.VITE_USER_CENTER_ENDPOINT;
  const client = new Client(endpoint);
  if (token) {
    client.setToken(token);
  }
  if (ip) {
    client.setIp(ip);
  }
  if (visitorId) {
    client.setVisitorId(visitorId);
  }
  const api = new BTCFIAPI(client);
  return api;
};
