import { getBridgeAPI } from '@/lib/api/bridge.client';
import { CrossChainTransaction } from '@/lib/api/models';
import { chainSymbolMap, zkbridgeChainMap } from '@/wallets/config/chains';
import { chainTokens } from '@/wallets/config/tokens';
import { BaseChainType, Token, TokenType } from '@/wallets/config/type';
import { TransactionData, TransferStatus } from '@/modules/bridge/transactions/types';
import { useMutation, useQuery } from '@tanstack/react-query';
import { BigNumber, utils } from 'ethers';

interface BridgeNotifyParams {
  fromChain: BaseChainType;
  toChain: BaseChainType;
  from: string;
  to: string;
  amount: string;
  token: Token;
  txId: string;
}

export const useBridgeNotify = () => {
  const { mutateAsync: notify } = useMutation({
    mutationKey: ['bridgeNotify'] as const,
    mutationFn: async (params: BridgeNotifyParams) => {
      const { token, fromChain } = params;
      await getBridgeAPI({ testnet: fromChain.testnet }).createTransaction({
        fromChain: fromChain.symbol,
        toChain: params.toChain.symbol,
        from: params.from,
        to: params.to,
        assetId: token.id === 'BTC' ? `${token.symbol}_${fromChain.symbol}` : token.id,
        amount: params.amount,
        fromTxId: params.txId,
      });
    },
  });
  return notify;
};

export const statusMap: { [key: number]: TransferStatus } = {
  0: 'pending',
  1: 'pending',
  2: 'completed',
  3: 'pending', // 'failed',
};

export const assetTypeMap: Record<number, TokenType> = {
  0: 'native',
  1: 'brc20',
  2: 'rune',
  10: 'erc20',
};

export const tokenAssetTypeMap: Record<TokenType, number> = {
  native: 0,
  brc20: 1,
  rune: 2,
  erc20: 10,
};

export const mapTransaction = (data: CrossChainTransaction): TransactionData => {
  const tokenId = data.assetId.split('_')[0];

  let fromChain: BaseChainType;
  let toChain: BaseChainType;

  if (data.zkbridge) {
    fromChain = zkbridgeChainMap[data.zkbridge.srcAppId];
    toChain = zkbridgeChainMap[data.zkbridge.dstAppId];
  } else {
    fromChain = chainSymbolMap[data.fromChain];
    toChain = chainSymbolMap[data.toChain];
  }

  let amount: BigNumber;
  const token = chainTokens[fromChain.id].find(
    (t) => t.id === tokenId || t.id.toLowerCase() === tokenId,
  ) as Token;
  if (data.zkbridge) {
    amount = utils.parseUnits(data.amount, token.decimals);
  } else {
    amount = BigNumber.from(data.amount);
  }

  let finalStatus: 0 | 1 | 2 | 3 = 0;
  if (data.fromTxStatus === 3 || data.toTxStatus === 3) {
    finalStatus = 3;
  } else if (data.fromTxStatus === 2 && data.toTxStatus === 2) {
    finalStatus = 2;
  } else {
    finalStatus = 0;
  }

  return {
    from: data.from,
    to: data.to,
    fromChain,
    toChain,
    token,
    amount,
    fromTxId: data.fromTxId,
    fromTxStatus: statusMap[data.fromTxStatus],
    toTxId: data.toTxId,
    toTxStatus: statusMap[data.toTxStatus || 0],
    status: statusMap[finalStatus],
    reason: data.reason,
    createdTime: data.createdTime,
    finishedTime: data.finishedTime,
  };
};

export const mapToSerializable = (data: TransactionData): CrossChainTransaction => {
  return {
    from: data.from,
    to: data.to,
    fromChain: data.fromChain.symbol,
    toChain: data.toChain.symbol,
    assetId: `${data.token.id}_${data.fromChain.symbol}`,
    assetType: tokenAssetTypeMap[data.token.type],
    amount: data.amount.toString(),
    fromTxId: data.fromTxId,
    fromTxStatus: data.fromTxStatus === 'pending' ? 0 : data.fromTxStatus === 'completed' ? 2 : 3,
    toTxId: data.toTxId,
    toTxStatus:
      data.toTxStatus === 'pending'
        ? 0
        : data.toTxStatus === 'completed'
          ? 2
          : data.toTxStatus === 'failed'
            ? 3
            : undefined,
    reason: data.reason,
    createdTime: data.createdTime,
    finishedTime: data.finishedTime,
  };
};

interface TransactionParams {
  txId: string;
  testnet?: boolean;
  refetchInterval?: number | false;
}

export const useTransaction = ({ txId, testnet, refetchInterval }: TransactionParams) => {
  return useQuery({
    queryKey: ['bridgeTransaction', { txId, testnet }] as const,
    queryFn: async (keys) => {
      const [, { txId }] = keys.queryKey;
      const res = await getBridgeAPI({ testnet }).getTransaction(txId);
      if (!res.result.txs.length) {
        return null;
      }
      return mapTransaction(res.result.txs[0]);
    },
    refetchInterval,
  });
};

export const useTransactionList = ({
  from,
  init,
  testnet,
}: {
  from?: string | null;
  init?: CrossChainTransaction;
  testnet?: boolean;
}) => {
  return useQuery({
    queryKey: ['bridgeTransactionList', from, testnet] as const,
    queryFn: async (keys) => {
      if (!keys.queryKey[1]) {
        throw new Error('Invalid address');
      }
      const res = await getBridgeAPI({ testnet }).getTransactionList(keys.queryKey[1]);
      let data = res.result.txs.map(mapTransaction);

      if (init) {
        const index = data.findIndex((tx) => tx.fromTxId === init.fromTxId);
        if (index === -1) {
          data = [mapTransaction(init), ...data];
        }
      }

      return data;
    },
  });
};
