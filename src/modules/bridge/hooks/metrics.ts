import { load as loadFingerPrint } from '@fingerprintjs/fingerprintjs';
import { md5 as md5js } from 'js-md5';
import { useEffect } from 'react';

interface Metrics {
  address?: string;
  wallet?: string;
  chain?: string;
}

let requestQueen: string[] = [];

export const useMetrics = ({ address, wallet = '', chain = '' }: Metrics) => {
  const getParamsStr = (paramsObj: Record<string, string | number>) => {
    return Object.entries(paramsObj)
      .map(([key, value]) => encodeURIComponent(key) + '=' + encodeURIComponent(value as string))
      .join('&');
  };

  const initMetrics = async () => {
    if (!address) {
      return;
    }

    try {
      const fp = await loadFingerPrint();
      const { visitorId } = await fp.get();

      const ts = Math.floor(new Date().getTime() / 1000);
      const minTs = Math.floor(ts / 60);
      const _salt = md5js(`${address}${minTs}`);
      const salt = _salt.substr(5, 10);
      const requestQueenParams = {
        address: address,
        fp: visitorId,
        wallet: wallet.split(' ').join('_'),
        chain: chain.split(' ').join('_'),
      };
      // 检查缓存中是否存在该请求
      const queenParamsStr = getParamsStr(requestQueenParams);
      if (requestQueen.includes(queenParamsStr)) {
        return null;
      }
      requestQueen.push(queenParamsStr);

      const _params = {
        ...requestQueenParams,
        ts: ts,
      };
      const signParams = {
        ..._params,
        salt: salt,
      };
      const signParamsStr = getParamsStr(signParams);
      const sign = md5js(signParamsStr);

      const params = {
        ..._params,
        sign: sign,
      };
      const paramsStr = getParamsStr(params);
      const endpoint = import.meta.env.VITE_USER_CENTER_ENDPOINT;
      const metrics = await fetch(`${endpoint}/auth/v1/user/metrics?${paramsStr}`, {
        method: 'GET',
      });
      const metricsJson = await metrics.json();
      return metricsJson;
    } catch (error) {
      console.error('metrics', error);
      return null;
    }
  };

  useEffect(() => {
    initMetrics();
  }, [address, wallet, chain]);

  return { initMetrics };
};
