import { buildWorkflow, TransferProps, Workflow } from '@/modules/bridge/transactions';
import { TransactionData, transferHintAtom } from '@/modules/bridge/transactions/types';
import { UserRejectedError } from '@/modules/bridge/transactions/errors';
import { useCallback, useMemo, useState } from 'react';
import { useConfig } from 'wagmi';
import { deepEqual, ResolvedRegister } from '@wagmi/core';
import { useBridgeNotify } from './transaction';
import { UseEnsureOnChain } from '@/hooks/wallet/chains';
import { NetworkType } from '@/wallets/config/type';
import { utils } from 'ethers';
import { useAtomValue } from 'jotai';
import { ValidatorParams } from '@/modules/bridge/transactions/validator';
import { useQuery } from '@tanstack/react-query';
import { useDebounceCallback } from '@react-hook/debounce';
import useChange from '@react-hook/change';

// There can be multiple states for a transfer bewteen idle and success
export type TransferState = 'idle' | 'approving' | 'loading' | 'comfirming' | 'success' | 'error';

export interface TransferOptions {
  onSuccess?: (data: TransactionData) => void;
  onError?: (error: Error) => void;
  onStale?: () => void;
}

// Temporary hardcoding of token limits
const tokenLimitChains = ['ethereum', 'sepolia'];
const tokenMinLimites: Record<string, Record<string, string>> = {
  ethereum: {
    ETH: '10',
    USDT: '30000',
    USDC: '30000',
  },
  sepolia: {
    ETH: '0.0006',
    TST: '100',
  },
};

export const shouldUseThirdParty = ({ fromChain, token, amount }: TransferProps) => {
  if (!tokenLimitChains.includes(fromChain.id)) {
    return false;
  }

  const limits = tokenMinLimites[fromChain.id];
  if (!limits) {
    return false;
  }

  const minLimit = limits[token.id];
  if (!minLimit) {
    return false;
  }

  const limit = utils.parseUnits(minLimit, token.decimals);
  if (amount.lt(limit)) {
    return true;
  }

  return false;
};

export const useTransferHint = () => {
  return useAtomValue(transferHintAtom);
};

export const useWorkflow = (params: Partial<TransferProps>) => {
  const config = useConfig();
  const { from, fromChain, toChain, token } = params;

  return useMemo(() => {
    if (!fromChain || !token || !from || !toChain) {
      return undefined;
    }
    return buildWorkflow(
      { from, fromChain, toChain, token },
      {
        config: config as unknown as ResolvedRegister['config'],
      },
    );
  }, [fromChain, toChain, token, from]);
};

export const useValidate = (workflow: Workflow | undefined, params: ValidatorParams) => {
  const [debouncing, setDebouncing] = useState(false);
  const [value, setValue] = useState(params);

  const setValueDebounce = useDebounceCallback((newValue: ValidatorParams) => {
    setValue(newValue);
    setDebouncing(false);
  }, 500);

  useChange(params, (current, prev) => {
    if (!deepEqual(current, prev)) {
      setDebouncing(true);
      setValueDebounce(current);
    }
  });

  const { data, isFetching } = useQuery({
    queryKey: ['workflow/validate', value] as const,
    queryFn: async ({ queryKey }) => {
      const [, params] = queryKey;
      if (!workflow) {
        return [false, undefined];
      }
      return workflow.validator(params);
    },
    placeholderData: [false, undefined],
    initialData: [false, undefined],
    enabled: !!workflow,
  });

  if (debouncing) {
    return [false, undefined];
  }

  if (!data || isFetching) {
    return [false, undefined];
  }

  return data;
};

export const useTransfer = () => {
  const [data, setData] = useState<TransactionData | null>(null); // transfer result
  const [state, setState] = useState<TransferState>('idle');
  const [error, setError] = useState<Error | null>(null);

  const { ensure: ensureOnChain } = UseEnsureOnChain();
  const notify = useBridgeNotify();
  const config = useConfig();

  const transfer = useCallback(
    async (params: TransferProps, options: TransferOptions = {}) => {
      try {
        setState('loading');

        // make sure the user is on the correct chain
        await ensureOnChain({ chain: params.fromChain });

        const workflow = buildWorkflow(params, {
          config: config as unknown as ResolvedRegister['config'],
        });

        await workflow.next(params);
        const txId = workflow.txId;
        console.log('transaction id', txId);
        if (!txId) {
          return;
        }

        setTimeout(() => {
          if (params.fromChain.networkType === NetworkType.evm) return;
          notify({
            fromChain: params.fromChain,
            toChain: params.toChain,
            from: params.from,
            to: params.to,
            amount: params.amount.toString(),
            token: params.token,
            txId,
          }).catch((e) => {
            console.warn('something error occurred while notify backend', e);
          });
        }, 0);

        const data: TransactionData = {
          from: params.from,
          to: params.to,
          amount: params.amount,
          fromChain: params.fromChain,
          toChain: params.toChain,
          fromTxId: txId,
          fromTxStatus: 'pending',
          status: 'pending',
          createdTime: new Date().getTime(),
          token: params.token,
        };

        setData(data);
        setState('success');
        options.onSuccess?.(data);
      } catch (e: unknown) {
        let error: Error;
        if (e instanceof UserRejectedError) {
          return;
        } else if (e instanceof Error) {
          error = e;
        } else {
          error = new Error('An unknown error occurred. Please try again later.');
        }
        setState('error');
        setError(error);
        options.onError?.(error);
      } finally {
        setState('idle');
        options.onStale?.();
      }
    },
    [config, ensureOnChain, setState, setError, notify],
  );

  const reset = useCallback(() => {
    setState('idle');
    setData(null);
    setError(null);
  }, [setState, setError]);

  return {
    data,
    transfer,
    state,
    error,
    reset,
  };
};
