import { TransferProps, TransferContext, transfer<PERSON>int<PERSON>tom, TransferContextInit } from './types';
import { ReadableError, UserRejectedError } from './errors';
import {
  estimateFeesPerGas,
  estimateGas,
  getWalletClient,
  readContract,
  waitForTransactionReceipt,
  writeContract,
} from '@wagmi/core';
import { abi as bridge } from '@/wallets/abi/bridge';
import { abi as ccipPool } from '@/modules/bridge/abi/ccip-pool';
import { abi as zkLight } from '@/wallets/abi/zklight';
import {
  Address,
  BaseError,
  Hex,
  InsufficientFundsError,
  UserRejectedRequestError,
  encodeAbiParameters,
  encodeFunctionData,
  erc20Abi as erc20,
} from 'viem';
import { ERC20Token, NativeToken } from '@/wallets/config/type';
import { BigNumber } from 'ethers';
import * as CCIP from '@chainlink/ccip-js';
import { store } from '@/store';
import { createClient } from './ccip-native';
import { formatUnits } from '@/lib/utils';

export const transfer = async (
  { config }: TransferContext,
  { to, amount, fromChain, fee }: TransferProps,
) => {
  if (!config) {
    throw new Error('Config is required');
  }

  if (fromChain.contracts?.bridge === undefined) {
    throw new Error('Bridge contract not found');
  }

  try {
    const feesPerGas = await estimateFeesPerGas(config);
    const address = fromChain.contracts.bridge.address;

    const functionData = encodeFunctionData({
      abi: bridge,
      functionName: 'lock',
      args: [to],
    });
    const gas = await estimateGas(config, {
      data: functionData,
      to: address,
      value: amount.add(fee).toBigInt(),
      account: address,
    });

    const result = await writeContract(config, {
      abi: bridge,
      address,
      functionName: 'lock',
      args: [to],
      value: amount.add(fee).toBigInt(),
      chainId: fromChain.chain.id,
      gas,
      maxFeePerGas: feesPerGas.maxFeePerGas,
      maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
    });
    return result;
  } catch (e: unknown) {
    handleException(e);
  }
};

export const transferErc20ToBtc = async (
  { config }: TransferContext,
  { from, to, amount, fromChain, fee, token }: TransferProps<ERC20Token>,
) => {
  if (!config) {
    throw new Error('Config is required');
  }

  if (fromChain.contracts?.bridge === undefined) {
    throw new Error('Bridge contract not found');
  }

  try {
    const feesPerGas = await estimateFeesPerGas(config);
    const address = fromChain.contracts.bridge.address;
    const args = [token.contractAddress, amount.toBigInt(), to] as const;

    const gas = await estimateGas(config, {
      data: encodeFunctionData({
        abi: bridge,
        functionName: 'crossoutBurn',
        args,
      }),
      to: address,
      value: fee.toBigInt(),
      account: from as Address,
    });

    const result = await writeContract(config, {
      abi: bridge,
      address,
      functionName: 'crossoutBurn',
      args,
      value: fee.toBigInt(),
      chainId: fromChain.chain.id,
      gas,
      maxFeePerGas: feesPerGas.maxFeePerGas,
      maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
    });
    return result;
  } catch (e: unknown) {
    handleException(e);
  }
};

export const incrBy = (origin: bigint, upPercent: number) => {
  return BigNumber.from(origin)
    .mul(Math.floor(100 * (1 + upPercent)))
    .div(100)
    .toBigInt();
};

export const transferETHViaZkBridge = async (
  context: TransferContext,
  params: TransferProps<NativeToken>,
) => {
  if (!context.config) {
    throw new Error('Config is required');
  }

  const { from, to, amount, fromChain, toChain, token } = params;

  if (!fromChain.contracts?.zkBridgeLightning) {
    throw new Error('Invalid chain config');
  }

  try {
    const poolId = BigInt(token.zkbridge!.poolId);
    const chainAppId = toChain.zkbridge!.appId;
    const address = fromChain.contracts.zkBridgeLightning.address;
    const args = [chainAppId, amount.toBigInt(), to as Address] as const;

    const estimatedFee = await readContract(context.config, {
      abi: zkLight,
      address,
      functionName: 'estimateFee',
      args: [poolId, chainAppId, amount.toBigInt()],
    });

    const feesPerGas = await estimateFeesPerGas(context.config);
    const gas = await estimateGas(context.config, {
      data: encodeFunctionData({
        abi: zkLight,
        functionName: 'transferETH',
        args,
      }),
      to: address,
      value: amount.add(estimatedFee).toBigInt(),
      account: from as Address,
    });

    const result = await writeContract(context.config, {
      abi: zkLight,
      address,
      functionName: 'transferETH',
      args,
      value: amount.add(estimatedFee).toBigInt(),
      chainId: fromChain.chain.id,
      gas: incrBy(gas, 0.2),
      maxFeePerGas: incrBy(feesPerGas.maxFeePerGas, 0.5),
      maxPriorityFeePerGas: incrBy(feesPerGas.maxPriorityFeePerGas, 0.5),
    });
    return result;
  } catch (e: unknown) {
    handleException(e);
  }
};

export const transferErc20ViaZkBridge = async (
  context: TransferContext,
  params: TransferProps<ERC20Token>,
) => {
  if (!context.config) {
    throw new Error('Config is required');
  }

  const { from, to, amount, fromChain, toChain, token } = params;

  if (!fromChain.contracts?.zkBridgeLightning) {
    throw new Error('Invalid chain config');
  }

  try {
    const poolId = BigInt(token.zkbridge!.poolId);
    const chainAppId = toChain.zkbridge!.appId;
    const address = fromChain.contracts.zkBridgeLightning.address;
    const args = [chainAppId, poolId, amount.toBigInt(), to as Address] as const;

    const estimatedFee = await readContract(context.config, {
      abi: zkLight,
      address,
      functionName: 'estimateFee',
      args: [poolId, chainAppId, amount.toBigInt()],
    });

    const feesPerGas = await estimateFeesPerGas(context.config);
    const gas = await estimateGas(context.config, {
      data: encodeFunctionData({
        abi: zkLight,
        functionName: 'transferToken',
        args,
      }),
      to: address,
      value: estimatedFee,
      account: from as Address,
    });

    const result = await writeContract(context.config, {
      abi: zkLight,
      address,
      functionName: 'transferToken',
      args,
      value: estimatedFee,
      chainId: fromChain.chain.id,
      gas: incrBy(gas, 0.2),
      maxFeePerGas: incrBy(feesPerGas.maxFeePerGas, 0.5),
      maxPriorityFeePerGas: incrBy(feesPerGas.maxPriorityFeePerGas, 0.5),
    });
    return result;
  } catch (e: unknown) {
    handleException(e);
  }
};

export const ccipLimitCheck = async (
  context: TransferContext,
  params: TransferProps<NativeToken | ERC20Token>,
) => {
  const { config } = context;
  if (!config) {
    throw new Error('Config is required');
  }

  let { token } = params;
  const { amount, fromChain, toChain } = params;

  if (!fromChain.contracts?.ccip || !toChain.ccip) {
    throw new Error('Invalid chain config');
  }

  if (token.type === 'native' && token.wrappedTokens?.default) {
    token = token.wrappedTokens?.default;
  }

  if (!token.ccip?.pool) {
    console.warn('No pool configuration for token', (token as ERC20Token).contractAddress);
    return;
  }

  const state = await readContract(config, {
    abi: ccipPool,
    address: token.ccip.pool,
    functionName: 'getCurrentOutboundRateLimiterState',
    args: [BigInt(toChain.ccip.selector)],
  });

  if (!state.isEnabled) {
    return;
  }

  if (state.tokens < amount.toBigInt()) {
    throw new ReadableError('withdrawLimitReached', 'amountExceed', {
      limit: formatUnits(state.capacity, token.decimals, { keepDecimals: 6, trimZeros: true }),
      available: formatUnits(state.tokens, token.decimals, { keepDecimals: 6, trimZeros: true }),
      recovery: formatUnits(state.rate, token.decimals, { keepDecimals: 6, trimZeros: true }),
      token: token.symbol,
    });
  }
};

export const transferETHViaCCIP = async (
  context: TransferContext,
  params: TransferProps<NativeToken>,
) => {
  const { config } = context;
  if (!config) {
    throw new Error('Config is required');
  }

  const { from, to, amount, fromChain, toChain, token } = params;

  if (!fromChain.contracts?.ccipNative || !toChain.ccip) {
    throw new Error('Invalid chain config');
  }

  const feeToken = token.wrappedTokens?.default;
  if (!feeToken) {
    throw new Error('Fee token not found');
  }

  const walletClient = await getWalletClient(config, { account: from as Address });
  const client = createClient();

  try {
    const { txHash } = await client.transferTokens({
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      client: walletClient as any,
      routerAddress: fromChain.contracts.ccipNative.address,
      tokenAddress: feeToken.contractAddress,
      destinationAccount: to as Address,
      destinationChainSelector: toChain.ccip.selector,
      amount: amount.toBigInt(),
    });
    return txHash;
  } catch (e: unknown) {
    handleException(e);
  }
};

export const transferErc20ViaCCIP = async (
  context: TransferContext,
  params: TransferProps<ERC20Token>,
) => {
  const { config } = context;
  if (!config) {
    throw new Error('Config is required');
  }

  const { from, to, amount, fromChain, toChain, token } = params;

  if (!fromChain.contracts?.ccip || !toChain.ccip || !fromChain.ccip) {
    throw new Error('Invalid chain config');
  }

  const walletClient = await getWalletClient(config, { account: from as Address });
  const client = CCIP.createClient();

  try {
    let extraArgs: CCIP.EVMExtraArgsV2 | undefined = undefined;
    let data: Hex | undefined = undefined;
    let destinationAccount = to as Address;

    if (token.ccip?.receiver === 'router') {
      const receiver = toChain.contracts?.ccipNative.address;
      if (!receiver) {
        throw new Error('Router not found');
      }

      const nativeClient = createClient();

      const destWalletClient = await getWalletClient(config, {
        account: from as Address,
        chainId: toChain.chain.id,
      });
      const gas = await nativeClient.estimateReceiveGas({
        client: destWalletClient,
        routerAddress: fromChain.contracts.ccip.address,
        destinationAccount: receiver,
        destinationChainSelector: fromChain.ccip.selector,
        amount: amount.toBigInt(),
        tokenAddress: token.contractAddress,
      });

      const gasLimit = toChain.ccip.gasLimitOverride
        ? toChain.ccip.gasLimitOverride(Number(gas))
        : Number(gas);

      destinationAccount = receiver;
      data = encodeAbiParameters([{ type: 'address', name: 'data' }], [to as Address]);
      extraArgs = {
        gasLimit,
        allowOutOfOrderExecution: true,
      };
    }

    const { txHash } = await client.transferTokens({
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      client: walletClient as any,
      routerAddress: fromChain.contracts.ccip.address,
      tokenAddress: token.contractAddress,
      destinationAccount,
      destinationChainSelector: toChain.ccip.selector,
      amount: amount.toBigInt(),
      data,
      extraArgs,
    });
    return txHash;
  } catch (e: unknown) {
    handleException(e);
  }
};

export const approveErc20 = async (
  spender: Address,
  context: TransferContextInit,
  { from, amount, token }: Pick<TransferProps<ERC20Token>, 'from' | 'amount' | 'token'>,
) => {
  const { config } = context;
  if (!config) {
    throw new Error('Config is required');
  }

  const tokenContract = token.contractAddress;
  const abi = token.abi ? token.abi : erc20;

  try {
    store.set(transferHintAtom, 'checkingAllowance');

    const allowance = await readContract(config, {
      abi,
      address: tokenContract,
      functionName: 'allowance',
      args: [from as Address, spender],
    });

    // If allowance is enough, it doesn't need to approve
    if (allowance >= amount.toBigInt()) {
      return;
    }

    store.set(transferHintAtom, 'approveInProgress');

    if (token.continuouslyApprove === false && allowance > 0) {
      // some token doesn't support continuous approval, in such case,
      // we should request the user to reset the allownace first
      // if the allownace is not enough but greater than 0
      await approve(
        abi,
        {
          spender,
          address: tokenContract,
          account: from as Address,
          allowance: 0n,
        },
        context,
      );
    }

    const max = 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn;
    await approve(
      abi,
      {
        spender,
        address: tokenContract,
        account: from as Address,
        allowance: max,
      },
      context,
    );
  } catch (e: unknown) {
    handleException(e, 'Failed to approve');
  } finally {
    store.set(transferHintAtom, undefined);
  }
};

const approve = async (
  abi: typeof erc20,
  {
    address,
    account,
    spender,
    allowance,
  }: { address: Address; account: Address; spender: Address; allowance: bigint },
  { config }: TransferContextInit,
) => {
  const args = [spender, allowance] as const;
  const functionName = 'approve';

  const feesPerGas = await estimateFeesPerGas(config);
  const gas = await estimateGas(config, {
    data: encodeFunctionData({
      abi,
      functionName,
      args,
    }),
    to: address,
    account,
  });

  const result = await writeContract(config, {
    abi,
    address,
    functionName,
    args,
    gas: incrBy(gas, 0.2),
    maxFeePerGas: incrBy(feesPerGas.maxFeePerGas, 0.5),
    maxPriorityFeePerGas: incrBy(feesPerGas.maxPriorityFeePerGas, 0.5),
  });

  await waitForTransactionReceipt(config, {
    hash: result,
    pollingInterval: 5_000,
  });
};

export function handleException(e: unknown, message: string = 'Failed to transfer') {
  if (e instanceof BaseError) {
    const userRejected = e.walk(
      (error) => error instanceof UserRejectedRequestError,
    ) as UserRejectedRequestError | null;
    if (userRejected) {
      throw new UserRejectedError();
    }

    const insufficientFunds = e.walk(
      (error) => error instanceof InsufficientFundsError,
    ) as InsufficientFundsError | null;
    if (insufficientFunds) {
      console.warn('InsufficientFundsError:', insufficientFunds.details);
      throw new ReadableError('insufficientFunds');
    }
  }

  console.error(e);
  throw new Error(message);
}
