import { ERC20Token } from '@/wallets/config/type';
import { bridgeI18n<PERSON>ey, ValidatorContext, ValidatorParams, ValidatorResult } from './types';
import { Address, erc20Abi } from 'viem';
import { readContract } from '@wagmi/core';
import { utils } from 'ethers';
import { Trans } from 'react-i18next';

// ERC20 allowance validator
// This validator checks if the user has enough allowance to spend the token.
// It should be initialized with the validator context.
export const erc20AllowanceValidator = async (
  { config, account, spender }: ValidatorContext,
  params: ValidatorParams,
): ValidatorResult => {
  const token = params.token as ERC20Token;
  if (!spender || !token) {
    return [false, undefined];
  }

  const abi = token.abi ? token.abi : erc20Abi;
  const allowance = await readContract(config, {
    abi,
    address: token.contractAddress,
    functionName: 'allowance',
    args: [account as Address, spender],
  });

  const amount = utils.parseUnits(params.formBalance, token.decimals);
  if (allowance >= amount.toBigInt()) {
    return [true, undefined];
  }

  return [true, <Trans key={'approve'} i18nKey={`${bridgeI18nKey}.approve`} />];
};
