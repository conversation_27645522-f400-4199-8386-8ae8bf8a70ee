import { BigNumber } from 'ethers';
import { ReactNode } from 'react';
import { TransferContext } from '../types';
import { Address } from 'viem';
import { BaseChainType, Token } from '@/wallets/config/type';

export const bridgeI18nKey = 'pages.bridge';

export interface ValidatorParams {
  fromChain?: BaseChainType;
  account?: string;
  formBalance: string;
  balance?: BigNumber;
  token: Token;
  fee?: BigNumber;
  min?: BigNumber;
  max?: BigNumber;
  state: string;
  address?: string;
}

export interface ValidatorContext {
  config: TransferContext['config'];
  account: Address;
  spender?: Address;
}

export type ValidatorResult = Promise<[boolean, ReactNode | string]>;
export type Validator = (params: ValidatorParams) => ValidatorResult;
