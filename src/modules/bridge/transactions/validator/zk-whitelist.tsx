import { Address } from 'viem';
import { readContract } from '@wagmi/core';
import { abi as zkLight } from '@/wallets/abi/zklight';
import { maxValidator } from './common';
import { ValidatorContext, ValidatorParams, ValidatorResult } from './types';

// ZK whitelist validator
// This validator checks if the user is whitelisted on the zkBridgeLightning contract.
// If the user is whitelisted, the validator will pass.
// If the user is not whitelisted, the validator will run the maxValidator.
// It should be initialized with the validator context.
export const zkMaxWhitelistValidator = async (
  { config, account }: ValidatorContext,
  params: ValidatorParams,
): ValidatorResult => {
  const { fromChain } = params;
  if (!fromChain) {
    return [false, undefined];
  }

  const contract = fromChain.contracts?.zkBridgeLightning?.address;
  if (!contract) {
    return maxValidator(params);
  }

  const abi = zkLight;
  const pass = await readContract(config, {
    abi,
    address: contract,
    functionName: 'whitelists',
    args: [account as Address],
  });

  if (pass) {
    return [true, undefined];
  }

  return maxValidator(params);
};
