import { ERC20Token } from '@/wallets/config/type';
import { bridgeI18n<PERSON>ey, ValidatorParams, ValidatorResult } from './types';
import { Trans } from 'react-i18next';

export const maintenanceTokens: string[] = [];

export const maintenanceValidator = async (params: ValidatorParams): ValidatorResult => {
  const token = params.token as ERC20Token;
  if (maintenanceTokens.includes(token.id)) {
    return [
      false,
      <Trans key={'underMaintenance'} i18nKey={`${bridgeI18nKey}.underMaintenance`} />,
    ];
  }
  return [true, undefined];
};
