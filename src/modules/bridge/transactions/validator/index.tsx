import {
  addressValidator,
  balanceValidator,
  makeValidatorChain,
  maxValidator,
  minValidator,
  stateValidator,
  zeroAmountValidator,
} from './common';
import { erc20AllowanceValidator } from './erc20-allowance';
import { maintenanceValidator } from './maintenance';
import { Validator, ValidatorContext } from './types';
import partial from 'lodash/partial';

export type { ValidatorParams, ValidatorContext, ValidatorResult, Validator } from './types';

const defaultValidators = [
  stateValidator,
  addressValidator,
  zeroAmountValidator,
  balanceValidator,
  minValidator,
  maxValidator,
];

export const defaultValidator = makeValidatorChain(defaultValidators);

export const createERC20Validator = (context: ValidatorContext): Validator => {
  // const maxWhitelistValidator = partial(zkMaxWhitelistValidator, context);
  const allowanceValidator = partial(erc20AllowanceValidator, context);

  return makeValidatorChain([
    maintenanceValidator,
    stateValidator,
    addressValidator,
    zeroAmountValidator,
    balanceValidator,
    minValidator,
    // maxWhitelistValidator,
    allowanceValidator,
  ]);
};

export const createERC20ToBtcValidator = (context: ValidatorContext): Validator => {
  const allowanceValidator = partial(erc20AllowanceValidator, context);

  return makeValidatorChain([
    stateValidator,
    addressValidator,
    zeroAmountValidator,
    balanceValidator,
    // minValidator,
    // maxValidator,
    allowanceValidator,
  ]);
};

export const createETHValidator = (context: ValidatorContext): Validator => {
  // const maxWhitelistValidator = partial(zkMaxWhitelistValidator, context);

  return makeValidatorChain([
    maintenanceValidator,
    stateValidator,
    addressValidator,
    zeroAmountValidator,
    balanceValidator,
    minValidator,
    // maxWhitelistValidator,
  ]);
};

export const createBRC20Validator = (): Validator => {
  return makeValidatorChain([
    stateValidator,
    addressValidator,
    zeroAmountValidator,
    minValidator,
    maxValidator,
  ]);
};
