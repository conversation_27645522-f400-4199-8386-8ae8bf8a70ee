import * as Viem from 'viem';
import OnRampABI from '@chainlink/ccip-js/dist/abi/OnRamp.json';
import EtherSenderReceiverABI from '@/modules/bridge/abi/ether-sender-receiver';

import { readContract, writeContract, waitForTransactionReceipt, estimateGas } from 'viem/actions';

/** An object containing methods for cross-chain transfer management.
 *  @typedef {Object} Client */
export interface Client {
  /** Get the fee required for the cross-chain transfer and/or sending cross-chain message.
   * @param {Viem.Client} options.client - A client with access to public actions on the source blockchain.
   * @param {Viem.Address} options.routerAddress - The address of the router contract on the source blockchain.
   * @param {Viem.Address} options.destinationAccount - The address of the recipient account on the destination blockchain.
   * @param {string} options.destinationChainSelector - The selector for the destination chain.
   * @param {bigint} options.amount - The amount of tokens to transfer, specified as a `bigint`.
   * @param {Viem.Address} options.tokenAddress - The address of the token contract on the source blockchain.
   * @param {Viem.Address} options.feeTokenAddress - The address of the token used for paying fees. If not specified the chain's native token will be used.
   * @param {Viem.Hex} options.data - Arbitrary message to send, ABI encoded
   * @param {EVMExtraArgsV2} options.extraArgs - Pass extraArgs. Check [CCIP Docs](https://docs.chain.link/ccip/best-practices#using-extraargs) how to use it
   * @returns {Promise<bigint>} A promise that resolves to a number representing the fee required
   *                            for the transfer. The fee is typically expressed in the smallest unit
   *                            of the token or currency used.
   * @example
   * import { createPublicClient, http } from 'viem'
   * import { mainnet } from 'viem/chains'
   *
   * const publicClient = createPublicClient({
   *   chain: mainnet,
   *   transport: http()
   * })
   *
   * const fee = await client.getFee({
   *   client: publicClient,
   *   routerAddress: "0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef",
   *   destinationAccount: "******************************************",
   *   destinationChainSelector: "1234"
   *   amount: 1000000000000000000n,
   *   tokenAddress: "0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef",
   * });
   */
  getFee(options: {
    client: Viem.Client;
    routerAddress: Viem.Address;
    destinationAccount: Viem.Address;
    destinationChainSelector: string;
    amount?: bigint;
    tokenAddress?: Viem.Address;
    feeTokenAddress?: Viem.Address;
    data?: Viem.Hex;
    extraArgs?: EVMExtraArgsV2;
  }): Promise<bigint>;

  estimateReceiveGas(options: {
    client: Viem.Client;
    routerAddress: Viem.Address;
    destinationAccount: Viem.Address;
    destinationChainSelector: string;
    amount?: bigint;
    tokenAddress?: Viem.Address;
    feeTokenAddress?: Viem.Address;
    data?: Viem.Hex;
    extraArgs?: EVMExtraArgsV2;
  }): Promise<bigint>;

  /** Initiate the token transfer and returns the transaction hash and message ID.
   * @param {Viem.WalletClient} options.client - A client with access to wallet actions on the source blockchain.
   * @param {Viem.Address} options.routerAddress - The address of the router contract on the source blockchain.
   * @param {string} options.destinationChainSelector - The selector for the destination chain.
   * @param {bigint} options.amount - Amount to transfer.
   * @param {Viem.Address} options.destinationAccount - Address of recipient.
   * @param {Viem.Address} options.tokenAddress - Address of transferred token.
   * @param {Viem.Address} options.feeTokenAddress - The address of the token used for paying fees. If not specified the chain's native token will be used.
   * @param {Viem.Hex} options.data - Arbitrary data to send along with the transaction. ABI encoded
   * @param {EVMExtraArgsV2} options.extraArgs - Pass extraArgs. Check [CCIP Docs](https://docs.chain.link/ccip/best-practices#using-extraargs) how to use it
   * @param {Object} options.writeContractParameters
   *  - Override the **optional** write contract parameters for the 'approve' method.
   * @param {Object} options.waitForTransactionReceiptParameters
   *  - Override waitForTransactionReceipt parameters.
   * @returns {Promise<{ txHash: Viem.Hash, messageId: Viem.Hash, txReceipt?: Viem.TransactionReceipt }>} A promise
   *                                                        that resolves to an object containing the transaction
   *                                                        hash (`txHash`) and message ID (`messageId`) and transaction
   *                                                        receipt (`txReceipt`).
   *                                                        These details are used to track and confirm the transfer.
   * @example
   *  import { createWalletClient, custom, encodeAbiParameters } from 'viem'
   *  import { mainnet } from 'viem/chains'
   *
   *  const walletClient = createWalletClient({
   *    chain: mainnet,
   *    transport: custom(window.ethereum!)
   *  })
   *
   * const { txHash, messageId } = await client.transferTokens({
   *   client: walletClient,
   *   routerAddress: "0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef",
   *   destinationChainSelector: "1234"
   *   amount: 1000000000000000000n,
   *   destinationAccount: "******************************************",
   *   tokenAddress: "0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef",
   *   data: encodeAbiParameters([{ type: 'string', name: 'data' }], ["Hello"])
   * });
   *
   */
  transferTokens(options: {
    client: Viem.WalletClient;
    routerAddress: Viem.Address;
    destinationChainSelector: string;
    amount: bigint;
    destinationAccount: Viem.Address;
    tokenAddress: Viem.Address;
    feeTokenAddress?: Viem.Address;
    data?: Viem.Hex;
    extraArgs?: EVMExtraArgsV2;
    writeContractParameters?: Partial<{
      gas: bigint;
      gasPrice: bigint;
      nonce: number;
    }>;
    waitForTransactionReceiptParameters?: Partial<{
      confirmations: number;
      pollingInterval: number;
    }>;
  }): Promise<{
    txHash: Viem.Hash;
    messageId: Viem.Hash;
    txReceipt: Viem.TransactionReceipt;
  }>;
}

/**
 * Creates a client for managing cross-chain transfers, configured for either testnet or mainnet environments.
 *
 * The `createClient` function initializes and returns a client object tailored for interacting with cross-chain
 * transfer functionalities. The function also allows for custom CCIP routers to be provided, with
 * sensible defaults based on the environment.
 *
 * @function createClient
 * @returns {Client} The client object with methods for cross-chain transfer management.
 *
 * @example
 * // Example usage of createClient function
 * import * as CCIP from '@chainlink/ccip-js';
 * import { createWalletClient, custom } from 'viem'
 * import { mainnet } from 'viem/chains'
 *
 * const ccipClient = CCIP.createClient();
 *
 * const publicClient = createPublicClient({
 *  chain: mainnet,
 *  transport: http()
 * })
 *
 * const walletClient = createWalletClient({
 *  chain: mainnet,
 *  transport: custom(window.ethereum!)
 * })
 *
 * const { txHash, txReceipt } = await ccipClient.approve({
 *  client: walletClient,
 *  routerAddress: "0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef",
 *  tokenAddress: "0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef",
 *  amount: 1000000000000000000n,
 *  waitForReceipt: true,
 * });
 *
 * console.log(`Transfer approved. Transaction hash: ${txHash}. Transaction receipt: ${txReceipt}`);
 *
 * const fee = await ccipClient.getFee({
 *  client: publicClient,
 *  routerAddress: "0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef",
 *  tokenAddress: "0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef",
 *  amount: 1000000000000000000n,
 *  destinationAccount: "******************************************",
 *  destinationChainSelector: "1234"
 * });
 *
 * console.log(`Fee: ${fee.toLocaleString()}`);
 *
 * const { txHash, messageId } = await client.transferTokens({
 *  client: walletClient,
 *  routerAddress: "0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef",
 *  tokenAddress: "0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef",
 *  amount: 1000000000000000000n,
 *  destinationAccount: "******************************************",
 *  destinationChainSelector: "1234"
 * });
 *
 * console.log(`Transfer success. Transaction hash: ${txHash}. Message ID: ${messageId}`)
 */
export const createClient = (): Client => {
  return {
    getFee,
    transferTokens,
    estimateReceiveGas,
  };

  async function getFee(options: Parameters<Client['getFee']>[0]) {
    checkIsAddressValid(
      options.routerAddress,
      `PARAMETER INPUT ERROR: Router address ${options.routerAddress} is not valid`,
    );

    if (options.amount && options.amount < BigInt(0)) {
      throw new Error('PARAMETER INPUT ERROR: Invalid amount. Amount can not be negative');
    }

    if (!Viem.isAddress(options.destinationAccount)) {
      throw new Error(
        `PARAMETER INPUT ERROR: ${options.destinationAccount} is not a valid destionation account address`,
      );
    }

    if (options.tokenAddress) {
      checkIsAddressValid(
        options.tokenAddress,
        `PARAMETER INPUT ERROR: Token address ${options.tokenAddress} is not valid`,
      );
    }

    if (options.feeTokenAddress) {
      if (!Viem.isAddress(options.feeTokenAddress)) {
        throw new Error(
          `PARAMETER INPUT ERROR: ${options.feeTokenAddress} is not a valid fee token address`,
        );
      }
    }

    return (await readContract(options.client, {
      abi: EtherSenderReceiverABI,
      address: options.routerAddress,
      functionName: 'getFee',
      args: buildArgs(options),
    })) as bigint;
  }

  async function estimateReceiveGas(options: Parameters<Client['estimateReceiveGas']>[0]) {
    checkIsAddressValid(
      options.routerAddress,
      `PARAMETER INPUT ERROR: Router address ${options.routerAddress} is not valid`,
    );

    if (options.amount && options.amount < BigInt(0)) {
      throw new Error('PARAMETER INPUT ERROR: Invalid amount. Amount can not be negative');
    }

    if (!Viem.isAddress(options.destinationAccount)) {
      throw new Error(
        `PARAMETER INPUT ERROR: ${options.destinationAccount} is not a valid destionation account address`,
      );
    }

    if (options.tokenAddress) {
      checkIsAddressValid(
        options.tokenAddress,
        `PARAMETER INPUT ERROR: Token address ${options.tokenAddress} is not valid`,
      );
    }

    if (options.feeTokenAddress) {
      if (!Viem.isAddress(options.feeTokenAddress)) {
        throw new Error(
          `PARAMETER INPUT ERROR: ${options.feeTokenAddress} is not a valid fee token address`,
        );
      }
    }

    return await estimateGas(options.client, {
      data: Viem.encodeFunctionData({
        abi: EtherSenderReceiverABI,
        functionName: 'ccipReceive',
        args: [
          {
            messageId: '0x0000000000000000000000000000000000000000000000000000000000000000',
            sourceChainSelector: BigInt(options.destinationChainSelector),
            sender: Viem.encodeAbiParameters(
              [{ type: 'address', name: 'sender' }],
              [options.client.account!.address],
            ),
            data: options.data ?? Viem.zeroHash,
            destTokenAmounts: [
              {
                token: options.tokenAddress || Viem.zeroAddress,
                amount: options.amount || BigInt(0),
              },
            ],
          },
        ],
      }),
      to: options.destinationAccount,
      account: options.client.account!.address,
    });
  }

  async function transferTokens(options: Parameters<Client['transferTokens']>[0]) {
    checkIsWalletAccountValid(options);

    if (!options.amount || options.amount <= BigInt(0)) {
      throw new Error('PARAMETER INPUT ERROR: Invalid amount. Amount must be greater than 0');
    }

    if (!Viem.isAddress(options.destinationAccount)) {
      throw new Error(
        `PARAMETER INPUT ERROR: ${options.destinationAccount} is not a valid destionation account address`,
      );
    }

    if (options.feeTokenAddress) {
      if (!Viem.isAddress(options.feeTokenAddress)) {
        throw new Error(
          `PARAMETER INPUT ERROR: ${options.feeTokenAddress} is not a valid fee token address`,
        );
      }
    }

    const writeContractParameters = {
      chain: options.client.chain,
      abi: EtherSenderReceiverABI,
      address: options.routerAddress,
      functionName: 'ccipSend',
      args: buildArgs(options),
      account: options.client.account!.address,
      ...(!options.feeTokenAddress && {
        value: options.amount + (await getFee(options)),
      }),
      ...options.writeContractParameters,
    } as const;

    const transferTokensTxHash = await writeContract(options.client, writeContractParameters);

    const txReceipt = await waitForTransactionReceipt(options.client, {
      hash: transferTokensTxHash,
      confirmations: 2,
      ...options.waitForTransactionReceiptParameters,
    });

    const parsedLog = Viem.parseEventLogs({
      abi: OnRampABI,
      logs: txReceipt.logs,
      eventName: 'CCIPSendRequested',
    }) as CCIPTrasnferReceipt[];

    const messageId = parsedLog[0]?.args?.message?.messageId;
    if (!messageId) {
      throw new Error('EVENTS LOG ERROR: Message ID not found in the transaction logs');
    }

    return {
      txHash: transferTokensTxHash,
      messageId: messageId,
      txReceipt: txReceipt as Viem.TransactionReceipt,
    };
  }

  function buildArgs(options: {
    amount?: bigint;
    destinationChainSelector: string;
    destinationAccount: Viem.Address;
    tokenAddress?: Viem.Address;
    feeTokenAddress?: Viem.Address;
    data?: Viem.Hex;
    extraArgs?: EVMExtraArgsV2;
  }) {
    const {
      destinationAccount,
      destinationChainSelector,
      tokenAddress,
      amount,
      feeTokenAddress,
      data,
      extraArgs: evmExtraArgsV2,
    } = options;
    const gasLimit = BigInt(evmExtraArgsV2?.gasLimit ?? 0);
    // Controls the execution order of your messages on the destination blockchain.
    // Setting this to true allows messages to be executed in any order. Setting it to false
    // ensures messages are executed in sequence, so a message will only be executed if the
    // preceeding one has been executed. On lanes where `Out of Order Execution` is required,
    // you must set this to true; otherwise, the transaction will revert.
    const allowOutOfOrderExecution =
      evmExtraArgsV2?.allowOutOfOrderExecution === false ? false : true;
    const extraArgsEncoded = Viem.encodeAbiParameters(
      [
        { type: 'uint256', name: 'gasLimit' },
        { type: 'bool', name: 'allowOutOfOrderExecution' },
      ],
      [gasLimit, allowOutOfOrderExecution],
    );
    const evmExtraArgsV2Tag = '0x181dcf10';
    const extraArgs = evmExtraArgsV2Tag + extraArgsEncoded.slice(2);
    return [
      BigInt(destinationChainSelector),
      {
        receiver: Viem.encodeAbiParameters(
          [{ type: 'address', name: 'receiver' }],
          [destinationAccount],
        ),
        data: data ?? Viem.zeroHash,
        tokenAmounts: amount && tokenAddress ? [{ token: tokenAddress, amount }] : [],
        feeToken: feeTokenAddress || Viem.zeroAddress,
        extraArgs: extraArgs as Viem.Hash,
      },
    ] as const;
  }

  function checkIsAddressValid(address: Viem.Address, errorMessage?: string) {
    if (!Viem.isAddress(address) || Viem.isAddressEqual(address, Viem.zeroAddress)) {
      throw new Error(errorMessage);
    }
  }

  function checkIsWalletAccountValid(options: { client: Viem.Client }) {
    if (!options.client.account) {
      throw new Error('WALLET ERROR: account is not valid');
    }

    checkIsAddressValid(
      options.client.account.address,
      'WALLET ERROR: account address is not valid',
    );
  }
};

/**
 * Represents the state of a rate limiter using a token bucket algorithm.
 *
 * @typedef {Object} RateLimiterState
 * @prop {bigint} tokens - Current number of tokens that are in the bucket.
 *                             This represents the available capacity for requests.
 * @prop {number} lastUpdated - Timestamp in seconds of the last token refill,
 *                                  allows tracking token consumption over time.
 *                                  This is designed to be accurate for over 100 years.
 * @prop {boolean} isEnabled - Indicates whether the rate limiting feature is enabled or disabled.
 * @prop {bigint} capacity - Maximum number of tokens that can be in the bucket,
 *                               representing the total capacity of the limiter.
 * @prop {bigint} rate - The rate at which tokens are refilled in the bucket,
 *                           typically measured in tokens per second.
 */
export interface RateLimiterState {
  tokens: bigint;
  lastUpdated: number;
  isEnabled: boolean;
  capacity: bigint;
  rate: bigint;
}

/**
 * Configuration settings for dynamic aspects of cross-chain transfers.
 *
 * The `DynamicConfig` type defines the structure of an object that holds various
 * dynamic configuration parameters for cross-chain transactions. These settings
 * are used to control the behavior and limits of transfers, such as gas calculations,
 * data availability, and message size constraints.
 *
 * @typedef {Object} DynamicConfig
 * @property {Viem.Address} router - The address of the router responsible for handling
 *                                   the cross-chain transfers. This address is used to
 *                                   route the transaction through the correct path.
 * @property {number} maxNumberOfTokensPerMsg - The maximum number of tokens that can be
 *                                              included in a single message. This parameter
 *                                              limits the token batch size in cross-chain
 *                                              transfers to prevent overly large transactions.
 * @property {number} destGasOverhead - The overhead in gas that is added to the destination
 *                                      chain to account for base transaction costs. This value
 *                                      helps ensure that the transaction has enough gas to
 *                                      cover additional overhead on the destination chain.
 * @property {number} destGasPerPayloadByte - The amount of gas required per byte of payload
 *                                            on the destination chain. This parameter is used
 *                                            to calculate the total gas needed based on the
 *                                            size of the payload being transferred.
 * @property {number} destDataAvailabilityOverheadGas - The additional gas required on the
 *                                                      destination chain to account for data
 *                                                      availability overhead. This value is used
 *                                                      to ensure that enough gas is allocated
 *                                                      for the availability of the data being
 *                                                      transferred.
 * @property {number} destGasPerDataAvailabilityByte - The gas cost per byte of data availability
 *                                                     on the destination chain. This parameter
 *                                                     contributes to the overall gas calculation
 *                                                     for data availability during the transfer.
 * @property {number} destDataAvailabilityMultiplierBps - The multiplier in basis points (bps)
 *                                                        applied to the data availability gas
 *                                                        cost. This value is used to adjust the
 *                                                        cost of data availability by applying
 *                                                        a scaling factor.
 * @property {Viem.Address} priceRegistry - The address of the price registry used to obtain
 *                                          pricing information for gas and other costs during
 *                                          the transfer. This registry helps ensure that the
 *                                          correct prices are applied to the transaction.
 * @property {number} maxDataBytes - The maximum number of data bytes that can be included in
 *                                   a single message. This parameter limits the size of the
 *                                   data payload to prevent excessive data in one transfer.
 * @property {number} maxPerMsgGasLimit - The maximum gas limit that can be applied to a single
 *                                        message. This parameter ensures that the transaction
 *                                        does not exceed a certain gas threshold, preventing
 *                                        overly costly operations.
 */
export type DynamicConfig = {
  router: Viem.Address;
  maxNumberOfTokensPerMsg: number;
  destGasOverhead: number;
  destGasPerPayloadByte: number;
  destDataAvailabilityOverheadGas: number;
  destGasPerDataAvailabilityByte: number;
  destDataAvailabilityMultiplierBps: number;
  priceRegistry: Viem.Address;
  maxDataBytes: number;
  maxPerMsgGasLimit: number;
  defaultTokenFeeUSDCents: number;
  defaultTokenDestGasOverhead: number;
  enforceOutOfOrder: boolean;
};

/**
 * Configuration settings for static aspects of cross-chain transfers.
 *
 * The `StaticConfig` type defines the structure of an object that holds various
 * static configuration parameters for cross-chain transactions.
 *
 * @typedef {Object} StaticConfig
 *
 * @property {bigint} chainSelector - The selector for the source chain.
 * @property {bigint} defaultTxGasLimit - Default gas limit per transaction.
 * @property {bigint} destChainSelector - The selector for the destination chain.
 * @property {Viem.Address} linkToken - The address of the LINK token on the source chain.
 * @property {bigint} maxNopFeesJuels - Maxium nop fee in juels.
 * @property {bigint} maxNopFeesJuels - Maxium nop fee in juels.
 * @property {Viem.Address} prevOnRamp - Previous onRamp contract address.
 * @property {Viem.Address} rmnProxy - RMN proxy contract address.
 * @property {Viem.Address} tokenAdminRegistryAddress - The address of the token admin registry contract.
 */
export type StaticConfig = {
  chainSelector: bigint;
  defaultTxGasLimit: bigint;
  destChainSelector: bigint;
  linkToken: Viem.Address;
  maxNopFeesJuels: bigint;
  prevOnRamp: Viem.Address;
  rmnProxy: Viem.Address;
  tokenAdminRegistry: Viem.Address;
};

/**
 * Represents the off-ramp configuration for a cross-chain transfer.
 *
 * @typedef {Object} OffRamp
 * @property {Viem.Address} offRamp - The address of the off-ramp contract on the destination blockchain.
 * @property {bigint} sourceChainSelector - The selector for the source chain.
 */
export type OffRamp = {
  offRamp: Viem.Address;
  sourceChainSelector: bigint;
};

/**
 * Represents the transaction status of a cross-chain transfer.
 */
export enum TransferStatus {
  Untouched = 0,
  InProgress = 1,
  Success = 2,
  Failure = 3,
}

/**
 * Extends the Viem.Log type to fetch cross-chain trasnfer messageId.
 */
export type CCIPTrasnferReceipt = Viem.Log & {
  args: {
    message?: {
      messageId?: Viem.Hash;
    };
  };
};

export type EVMExtraArgsV2 = {
  gasLimit?: number;
  allowOutOfOrderExecution?: boolean;
};
