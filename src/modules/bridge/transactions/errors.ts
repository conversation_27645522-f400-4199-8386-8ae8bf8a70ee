export class UserRejectedError extends Error {
  constructor() {
    super('User rejected');
    this.name = 'UserRejectedError';
  }
}

export class ReadableError extends Error {
  public title: string;
  public data: Record<string, unknown>;

  constructor(message: string, title: string = 'transferFailed', data?: Record<string, unknown>) {
    message = message || 'internalError';

    super(message);
    this.name = 'ReadableError';
    this.title = title;
    this.data = data || {};
  }
}
