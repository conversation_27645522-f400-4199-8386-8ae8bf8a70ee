import { TransferContext, TransferContextInit, TransferProps } from './types';
import {
  buildBtcTransfer,
  buildBrc20Transfer,
  buildRuneTransfer,
  signAndPushPsbt,
  requestConfirmCaution,
} from './btc.client';
import {
  transfer as transferFromEvmToBtc,
  transferETHViaCCIP,
  approveErc20,
  transferErc20ToBtc,
  transferErc20ViaCCIP,
  ccipLimitCheck,
} from './evm.client';
import { NetworkType, Token } from '@/wallets/config/type';
import {
  Validator,
  createBRC20Validator,
  createERC20ToBtcValidator,
  createERC20Validator,
  createETHValidator,
  defaultValidator,
} from './validator';
import partial from 'lodash/partial';
import { Address } from 'viem';
import { ResolvedRegister } from '@wagmi/core';

export type { TransferProps } from './types';

export type StepFunction<TokenType extends Token = Token> = (
  context: TransferContext,
  params: TransferProps<TokenType>,
) => Promise<string | undefined>;

export class Workflow<TokenType extends Token = Token> {
  protected steps: Array<StepFunction<TokenType>> = [];
  #validator: Validator;

  protected currentStep = 0;

  protected context: TransferContext;

  protected result: string | undefined;

  constructor(
    context: TransferContext,
    steps: StepFunction<TokenType>[],
    validator: Validator | null,
  ) {
    this.steps = steps;
    this.context = context;
    this.#validator = validator ?? defaultValidator;
  }

  async next(params: TransferProps<TokenType>) {
    if (this.currentStep >= this.steps.length) {
      return this.result;
    }
    const step = this.steps[this.currentStep];
    this.result = await step(this.context, params);
    this.currentStep++;
    await this.next(params);
  }

  get txId() {
    return this.result;
  }

  get validator() {
    return this.#validator;
  }
}

const buildBtcSteps = <TokenType extends Token = Token>(
  token: TokenType,
): [StepFunction<TokenType>[], Validator | null] => {
  let steps: StepFunction<TokenType>[] = [];
  let validator: Validator | null = null;
  switch (token.type) {
    case 'rune':
      steps = [
        buildRuneTransfer,
        requestConfirmCaution,
        signAndPushPsbt,
      ] as StepFunction<TokenType>[];
      break;
    case 'brc20':
      steps = [
        buildBrc20Transfer,
        requestConfirmCaution,
        signAndPushPsbt,
      ] as StepFunction<TokenType>[];
      validator = createBRC20Validator();
      break;
    default:
      steps = [buildBtcTransfer, requestConfirmCaution, signAndPushPsbt];
  }
  return [steps, validator];
};

const buildEvmSteps = <TokenType extends Token = Token>(
  context: TransferContext,
  {
    token,
    fromChain,
    toChain,
    from,
  }: Pick<TransferProps<TokenType>, 'from' | 'fromChain' | 'toChain' | 'token'>,
): [StepFunction<TokenType>[], Validator | null] => {
  if (token.id === 'BTC') {
    return [[transferFromEvmToBtc], null];
  }

  const validatorCtx = { config: context.config, account: from as Address };

  if (token.type === 'erc20') {
    if (toChain.networkType === NetworkType.evm) {
      // const spender = fromChain.contracts!.zkBridgeLightning.address as Address;
      const spender = fromChain.contracts!.ccip.address as Address;
      const validator = createERC20Validator({ ...validatorCtx, spender });
      return [
        [
          partial(approveErc20, spender),
          ccipLimitCheck,
          transferErc20ViaCCIP,
        ] as StepFunction<TokenType>[],
        validator,
      ];
    } else if (toChain.networkType === NetworkType.btc) {
      const spender = fromChain.contracts!.bridge.address as Address;
      const validator = createERC20ToBtcValidator({ ...validatorCtx, spender });
      return [
        [partial(approveErc20, spender), transferErc20ToBtc] as StepFunction<TokenType>[],
        validator,
      ];
    }
  } else if (token.id === 'ETH') {
    const validator = createETHValidator(validatorCtx);
    return [[ccipLimitCheck, transferETHViaCCIP] as StepFunction<TokenType>[], validator];
  }

  return [[], null];
};

export const buildWorkflow = <TokenType extends Token = Token>(
  params: Pick<TransferProps<TokenType>, 'from' | 'fromChain' | 'toChain' | 'token'>,
  contextInit: TransferContextInit,
): Workflow<TokenType> => {
  const { fromChain, token } = params;
  let steps: StepFunction<TokenType>[] = [];
  let validator: Validator | null = null;

  const context: TransferContext = new Context(contextInit);

  if (fromChain.networkType === NetworkType.btc) {
    [steps, validator] = buildBtcSteps(token);
  } else {
    [steps, validator] = buildEvmSteps(context, params);
  }

  return new Workflow(context, steps, validator);
};

class Context {
  config: ResolvedRegister['config'];

  protected store: Record<string, unknown> = {};

  constructor({ config }: TransferContextInit) {
    this.config = config;
  }

  set<T>(key: string, value: T) {
    this.store[key] = value;
  }

  get<T>(key: string): T | undefined {
    return this.store[key] as T;
  }
}
