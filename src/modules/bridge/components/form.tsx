import * as zod from 'zod';
import React from 'react';
import { ChevronDown } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRemixForm } from 'remix-hook-form';
import { Button } from '@/components/ui/button';
import { FormField } from '@/components/ui/form';
import { BalanceData } from '@/hooks/wallet/balance';
import { TransferState } from '@/modules/bridge/hooks/transfer';
import { BaseChainType, NetworkType, Token } from '@/wallets/config/type';
import { BigNumber, utils } from 'ethers';
import { TokenIcon } from '@/components/ui/token-icon';
import { ControllerProps, FieldPath, FieldValues } from 'react-hook-form';
import { cn, formatBalance, formatUnits } from '@/lib/utils';
import {
  AmountSectionProps,
  CommonAmountSection,
  MobileCommonFromSection,
  MobileFromSectionProps,
} from './amount-common';
import { Brc20AmountSection, MobileBrc20FromSection } from './amount-brc20';
import { GroupToken, useQuickTokens } from '../hooks/chains';
import { getAccount } from '@/hooks/wallet/account';
import { openSwitchDialog } from './common';
import { useDialog } from '@/hooks/dialog';
import {
  Dialog,
  DialogClose,
  DialogCloseRounded,
  DialogContent,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Title } from '@/components/ui/title';
import { useMediaQuery } from '@react-hook/media-query';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

export const schema = zod.object({
  left: zod.string().min(1),
  right: zod.string().min(1),
  amount: zod.coerce.string(),
  token: zod.string().min(1),
  address: zod.string().optional(),
  payload: zod.string().optional(),
});

// FormData is the type of the form data.
// It is used by TypeScript to infer the type of the form data.
export type FormData = zod.infer<typeof schema>;
export const resolver = zodResolver(schema);
export type TransferFormData = ReturnType<typeof useRemixForm<FormData>>;

export interface TransferFormProps {
  form: TransferFormData;
  leftOptions: BaseChainType[];
  rightOptions: BaseChainType[];
  fromChain?: BaseChainType;
  toChain?: BaseChainType;
  tokens: Token[];
  state: TransferState;
  balance?: BalanceData;
  fee?: BigNumber;
  max?: BigNumber;
  min?: BigNumber;
  onSubmit: (e: React.FormEvent) => void;
  onSwap: () => boolean | undefined;
}

export const formatTotalAmount = (
  formBalance: string,
  fee: BigNumber | undefined,
  token: Token,
) => {
  if (!fee) {
    return '--';
  }

  try {
    const userAmount = utils.parseUnits(formBalance, token.decimals);
    const totalAmount = token.type === 'native' ? userAmount.add(fee) : userAmount;
    const formattedAmount = formatBalance(utils.formatUnits(totalAmount, token.decimals));
    return `${formattedAmount} ${token.symbol}`;
  } catch (e) {
    return '--';
  }
};

export const FeeText = ({ fee, chain }: { fee?: BigNumber; chain?: BaseChainType }) => {
  if (!fee || !chain) {
    return '--';
  }

  const value = formatUnits(fee.toBigInt(), chain.chain.nativeCurrency.decimals, {
    keepDecimals: 6,
    trimZeros: true,
  });
  const symbol = chain.chain.nativeCurrency.symbol;
  return `${value} ${symbol}`;
};

const TokenBadge = ({ token, active }: { token: Token; active?: boolean }) => {
  const type = token.originalType || token.type;
  const label = type === 'native' ? token.name : type;
  return (
    <div
      className={cn(
        'bl-text-primary bl-bg-primary/20 bl-text-sm/[18px] bl-px-1.5 bl-uppercase group-data-[highlighted]:bl-text-white',
        {
          'bl-text-black': active,
        },
      )}
    >
      {label}
    </div>
  );
};
const SelectTokenContent = ({ token, active }: { token: GroupToken; active?: boolean }) => {
  return (
    <div
      className={cn(
        'bl-flex bl-items-center bl-justify-between bl-w-full bl-gap-2 bl-px-3',
        token.disabled ? 'bl-opacity-40' : '',
      )}
    >
      <div className="bl-flex bl-items-center bl-text-sm bl-text-white">
        <TokenIcon token={token} className="bl-size-5 bl-mr-2" />
        <div className="bl-text-wrap bl-break-all">{token.name}</div>
      </div>
      <TokenBadge token={token} active={active} />
    </div>
  );
};

const SelectTokenItem = ({
  token,
  active,
  onSelect,
}: {
  token: GroupToken;
  active?: boolean;
  onSelect: (id: string) => void;
}) => {
  const isDesktop = useMediaQuery('(min-width: 640px)');
  if (token.disabled) {
    return (
      <div
        key={token.id}
        className={cn(
          'bl-h-[38px] md:bl-h-[46px] bl-border bl-border-primary bl-text-base/5 bl-flex bl-gap-2 bl-items-center bl-col-span-2',
          'bl-min-w-[calc(50%-4px)] md:bl-min-w-[calc(33.3%-5.3px)] bl-cursor-not-allowed',
          {
            'bl-bg-primary': active,
            'hover:bl-bg-primary/30': !active,
          },
        )}
      >
        {isDesktop ? (
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger className="bl-cursor-not-allowed">
                <SelectTokenContent token={token} active={active} />
              </TooltipTrigger>
              <TooltipContent sideOffset={15} avoidCollisions className="bl-max-w-[220px]">
                <div
                  dangerouslySetInnerHTML={{ __html: "The wallet doesn't support this token" }}
                ></div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          <Popover>
            <PopoverTrigger className="bl-cursor-not-allowed">
              <SelectTokenContent token={token} active={active} />
            </PopoverTrigger>
            <PopoverContent
              side="top"
              sideOffset={10}
              avoidCollisions
              className="bl-max-w-[220px] bl-z-50"
            >
              <div
                dangerouslySetInnerHTML={{ __html: "The wallet doesn't support this token" }}
              ></div>
            </PopoverContent>
          </Popover>
        )}
      </div>
    );
  }

  return (
    <DialogClose
      key={token.id}
      className={cn(
        'bl-px-3 bl-py-2 md:bl-py-3 bl-border bl-border-primary bl-text-base/5 bl-flex bl-gap-2 bl-items-center bl-col-span-2',
        'bl-min-w-[calc(50%-4px)] md:bl-min-w-[calc(33.3%-5.3px)]',
        {
          'bl-bg-primary': active,
          'hover:bl-bg-primary/30': !active,
        },
      )}
      onClick={() => onSelect?.(token.id)}
    >
      <SelectTokenContent token={token} active={active} />
    </DialogClose>
  );
};

export const SelectTokenField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  form,
  current,
  fromChains,
  toChains,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, 'render'> & {
  form: TransferFormData;
  current?: Token;
  fromChains: BaseChainType[];
  toChains: BaseChainType[];
}) => {
  const left = form.watch('left');
  const right = form.watch('right');
  const tokenGroups = useQuickTokens(fromChains, toChains, { left, right });
  const { open: openDialog } = useDialog();

  const handleSelect = (chain: BaseChainType, token: Token, field: 'left' | 'right') => {
    form.setValue(field, chain.id);
    form.setValue('token', token.id);
    const account = getAccount({ network: chain.networkType });
    if (!account.address) {
      openSwitchDialog(chain, openDialog);
    }
  };

  return (
    <FormField
      {...props}
      render={({ field }) => (
        <Dialog>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              type="button"
              className="bl-w-60 md:bl-min-w-52 md:bl-w-52 md:bl-h-10 bl-text-sm/none md:bl-text-base/none md:bl-px-4 bl-text-white bl-border-input"
              outlineClassName="bl-border-input"
            >
              <div className="bl-flex bl-gap-2 bl-items-center bl-justify-between bl-w-full">
                <div className="bl-flex bl-gap-2 bl-items-center">
                  <TokenIcon token={current} className="bl-size-5" />
                  <div className="bl-max-w-12 md:bl-max-w-28 bl-text-ellipsis bl-overflow-hidden">
                    {current?.name}
                  </div>
                </div>
                <ChevronDown className="bl-size-5 bl-min-w-0 bl-shrink-0 bl-text-primary bl-duration-200 group-data-[state=open]:bl-rotate-180" />
              </div>
            </Button>
          </DialogTrigger>
          <DialogContent className="bl-w-screen bl-h-dvh bl-overflow-auto bl-bg-black/40 bl-p-6 bl-top-0 bl-translate-y-0 md:-bl-translate-y-1/2 md:bl-top-1/2 md:bl-h-auto md:bl-bg-black/70 md:bl-px-8 md:bl-max-w-xl md:bl-w-full md:bl-border md:bl-border-primary">
            <div className="bl-mt-10 bl-space-y-6 md:bl-mt-0">
              <Title className="bl-text-[22px] bl-text-white bl-text-center bl-uppercase md:bl-text-[22px]">
                Select A Token
              </Title>
              {tokenGroups.map((group) => (
                <div key={group.chain.id} className="bl-mb-2 last:bl-mb-0">
                  <div className="bl-py-1 bl-border-b bl-border-primary md:bl-text-white md:bl-text-base">
                    {group.chain.name}
                  </div>
                  <div className="bl-flex bl-w-full bl-flex-wrap bl-gap-2 bl-mt-6 md:bl-mt-4">
                    {group.tokens.map((token) => (
                      <SelectTokenItem
                        key={token.id}
                        token={token}
                        active={token.id === field.value}
                        onSelect={() => handleSelect(group.chain, token, group.field)}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
            <div className="bl-w-full bl-flex bl-justify-center">
              <DialogCloseRounded
                className="bl-hidden md:bl-flex hover:bl-text-white"
                buttonProps={{ overlayFrom: 'none' }}
              />
              <DialogCloseRounded
                className="bl-absolute bl-top-4 bl-right-6 bl-text-secondary bl-bg-black bl-border bl-border-secondary hover:bl-text-white md:bl-hidden"
                buttonProps={{ overlayFrom: 'none', variant: 'secondary' }}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    />
  );
};

const isUsingBrc20 = (chain: BaseChainType | undefined, token: Token | undefined) => {
  return chain?.networkType === NetworkType.btc && token?.type === 'brc20';
};

export const AmountSection = (props: AmountSectionProps) => {
  const { chain, token } = props;
  if (isUsingBrc20(chain, token)) {
    return <Brc20AmountSection {...props} />;
  }
  return <CommonAmountSection {...props} />;
};

export const MobileFromSection = (props: MobileFromSectionProps) => {
  const { chain, token } = props;
  if (isUsingBrc20(chain, token)) {
    return <MobileBrc20FromSection {...props} />;
  }
  return <MobileCommonFromSection {...props} />;
};
