import { Text } from '@/components/ui/text';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { useConnectors } from 'wagmi';
import { useDialog } from '@/hooks/dialog';
import { WalletDriverName } from '@/hooks/wallet/common';
import { WalletNotInstallDialog } from '@/hooks/wallet/connect';
import { Chain } from 'viem';
import { bitlayerMainnet, bitlayerTestnet } from '@/wallets/config/chains';

interface LinkData {
  label: string;
  href: string;
  onClick?: () => void;
}

interface SectionData {
  label: string;
  links: LinkData[];
}

const cornerVariants = cva('bl-absolute bl-h-2 bl-w-2 bl-border-primary', {
  variants: {
    position: {
      tl: 'bl-border-t bl-border-l bl-top-0 bl-left-0',
      tr: 'bl-border-t bl-border-r bl-top-0 bl-right-0',
      bl: 'bl-border-b bl-border-l bl-bottom-0 bl-left-0',
      br: 'bl-border-b bl-border-r bl-bottom-0 bl-right-0',
    },
  },
});

export default function FooterNavigation() {
  const { t } = useTranslation();

  const connectors = useConnectors();
  const { open } = useDialog();
  const id = WalletDriverName.MetaMask;

  const addChain = async (chain: Chain) => {
    const connector = connectors.find((item) => item.id.startsWith(id));
    if (!connector) {
      return open({
        content: ({ close }) => <WalletNotInstallDialog id={id} close={close} />,
      });
    }
    const provider = (await connector.getProvider()) as EIP1193Provider;
    provider.request({
      method: 'wallet_addEthereumChain',
      params: [
        {
          chainId: `0x${chain.id.toString(16)}`,
          chainName: chain.name,
          nativeCurrency: chain.nativeCurrency,
          rpcUrls: chain.rpcUrls.default.http,
          blockExplorerUrls: [chain.blockExplorers?.default.url],
          iconUrls: chain.custom?.iconUrls,
        },
      ],
    });
  };

  const navigationSections: SectionData[] = [
    {
      label: t('navigation.footer.Participate'),
      links: [
        {
          label: t('navigation.links.bridge'),
          href: '/bridge',
        },
        {
          label: t('navigation.links.dappCenter'),
          href: '/ready-player-one/dapps-center',
        },
        {
          label: t('navigation.links.leaderboard'),
          href: '/ready-player-one/leaderboard',
        },
        {
          label: t('navigation.links.readyPlayerOne'),
          href: '/airdrop/ready-player-one',
        },
        {
          label: t('navigation.links.career'),
          href: 'https://bitlayerlabs.notion.site/Bitlayer-Job-Board-e3ac3c8dffdf41d090997397fa63d4ba',
        },
      ],
    },
    {
      label: t('navigation.links.resources'),
      links: [
        {
          label: t('pages.developers.title.blog'),
          href: 'https://blog.bitlayer.org/',
        },
        {
          label: t('navigation.footer.Podcast'),
          href: 'https://blog.bitlayer.org/?types=audio',
        },
        {
          label: t('navigation.links.brandKit'),
          href: 'https://github.com/bitlayer-org/bitlayer-materials',
        },
      ],
    },
    {
      label: t('navigation.links.developers'),
      links: [
        {
          label: t('navigation.links.startBuilding'),
          href: '/developers',
        },
        {
          label: t('navigation.links.developersSection.tools.multisigWallet'),
          href: 'https://multisign.bitlayer.org/welcome',
        },
        {
          label: t('navigation.links.developersSection.documentation'),
          href: 'https://docs.bitlayer.org/docs/Build/GettingStarted/QuickStart',
        },
        {
          label: t('navigation.links.developersSection.security.title'),
          href: 'https://docs.bitlayer.org/docs/Build/TrackPack/DappSecurityManual/',
        },
        {
          label: t('navigation.footer.MainScan'),
          href: 'https://www.btrscan.com',
        },
        {
          label: t('navigation.links.developersSection.github'),
          href: 'https://github.com/bitlayer-org',
        },
        {
          label: t('navigation.footer.TestScan'),
          href: 'https://testnet.btrscan.com',
        },
        {
          label: t('navigation.links.developersSection.developerSupport.title'),
          href: '/developers#operationalSupport',
        },
        {
          label: t('navigation.footer.Faucet'),
          href: '/faucet',
        },
        // {
        //   label: 'FAQs',
        //   href: '/',
        // },
        // {
        //   label: t('navigation.footer.SupportedWallets'),
        //   href: 'https://docs.bitlayer.org/docs/DeveloperResources/Wallets',
        // },
        {
          label: t('navigation.links.testnetBridge'),
          href: '/bridge/testnet',
        },
        {
          label: t('navigation.links.addMainnet'),
          href: '',
          onClick: () => addChain(bitlayerMainnet.chain),
        },
        {
          label: t('navigation.links.developersSection.tools.theGraph'),
          href: 'https://docs.bitlayer.org/docs/Build/DeveloperResources/Indexers/TheGraph/',
        },
        {
          label: t('navigation.links.addTestnet'),
          href: '',
          onClick: () => addChain(bitlayerTestnet.chain),
        },
      ],
    },
  ];
  return (
    <div className="bl-w-full bl-relative bl-px-0 md:bl-p-4  bl-border-b bl-border-divider md:bl-border-none">
      <div className="bl-w-full bl-flex bl-flex-wrap">
        {navigationSections.map((section, index) => (
          <div
            key={section.label}
            className="bl-group bl-flex bl-flex-col bl-flex-1 bl-min-w-[49%]"
          >
            <div
              className={cn(
                'bl-relative bl-px-4 md:bl-px-0 bl-h-12 md:bl-h-16 bl-border-t bl-border-b bl-border-divider md:bl-border-none',
                {
                  'bl-border-r bl-border-divider': index === 0,
                },
              )}
            >
              <Text
                variant="dark"
                className="bl-flex bl-items-center bl-text-sm md:bl-text-xl bl-h-full md:bl-pl-15 bl-text-secondary md:bl-border md:bl-border-divider
                group-hover:bl-text-white
                group-hover:bl-border-primary
                group-hover:bl-border-b-divider
                group-hover:bl-border-l-0
                group-hover:bl-border-r-0
                bl-relative
                "
              >
                {section.label}
                <div className="bl-hidden group-hover:bl-block bl-absolute bl-top-0 bl-bottom-0 bl-left-0 bl-w-[1px] bl-bg-gradient-to-b bl-from-primary/80 bl-to-divider"></div>
                <div className="bl-hidden group-hover:bl-block bl-absolute bl-top-0 bl-bottom-0 bl-right-0 bl-w-[1px] bl-bg-gradient-to-b bl-from-primary/80 bl-to-divider"></div>
              </Text>
              <div className="bl-absolute bl-top-0 bl-left-0 bl-w-full bl-h-full bl-bg-gradient-to-b bl-from-primary/50 bl-opacity-0 bl-duration-200 group-hover:bl-opacity-30"></div>
              <div
                className="bl-absolute bl-top-0 bl-left-0 bl-w-full bl-h-full bl-opacity-0 group-hover:bl-opacity-100"
                style={{
                  backgroundImage: `url("/images/home/<USER>")`,
                }}
              ></div>
            </div>

            <ul
              className={cn(
                'bl-flex bl-flex-1 bl-flex-col md:bl-border-l md:bl-border-r md:bl-border-divider md:bl-pl-15 md:bl-pt-9 md:bl-pb-12 bl-px-4 bl-py-7',
                {
                  'bl-border-r bl-border-divider': index === 0,
                  'bl-flex-row bl-flex-wrap md:bl-border-b': index === 2,
                },
              )}
            >
              {section.links.map((link) => (
                <li
                  key={link.label}
                  className={cn('bl-mb-6 md:bl-mb-8 last:bl-mb-0', {
                    'bl-min-w-[50%]': index === 2,
                  })}
                >
                  <Text
                    variant="dark"
                    className="bl-font-body bl-text-base md:bl-text-[28px] bl-text-secondary bl-duration-200 group-hover:bl-text-white group-hover:hover:bl-text-primary group-hover:hover:bl-font-bold bl-flex bl-gap-2 bl-group/item"
                  >
                    {link.onClick ? (
                      <div onClick={link.onClick} className="bl-cursor-pointer">
                        {link.label}
                      </div>
                    ) : (
                      <>
                        <a href={link.href} target="_blank" rel="noreferrer">
                          {link.label}
                        </a>
                        <img
                          src="/images/home/<USER>"
                          className="bl-hidden group-hover/item:bl-block bl-w-3 bl-h-3"
                        />
                      </>
                    )}
                  </Text>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
      <div className="bl-hidden md:bl-block">
        <div className={cornerVariants({ position: 'tl' })}></div>
        <div className={cornerVariants({ position: 'tr' })}></div>
        <div className={cornerVariants({ position: 'bl' })}></div>
        <div className={cornerVariants({ position: 'br' })}></div>
      </div>
    </div>
  );
}
