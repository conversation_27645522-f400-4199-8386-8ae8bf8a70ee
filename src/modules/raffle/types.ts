export interface ICollectionItem {
  categoryId: number;
  itemId?: string;
  itemName: string;
  amount: number;
  resourceUrl?: string;
  star: string;
}

export type ItemInfo = {
  categoryId: string;
  itemId: string;
  itemName: string;
  amount: string;
  resourceUrl: string;
  categoryType: string;
  star?: string;
  count?: number;
};

// 0: one 1: ten 2:free
export type drawTypeProps = '0' | '1' | '2';

export interface InfoResponse {
  address: string;
  normalCarAmount: string;
  premiumCarAmount: string;
  topCarAmount: string;
  remainFreeDrawAmount: string;
  itemList: ICollectionItem[];
  estimateIncome: number;
  needToastStar?: string;
  totalCostInUSD: number;
  roi: number;
  premiumAssembleUsd: number;
  topAssembleUsd: number;
  rebateAmountUsd: number;
  inviteeCount: string;
}

export interface CarPriceResponse {
  normalCarPrice: number;
  premiumCarPrice: number;
  topCarPrice: number;
}

export interface RoiResponse {
  points: {
    x: string;
    y: string;
  }[];
}

export interface PreDrawResponse {
  drawId: string;
  expireTime: string;
}

export interface ResultResponse {
  status: number;
  drawType: number;
  itemInfos: ItemInfo[];
}

export interface InviteHistoryResponse {
  total: {
    totalFreeDraw: number;
    totalSingleDraw: number;
    totalTenDraw: number;
    totalHundredDraw: number;
    totalSpent: number;
    totalRebate: number;
  };
  invites: {
    inviteeAddress: string;
    drawCounts: {
      freeDraw: number;
      singleDraw: number;
      tenDraw: number;
      hundredDraw: number;
    };
    spent: number;
    rebate: number;
  }[];
  page: number;
  pageSize: number;
  totalPages: number;
}
