import { CheckPointsResponse, TaskDetail, XIntent } from '../types';
import { useNavigate } from '@/components/i18n/navigate';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useLocation, useRevalidator } from '@remix-run/react';
import ky, { HTTPError } from 'ky';
import { createContext, useContext, useState } from 'react';
import { chain as BitlayerNetwork } from '@/modules/user-center/config';
import { Address, encodeFunctionData } from 'viem';
import { bitlayerMainnetChain, bitlayerTestnetChain } from '@/wallets/config/chains';
import { useBalance, useConfig } from 'wagmi';
import { atom, useSetAtom } from 'jotai';

import { abi } from '../abi/daily';
import { useAccount } from '@/hooks/wallet/account';
import { incrBy } from '@/lib/utils';

import {
  estimateFeesPerGas,
  estimateGas,
  waitForTransactionReceipt,
  writeContract,
} from '@wagmi/core';

interface UserCenterTaskContextData {
  bindXOpen: boolean;
  setBindXOpen: (open: boolean) => void;
}

export const UserCenterTaskContext = createContext<UserCenterTaskContextData>(
  {} as UserCenterTaskContextData,
);

const dailyCheckAddress: Record<number, Address> = {
  [bitlayerMainnetChain.id]: '******************************************',
  [bitlayerTestnetChain.id]: '******************************************',
};

export const useStartTask = (taskId: number) => {
  return useMutation({
    mutationKey: ['startTask', taskId],
    mutationFn: async () => {
      await ky.post('/me/task/start', {
        json: { taskId },
      });
    },
  });
};

export const useDailyCheck = (projectId: string) => {
  const { address } = useAccount({ network: BitlayerNetwork.networkType });
  const contract = dailyCheckAddress[BitlayerNetwork.chain.id];
  const isTestNet = BitlayerNetwork.chain.id === bitlayerTestnetChain.id;
  const config = useConfig();
  const [isNotBalance, setIsNotBalance] = useState(false);
  const revalidator = useRevalidator();
  const { data: btcBalance } = useBalance({
    address: address as `0x${string}`,
    config,
    chainId: BitlayerNetwork.chain.id,
  });
  const functionName = 'claimPoint';
  return useMutation({
    mutationKey: ['/task/daily-check'],
    mutationFn: async () => {
      try {
        const feesPerGas = await estimateFeesPerGas(config);
        const perGas = Number(feesPerGas.maxFeePerGas) * 50000;
        const _isNotBalance = Number(perGas) > Number(btcBalance?.value);
        setIsNotBalance(_isNotBalance);
        if (projectId && !_isNotBalance) {
          await ky.get('/me/task/daily-check');
          const args = [projectId] as const;
          const gas = await estimateGas(config, {
            data: encodeFunctionData({
              abi,
              functionName,
              args,
            }),
            to: contract,
            account: address as Address,
          });
          const gasData = isTestNet
            ? {}
            : {
                gas: incrBy(gas, 0.2),
                maxFeePerGas: incrBy(feesPerGas.maxFeePerGas, 0.5),
                maxPriorityFeePerGas: incrBy(feesPerGas.maxPriorityFeePerGas, 0.5),
              };
          try {
            const tx = await writeContract(config, {
              abi,
              address: contract,
              chainId: BitlayerNetwork.chain.id,
              functionName,
              args,
              ...gasData,
            });
            await waitForTransactionReceipt(config, {
              hash: tx,
              pollingInterval: 5_000,
            });
            revalidator.revalidate();
          } catch (error) {
            await ky.get('/me/task/daily-cancel');
            revalidator.revalidate();
          }
        }
      } catch (e) {
        console.log(e);
        await ky.get('/me/task/daily-cancel');
      }
      return {
        isNotBalance,
      };
    },
  });
};

export const useVerifyTask = (taskId: number) => {
  return useMutation({
    mutationKey: ['verifyTask', taskId],
    mutationFn: async () => {
      await ky.post('/me/task/verify', {
        json: { taskId },
      });
      return true;
    },
  });
};

export const useFollowTask = (taskId: number) => {
  return useMutation({
    mutationKey: ['followTask', taskId],
    mutationFn: async ({ redirect }: { redirect?: string }) => {
      const url = `${window.location.origin}/me/x-callback`;
      const data: { url: string } = await ky
        .post('/me/task/follow', {
          json: { taskId, url, redirect },
        })
        .json();
      return data.url;
    },
  });
};

export const useClaimTask = (taskId: number, taskType: number) => {
  return useMutation({
    mutationKey: ['claimTask', taskId],
    mutationFn: async () => {
      await ky.post('/me/task/claim', {
        json: { taskId, taskType },
      });
      return true;
    },
  });
};

export const useClaimPoints = (taskName: string) => {
  return useMutation({
    mutationKey: ['claimPoints', taskName],
    mutationFn: async () => {
      await ky.post('/me/task/claimPoints', {
        json: { taskName },
      });
      return true;
    },
  });
};

export const useCheckPoints = (taskName: string, isSigned: boolean) => {
  return useQuery<CheckPointsResponse>({
    queryKey: ['/me/task/check', taskName],
    queryFn: async () => {
      return await ky.get(`/me/task/check/${taskName || ''}`).json();
    },
    enabled: isSigned,
  });
};

export const useBindX = () => {
  return useMutation({
    mutationFn: async () => {
      const url = `${window.location.origin}/me/x-callback`;
      const data: { url: string } = await ky
        .post('/me/x-bind', {
          json: { url },
        })
        .json();
      return data.url;
    },
  });
};

interface RouteTaskState {
  userCenterTask?: {
    taskId?: number;
    timeOnPage?: number;
  };
}

export const useReportStayTime = (pageName: string) => {
  const { state } = useLocation();
  useQuery({
    queryKey: ['reportStayTime', pageName],
    queryFn: async () => {
      const taskId = (state as RouteTaskState).userCenterTask?.taskId;
      const resp = await fetch('/me/task/report', {
        method: 'POST',
        body: JSON.stringify({ taskId, pageName }),
      });
      if (resp.ok) {
        return (await resp.json()) as { checked: boolean };
      }
      return undefined;
    },
    refetchInterval: (query) => {
      if (query.state.data?.checked) {
        return 0;
      }
      return 5_000;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    enabled:
      typeof state === 'object' &&
      (state as RouteTaskState)?.userCenterTask?.timeOnPage !== undefined,
  });
};

export const makeXIntent = (intent?: XIntent) => {
  if (!intent) {
    return;
  }
  const searchParams = new URLSearchParams(intent.params);
  const shareText = `https://x.com/intent/${intent.type}?${searchParams.toString()}`;
  return shareText;
  // window.open(shareText, '_blank', 'width=785,height=600');
};

export interface UseTaskActionOptions {
  onBefore?: () => void;
  onAfter?: () => void;
  onSuccess?: () => void;
  onFailure?: () => void;
  onError?: (error: unknown) => void;
}

export const useTaskAction = (task: TaskDetail, options: UseTaskActionOptions = {}) => {
  const setDaily = useSetAtom(dailyAtom);
  const { action, taskId } = task;
  const navigate = useNavigate();
  const revalidator = useRevalidator();

  const { mutateAsync: startTask, isPending: isStarting } = useStartTask(taskId);
  const { mutateAsync: verifyTask, isPending: isVerifying } = useVerifyTask(taskId);
  const { mutateAsync: followTask, isPending: isFollowing } = useFollowTask(taskId);
  const { mutateAsync: claimTask, isPending: isClaiming } = useClaimTask(taskId, task.taskType);

  const { setBindXOpen } = useContext(UserCenterTaskContext);

  const handleTask = async () => {
    if (task.canClaim) {
      if (task.title === 'Total TXN') {
        setDaily((prev) => ({ ...prev, isTxClaiming: true }));
      } else if (task.title === 'Total Bridged In') {
        setDaily((prev) => ({ ...prev, isBridgeClaiming: true }));
      }
      await claimTask(undefined, {
        onSuccess: () => {
          options.onSuccess?.();
        },
      });
      const interval = setInterval(() => {
        revalidator.revalidate();
        if (!task.canClaim) {
          clearInterval(interval);
        }
      }, 8000);
      return;
    }

    options.onBefore?.();

    switch (action.type) {
      case 'route':
        await startTask();
        navigate(action.payload?.path as string, {
          state: { userCenterTask: { taskId, timeOnPage: action.payload?.timeOnPage } },
        });
        break;
      case 'redirect':
        await startTask();
        window.open((action.payload as { path: string }).path, '_blank');
        revalidator.revalidate();
        break;
      case 'follow':
        if (action.payload?.isBind) {
          await verifyTask();
          break;
        }
        await startTask();
        await followTask(
          {
            redirect: action.payload?.path,
          },
          {
            onSuccess: (url?: string) => {
              if (url) {
                window.open(url, '_blank');
              }
            },
          },
        );
        break;
      case 'verify':
        await startTask();
        if (await verifyTask()) {
          options.onSuccess?.();
        } else {
          options.onFailure?.();
        }
        revalidator.revalidate();
        break;
      case 'x-intent':
        await startTask();
        // makeXIntent(action.payload?.intent);
        break;
      default:
        await startTask();
        revalidator.revalidate();
    }

    options.onAfter?.();
  };

  const onClick = async () => {
    try {
      await handleTask();
    } catch (e: unknown) {
      console.error(e);
      if (e instanceof HTTPError && e.response.status === 400) {
        const { code } = (await e.response.json()) as { code?: number };
        if (code === 200008) {
          setBindXOpen(true);
        }
      }
    }
  };

  const buttonProps = {
    onClick,
    disabled: isStarting || isVerifying || isFollowing || isClaiming,
  };

  const anchorProps =
    task.action.type === 'x-intent' && !task.canClaim
      ? { href: makeXIntent(action.payload?.intent), target: '_blank', rel: 'noopener noreferrer' }
      : {};

  return {
    ...buttonProps,
    ...anchorProps,
  };
};

export const dailyAtom = atom({
  isDailyLoading: false,
  isTxClaiming: false,
  isBridgeClaiming: false,
});
