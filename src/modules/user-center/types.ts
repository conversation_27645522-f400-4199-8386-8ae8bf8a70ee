import { staticAsset } from '@/lib/static';

export interface LoginRequest {
  sig: string;
  invite_code?: string;
}

export interface PagedDataWrapper {
  curPage: string;
  myReferrals: ReferralsItem[];
  totalPage: string;
}

export interface LoginResponse {
  token: string;
}

export interface UserInfo {
  daysToBitlayer: number;
  txCount: number;
  bridgedInUsd: string;
  level: number;
  totalPoints: number;
  gemsPoints: number;
  totalTaskCount: number;
  totalCompleteCount: number;
  totalBadgeCount: number;
  obtainBadgeCount: number;
  nextLevelPoints: number;
  currentLevelPoints: number;
  totalBtr: number;
}

export interface DailyInfoResponse {
  dailyCheckinTask: {
    title: string;
    rewardPoints: number;
    action: TaskAction;
    logoType: string;
    isCompleted: boolean;
    completedCount: number;
    targetCount: number;
    taskId: number;
    rank: number;
    mainTitle: string;
    canClaim: boolean;
    taskType: number;
    extraData: {
      is_tx_pending: boolean;
      cur_done_progress: number;
    };
  };
}

export interface ReferralsItem {
  address: string;
  isSuccess: boolean;
  rewardAmount: number;
}

export interface InviteInfo {
  referCode: string;
  referralRewards: number;
  myReferrals: ReferralsItem[];
  referTask: {
    title: string;
    rewardPoints: number;
    action: TaskAction;
    logoType: string;
    isCompleted: boolean;
    completedCount: number;
    targetCount: number;
    taskId: number;
    rank: number;
    mainTitle: string;
    canClaim: boolean;
    taskType: number;
  };
}

export interface RankListInfo {
  total: number;
  list: RankListItem[];
}

export interface TaskAction {
  type: string;
  payload?: {
    path?: string;
    timeOnPage?: number;
    isBind?: boolean;
    template?: string;
    intent?: XIntent;
    contract_addr?: `0x${string}` | null | undefined;
    project_id?: number;
    task_name?: string;
    progress_cfg?: { key: number; value: number }[];
  };
}

export interface PointsResponse {
  claimablePoints: string;
}

export interface CheckPointsResponse {
  claimablePoints: string;
}

export interface ScratchResponse {
  per_draw?: number;
  chances: number;
  is_twitter_auth: boolean;
  is_twitter_bound: boolean;
  points_enough: boolean;
  points_per_draw?: number;
}

export interface LotteryRecord {
  confirm_time: string;
  lottery_type: number;
  user_address: string;
  tx_hash: string;
  amount: string;
  value: string;
  token_name: string;
  decimal: number;
}

export interface DrawHistoryParams {
  pageSize?: number;
  pageToken?: string;
}

export interface DrawHistoryItem {
  draw_time: number;
  reward: string;
  lottery_type: number;
  token_name: string;
  reward_in_usd: string;
}

export interface LoopResponse {
  total: number;
  records: LotteryRecord[];
}

export interface HistoryResponse {
  data: DrawHistoryItem[];
  next_page_token?: string;
}

export interface PreDrawResponse {
  lottery_id: string;
  expire_time: number;
}

export interface ReplyResponse {
  status: 0 | 1 | 2 | 3 | 4; //0: 未发放,1:已取消 2:已过期 3:已发放 4: 发放中
  lottery_type: 0 | 1 | undefined; // 0: token; 1: point
  token_asset: 'btc' | 'eth';
  value: string;
}

export interface CancelResponse {
  lottery_id: string;
}
export interface TaskExtra {
  cur_done_progress: number;
  interrupted_at: number;
  is_tx_pending: boolean;
}

export interface TaskDetail {
  title: string;
  rewardPoints: number;
  action: TaskAction;
  logoType: string;
  isCompleted: boolean;
  canClaim: boolean;
  completedCount: number;
  targetCount: number;
  taskId: number;
  mainTitle?: string;
  rank: number;
  taskType: number;
  extraData: {
    cur_claimed_progress?: number;
    rewards: string;
    cur_done_progress?: number;
    interrupted_at?: number;
    is_tx_pending?: boolean;
    rechecked_in_days?: string;
    today_progress?: string;
    missing_days?: string;
    end_timestamp?: number;
  };
}

export enum BadgeStatus {
  Coming = 1,
  InProgress = 2,
  Finished = 3,
}

export enum BadgeRewardType {
  Points = 1,
  Gems = 2,
  BTR = 3,
}

export enum BadgeObtainStatus {
  NotObtained = 1,
  CanObtain = 2,
  Obtained = 3,
}

export interface BadgeDetail {
  name: string;
  jumpUrl: string;
  status: BadgeStatus;
  isObtained: BadgeObtainStatus;
  rewardAmount: string;
  rewardType: BadgeRewardType;
  badgeId: number;
  icon: {
    back: string;
    face: string;
    outline: string;
  };
}

export interface ActivitySummary {
  name: string;
  points: number;
}

export interface TaskListData {
  dailyTasks: TaskDetail[];
  newRacerTasks: TaskDetail[];
  advanceTasks: TaskDetail[];
  ecoTasks: TaskDetail[];
  myBadge: BadgeDetail[];
  activitySummary: ActivitySummary[];
  ongoingTask: TaskDetail;
  canClaimBadgeCount: number;
  canClaimTaskCount: number;
}

export interface VerifyTaskResult {
  isVerified: boolean;
}

export interface TwitterAuthRequest {
  url: string;
}

export interface TwitterAuthResponse {
  auth_url: string;
  state: string;
}

export interface DailyResponse {
  claimablePoints: string;
}

export interface ReportStayTimeRequest {
  pageName: string;
  unixTimestamp: number;
  taskId: number;
}

export interface ReportStayTimeResponse {
  isChecked: boolean;
}

export interface ClaimPointsResponse {
  claimedPoints: string;
}

export interface ClaimTaskResponse {
  rewardAmount: string;
  rewardType: number;
}

export interface XIntent {
  type: string;
  params: Record<string, string>;
}

export interface RankListItem {
  address: string;
  point: number;
  rank: number;
}

export interface RankRecordsList {
  total: number;
  list: RankRecords[];
}

export interface RankRecords {
  id: number;
  point: number;
  createdAt: number;
  address: string;
}

export enum DrawRewardStatus {
  UNISSUED = 0,
  CANCEL = 1,
  EXPIRED = 2,
  ISSUED = 3,
}

export enum DrawRewardType {
  TOKEN = 0,
  POINT = 1,
}

export const RewardSource: Record<DrawRewardType, { name: string; src: string; unit: string }> = {
  [DrawRewardType.TOKEN]: {
    name: 'BTC',
    src: staticAsset('/images/activities/lucky-draw/coins.26ae8e412d.gif'),
    unit: '$',
  },
  [DrawRewardType.POINT]: {
    name: 'Bitlayer Points',
    src: staticAsset('/images/user-center/bitlayer-golden.10071acb9b.gif'),
    unit: 'x',
  },
};
