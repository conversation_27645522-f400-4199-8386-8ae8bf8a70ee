import { Client, DataWrapper } from '@/lib/api/client';
import { getSession } from '@/modules/session';
import { ActionFunctionArgs } from '@remix-run/cloudflare';

import {
  LoginRequest,
  LoginResponse,
  UserInfo,
  TaskListData,
  VerifyTaskResult,
  TwitterAuthResponse,
  DailyResponse,
  ReportStayTimeRequest,
  ReportStayTimeResponse,
  ClaimTaskResponse,
  RankListInfo,
  RankListItem,
  RankRecordsList,
  InviteInfo,
  PagedDataWrapper,
  ScratchResponse,
  PreDrawResponse,
  ReplyResponse,
  CancelResponse,
  PointsResponse,
  ClaimPointsResponse,
  LoopResponse,
  DrawHistoryParams,
  HistoryResponse,
  DailyInfoResponse,
} from './types';

class UserCenterAPI {
  protected client: Client;
  protected baseRoute: string = import.meta.env.VITE_USER_CENTER_BASE_ROUTE;

  constructor(client: Client) {
    this.client = client;
  }

  async login(payload: LoginRequest): Promise<DataWrapper<LoginResponse>> {
    return this.client.request('post', '/auth/v1/user/login', payload);
  }

  async twitterAuthorize(url: string): Promise<DataWrapper<TwitterAuthResponse>> {
    return this.client.request('post', '/social/v1/twitter/authorize', {
      redirect_url: url,
    });
  }

  async dailyCheck(): Promise<DataWrapper<DailyResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/try_daily_checkin'));
  }

  async dailyCancel(): Promise<DataWrapper<DailyResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/cancel_daily_checkin'));
  }

  async getDailyInfo(): Promise<DataWrapper<DailyInfoResponse>> {
    return this.client.request('get', this.buildRoute('/v1/task/daily_checkin_task'));
  }

  protected buildRoute(route: string) {
    return `${this.baseRoute}${route}`;
  }

  async getUserInfo(): Promise<DataWrapper<UserInfo>> {
    return this.client.request('get', this.buildRoute('/v1/user/info'));
  }

  async getInviteInfo(): Promise<DataWrapper<InviteInfo>> {
    return this.client.request('get', this.buildRoute('/v1/refer/my'));
  }

  async getInviteList({
    page,
    pageSize,
  }: {
    page: number;
    pageSize: number;
  }): Promise<DataWrapper<PagedDataWrapper>> {
    const queryParams = new URLSearchParams();
    if (page) {
      queryParams.set('page', page.toString());
    }
    if (pageSize) {
      queryParams.set('pageSize', pageSize.toString());
    }
    return this.client.request(
      'get',
      this.buildRoute(`/v1/refer/my/list?${queryParams.toString()}`),
    );
  }

  async getTaskList(): Promise<DataWrapper<TaskListData>> {
    const resp = await this.client.request<TaskListData>('get', this.buildRoute('/v1/task/list'));
    if (resp.data) {
      const { dailyTasks, newRacerTasks, advanceTasks, ecoTasks } = resp.data;
      dailyTasks.sort((a, b) => a.rank - b.rank);
      newRacerTasks.sort((a, b) => a.rank - b.rank);
      advanceTasks.sort((a, b) => a.rank - b.rank);
      ecoTasks.sort((a, b) => a.rank - b.rank);
      resp.data.dailyTasks = dailyTasks;
      resp.data.newRacerTasks = newRacerTasks;
      resp.data.advanceTasks = advanceTasks;
      resp.data.ecoTasks = ecoTasks;
    }
    return resp;
  }

  async getRacersList(): Promise<DataWrapper<RankListInfo>> {
    const params = new URLSearchParams({
      page: '1',
      limit: '5',
    });
    return this.client.request('get', this.buildRoute('/v1/point/ranks'), params);
  }

  async getMeRank(address: string | undefined): Promise<DataWrapper<RankListItem>> {
    return this.client.request('get', this.buildRoute(`/v1/point/rank/${address}`));
  }

  async getRankRecords(): Promise<DataWrapper<RankRecordsList>> {
    const params = new URLSearchParams({
      page: '1',
      limit: '30',
    });
    return this.client.request('get', this.buildRoute(`/v1/point/incr/records`), params);
  }

  async getPointInfo(): Promise<DataWrapper<PointsResponse>> {
    return this.client.request('get', this.buildRoute(`/v1/eco/bullish_points`));
  }

  async getScratchInfo(): Promise<DataWrapper<ScratchResponse>> {
    return this.client.request('get', `/scratch/v1/scratch/info`);
  }

  async getLoopRecords(): Promise<DataWrapper<LoopResponse>> {
    return this.client.request('get', `/scratch/v1/scratch/loop_show_records`);
  }

  async getDrawHistory({ pageSize, pageToken }: DrawHistoryParams = {}): Promise<
    DataWrapper<HistoryResponse>
  > {
    const queryParams = new URLSearchParams();
    if (pageSize) {
      queryParams.set('pageSize', pageSize.toString());
    }
    if (pageToken) {
      queryParams.set('pageToken', pageToken);
    }
    return this.client.request('get', `/scratch/v1/scratch/draw/history?${queryParams.toString()}`);
  }

  async getPreDraw(): Promise<DataWrapper<PreDrawResponse>> {
    return this.client.request(
      'post',
      `/scratch/v1/scratch/pre_draw`,
      {},
      { 'Content-Type': 'application/json' },
    );
  }

  async getDrawReply({ lottery_id }: { lottery_id: string }): Promise<DataWrapper<ReplyResponse>> {
    const params = new URLSearchParams({
      lottery_id,
    });
    return this.client.request('get', `/scratch/v1/scratch/draw_reply`, params);
  }

  async getCancel({ lottery_id }: { lottery_id: string }): Promise<DataWrapper<CancelResponse>> {
    const params = new URLSearchParams({
      lottery_id,
    });
    return this.client.request('get', `/scratch/v1/scratch/cancel`, params);
  }

  async startTask(taskId: number): Promise<DataWrapper<void>> {
    return this.client.request('post', this.buildRoute('/v1/task/start'), { taskId });
  }

  async verifyTask(taskId: number): Promise<DataWrapper<VerifyTaskResult>> {
    return this.client.request('post', this.buildRoute('/v1/task/verify'), { taskId });
  }

  async claimTask(taskId: number, taskType: number): Promise<DataWrapper<ClaimTaskResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/reward/claim'), {
      taskId,
      taskType,
    });
  }

  async updateTask(taskId: number): Promise<DataWrapper<void>> {
    return this.client.request('post', this.buildRoute('/v1/task/update'), { taskId });
  }

  async claimBadge(badgeId: number): Promise<DataWrapper<void>> {
    return this.client.request('post', this.buildRoute('/v1/reward/claim'), { badgeId });
  }

  async claimPoints(taskName: string): Promise<DataWrapper<ClaimPointsResponse>> {
    return this.client.request('post', this.buildRoute('/v1/eco/claim_points'), {
      task_name: taskName,
    });
  }

  async reportStayTime(data: ReportStayTimeRequest): Promise<DataWrapper<ReportStayTimeResponse>> {
    return this.client.request('post', this.buildRoute('/v1/page/stay-time'), data);
  }

  async getTotalClaimedPoints(
    task_name: string,
  ): Promise<DataWrapper<{ claimablePoints: string }>> {
    const params = new URLSearchParams({
      task_name,
    });
    return this.client.request('get', this.buildRoute('/v1/eco/eco_claimable_points'), params);
  }

  // async carsAssemble(data: { starRating: number }): Promise<DataWrapper<{ isSuccess: number }>> {
  //   return this.client.request('post', this.buildRoute('/v1/cars/assemble'), data);
  // }
}

export const createAPIClient = (token?: string, ip?: string) => {
  const endpoint = import.meta.env.VITE_USER_CENTER_ENDPOINT;
  const client = new Client(endpoint);
  if (token) {
    client.setToken(token);
  }
  if (ip) {
    client.setIp(ip);
  }
  return new UserCenterAPI(client);
};

export const createAPIRquest = async (request: ActionFunctionArgs['request']) => {
  const session = await getSession(request);
  const token = session.get('user.token') as string | undefined;
  const visitorId = session.get('user.visitorId') as string | undefined;

  const cfConnectingIp = request.headers.get('CF-Connecting-IP');
  const ip = cfConnectingIp || '';
  const endpoint = import.meta.env.VITE_USER_CENTER_ENDPOINT;
  const client = new Client(endpoint);
  if (token) {
    client.setToken(token);
  }
  if (ip) {
    client.setIp(ip);
  }
  if (visitorId) {
    client.setVisitorId(visitorId);
  }
  return new UserCenterAPI(client);
};
