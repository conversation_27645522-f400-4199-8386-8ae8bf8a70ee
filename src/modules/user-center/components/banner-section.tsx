import { AnimatePageLayout } from '@/components/ui/page';
import { useCallback, useEffect, useState } from 'react';
import { BitlayerPoints, NumberBlock } from '@/modules/user-center/components/number-block';
import { ContinuesTaskItem } from '@/modules/user-center/components/task';
import { LoginButton, UserProfile } from '@/modules/user-center/components/user-profile';
import { useAccount } from '@/hooks/wallet/account';
import { useRevalidator } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { useLoginContext } from '@/modules/user-center/components/login';
import { chain } from '@/modules/user-center/config';
import { Button } from '@/components/ui/button';
import { Title } from '@/components/ui/title';
import { LoaderResponse } from '@/routes/($lang)._app+/me+/_index+/_layout';
import { useMediaQuery } from '@react-hook/media-query';
import { cn } from '@/lib/utils';
import { dailyAtom } from '@/modules/user-center/hooks/task';
import { useDailyCheck } from '@/modules/btcfi/hooks/task';

import { BtcDialog } from '@/modules/mining-gala/components/btc-dialog.tsx';
import { useDebounceFn } from 'ahooks';
import { useSetAtom, useAtomValue } from 'jotai';
import { LoaderIcon } from 'lucide-react';

export function BannerSection({
  loaderData,
  bgImgPc,
  bgImgMobile,
  showCar,
  title1,
  title2,
  height,
}: {
  loaderData: LoaderResponse;
  bgImgPc?: string;
  bgImgMobile?: string;
  showCar: boolean;
  title1?: string;
  title2?: string;
  height?: number;
}) {
  const { address } = useAccount({ network: chain.networkType });
  const { isSigned, isSigning, login } = useLoginContext();
  const revalidator = useRevalidator();
  const { t } = useTranslation();
  const isMobile = useMediaQuery('(max-width: 640px)');
  const [noBalance, setNoBalance] = useState(false);
  const setDaily = useSetAtom(dailyAtom);
  const dailyInfo = useAtomValue(dailyAtom);

  const bgImg = isMobile ? bgImgMobile : bgImgPc;

  const data = Object.assign({}, loaderData, {
    profile: isSigned ? loaderData.profile : {},
    dailyInfo: loaderData?.tasks?.dailyTasks?.filter(
      (item) => item.action.type === 'on_chain_gas',
    )[0],
  });

  const { mutate: checkDaily, data: checkInfo } = useDailyCheck();

  const handleClick = () => {
    setDaily((prev) => ({ ...prev, isDailyLoading: true }));
    checkDaily(
      { projectId: String(data?.dailyInfo?.action?.payload?.project_id) },
      {
        onSuccess: (result) => {
          setNoBalance(result.isNotBalance);
          setDaily((prev) => ({ ...prev, isDailyLoading: !result.isNotBalance }));
        },
      },
    );
  };

  const { run: handleDailyCheck } = useDebounceFn(
    () => {
      handleClick();
    },
    { wait: 1000 },
  );

  useEffect(() => {
    if (checkInfo?.isNotBalance) {
      setNoBalance(true);
    }
  }, [checkInfo]);

  const handleLogin = useCallback(() => {
    if (!address) {
      return;
    }
    login(address, {
      onSuccess: () => {
        revalidator.revalidate();
      },
    });
  }, [address, login, revalidator]);

  return (
    <AnimatePageLayout>
      <div className="bl-w-full bl-bg-black bl-relative bl-overflow-hidden">
        <div className="bl-absolute bl-top-[84%] lg:bl-top-[61%] bl-z-[5] bl-left-0 bl-w-[110%] bl-h-1/2 bl-bg-white bl-transform bl-rotate-3 bl-origin-top-left"></div>
        <div
          className={cn(
            'bl-container bl-overflow-hidden bl-relative bl-min-h-[700px] lg:bl-min-h-[730px]',
            {
              [`bl-min-h-[${height}px]`]: height,
            },
          )}
        >
          {bgImg && (
            <img
              className="bl-absolute bl-top-0 bl-right-0 bl-w-full bl-h-full md:bl-h-auto md:bl-w-[80%] bl-object-fill bl-z-0"
              src={bgImg}
              alt="section-bg"
            />
          )}
          <section className="md:bl-px-12 lg:bl-w-[1440px] bl-pt-24">
            <div className="bl-flex bl-justify-between">
              <UserProfile
                address={address}
                daysOnBitlayer={data.profile.daysOnBitlayer}
                level={data.profile.level}
                totalPoints={data.profile.totalPoints}
              />
              <div className="bl-flex bl-gap-1.5 lg:bl-gap-3.5">
                <div className="bl-flex bl-flex-col lg:bl-flex-row bl-gap-1.5 lg:bl-gap-3.5">
                  {isSigned && data.tasks?.ongoingTask && (
                    <div className="bl-hidden md:bl-flex">
                      <ContinuesTaskItem task={data.tasks.ongoingTask} />
                    </div>
                  )}
                  <NumberBlock label={t('pages.userCenter.txn')} value={data.profile.txn} />
                  <NumberBlock
                    label={t('pages.userCenter.bridged')}
                    value={data.profile.bridgedInUsd ? `$${data.profile.bridgedInUsd}` : undefined}
                  />
                  {isSigned && data.tasks?.ongoingTask && (
                    <div className="bl-flex bl-justify-end md:bl-hidden">
                      <ContinuesTaskItem task={data.tasks.ongoingTask} />
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="bl-flex bl-flex-col lg:bl-justify-between lg:bl-flex-row bl-gap-6 lg:bl-gap-10 bl-mt-10 lg:bl-mt-20">
              <div className="lg:bl-w-[500px] bl-flex bl-relative bl-justify-center md:bl-justify-start">
                <div className="bl-flex bl-flex-row bl-gap-2">
                  <Title
                    variant="white"
                    className="bl-text-[45px] md:bl-text-[80px] md:bl-font-bold bl-uppercase"
                  >
                    {title1 || 'racer'}
                  </Title>
                  <Title
                    variant="default"
                    className="bl-text-[45px] md:bl-text-[80px] md:bl-font-bold bl-uppercase"
                  >
                    {title2 || 'center'}
                  </Title>
                </div>

                {showCar && (
                  <img
                    className="bl-top-14 bl-hidden lg:bl-block bl-absolute bl-z-[10]"
                    src="/images/user-center/banner/car.png"
                    alt="car"
                  />
                )}
              </div>
              {showCar && (
                <div className="lg:bl-w-1/2 bl-w-full bl-space-y-3 lg:bl-space-y-4">
                  <BitlayerPoints
                    points={data.profile.totalPoints}
                    gems={data.profile.gemsPoints}
                    btr={data.profile.btr}
                    loaderData={loaderData}
                  />
                  {!isSigned && (
                    <div className="bl-flex bl-justify-center bl-pt-9">
                      <LoginButton
                        isSigned={isSigned}
                        isSigning={isSigning}
                        onLogin={handleLogin}
                      />
                    </div>
                  )}
                </div>
              )}
              {!showCar && (
                <div className="lg:bl-w-1/2 bl-w-full bl-space-y-3 lg:bl-space-y-4">
                  <BitlayerPoints
                    points={data.profile.totalPoints}
                    gems={data.profile.gemsPoints}
                  />
                  {!isSigned && (
                    <div className="bl-flex bl-justify-center bl-pt-9">
                      <LoginButton
                        isSigned={isSigned}
                        isSigning={isSigning}
                        onLogin={handleLogin}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </section>
        </div>
      </div>
      <BtcDialog open={noBalance} onOpenChange={setNoBalance} />
    </AnimatePageLayout>
  );
}
