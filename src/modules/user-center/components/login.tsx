import { createContext, useContext, useEffect, useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Address } from 'viem';
import { useSignMessage } from 'wagmi';

export const useLogin = () => {
  const { signMessageAsync } = useSignMessage();
  return useMutation({
    mutationKey: ['login'],
    mutationFn: async (address: string) => {
      const signature = await signMessageAsync({
        account: address as Address,
        message: 'BITLAYER',
      });
      await fetch('/me/login', {
        method: 'POST',
        body: JSON.stringify({
          address,
          signature,
        }),
      });
    },
  });
};

export const useLogout = () => {
  return useMutation({
    mutationKey: ['logout'],
    mutationFn: async (active?: boolean) => {
      await fetch('/me/logout', {
        method: 'POST',
        body: JSON.stringify({
          active,
        }),
      });
    },
  });
};

type LoginFunction = ReturnType<typeof useLogin>['mutate'];
type LogoutFunction = ReturnType<typeof useLogout>['mutate'];

interface LoginContextData {
  isSigned?: boolean;
  isSigning?: boolean;
  login: LoginFunction;
  logout: LogoutFunction;
}

export const LoginContext = createContext({} as LoginContextData);

export const useLoginContext = () => {
  return useContext(LoginContext);
};

interface LoginProviderProps {
  children?: React.ReactNode;
  walletAddress?: string;
  sessionAddress?: string;
}

export const LoginProvider = ({ walletAddress, sessionAddress, children }: LoginProviderProps) => {
  const [isSigned, setIsSigned] = useState(false);
  const { mutate: login, isPending: isSigning } = useLogin();
  const { mutate: logout } = useLogout();

  useEffect(() => {
    let signedState = false;
    if (walletAddress) {
      signedState = walletAddress === sessionAddress;
    }
    setIsSigned(signedState);
  }, [walletAddress, sessionAddress]);

  return (
    <LoginContext.Provider value={{ isSigned, isSigning, login, logout }}>
      {children}
    </LoginContext.Provider>
  );
};
