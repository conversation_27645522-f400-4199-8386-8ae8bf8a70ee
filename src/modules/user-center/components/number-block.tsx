import { staticAsset } from '@/lib/static';
import { cn } from '@/lib/utils';
import { useRef, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useCountUp } from 'react-countup';
import useChange from '@react-hook/change';
import RacersCard from '@/modules/user-center/components/racers-card.tsx';
import { LoaderResponse } from '@/routes/($lang)._app+/me+/_index+/_layout';

export interface NumberBlockProps {
  label: string;
  value?: string;
}

export const NumberBlock = ({ label, value }: NumberBlockProps) => {
  return (
    <div className="bl-flex bl-h-6 lg:bl-h-10 bl-tracking-wide">
      <div className="bl-bg-primary bl-text-xs/6 lg:bl-text-xl/[2.5rem] bl-text-black/50 bl-px-2 bl-w-[50px] md:bl-w-14 bl-text-center lg:bl-w-auto">
        {label}
      </div>
      <div className="bl-bg-[#d2d2d2]/20 bl-text-xs/6 lg:bl-text-base/[2.5rem] bl-text-white bl-px-3.5 bl-text-center bl-w-[86px] lg:bl-w-auto">
        {value || '--'}
      </div>
    </div>
  );
};

export interface TaskNumberBlockProps {
  label: string;
  accomplished?: number;
  total?: number;
  background?: string;
  className?: string;
  badge?: number;
  onClick?: () => void;
}

export const TaskNumberButton = ({
  label,
  accomplished,
  total,
  background = '/images/user-center/race-01.png',
  badge,
  className,
  onClick,
}: TaskNumberBlockProps) => {
  const Badge = () => {
    if (!badge) return null;
    return (
      <div
        className={cn(
          'bl-size-4.5 bl-text-center bl-bg-primary bl-text-xs/[1.125rem] bl-text-white',
          'bl-absolute bl-top-0 bl-right-0 bl-translate-x-1/2 -bl-translate-y-1/2 bl-rounded-full',
        )}
      >
        {badge}
      </div>
    );
  };

  return (
    <button
      onClick={onClick}
      className={cn(
        'bl-group bl-h-[56px] lg:bl-h-[85px] bl-relative',
        'bl-border bl-border-secondary focus-visible:bl-outline-none',
        className,
      )}
    >
      <div className="bl-size-full bl-overflow-hidden bl-absolute bl-top-0 bl-left-0 -bl-z-10">
        <img
          className="bl-size-full bl-object-cover bl-object-center bl-duration-500 bl-ease-in-out group-hover:bl-scale-125"
          src={background}
          alt="bg"
        />
      </div>
      <div className="bl-size-full bl-flex bl-items-center bl-justify-between bl-gap-2 lg:bl-gap-3 bl-px-4 bl-bg-black/50">
        <div className="bl-text-xl lg:bl-text-3xl bl-font-bold bl-text-secondary bl-duration-200 group-hover:bl-text-white">
          {label}
        </div>
        <div className="bl-text-sm lg:bl-text-xl">
          <span className="bl-text-primary">
            {accomplished !== undefined ? accomplished : '--'}
          </span>
          /<span>{total !== undefined ? total : '--'}</span>
        </div>
      </div>
      <Badge />
    </button>
  );
};

export interface BitlayerPointsProps {
  points?: number;
  gems?: number;
  btr?: number;
  loaderData?: LoaderResponse;
}

const TabContent = ({
  icon,
  value,
  isBtr = false,
}: {
  icon?: string;
  value?: number;
  isBtr: boolean;
}) => {
  const counter = useRef<HTMLSpanElement>(null);
  const { start, update } = useCountUp({
    ref: counter,
    start: 0,
    end: value || 0,
    delay: 0,
    duration: 1,
    decimals: isBtr ? 2 : 0,
  });

  useEffect(() => {
    update(value || 0); // 当 value 更新时重新触发动画
  }, [value, update]);

  useChange(value, (current, prev) => {
    if (current === undefined) return;
    if (prev === undefined) {
      start();
    } else {
      update(current);
    }
  });

  return (
    <div className="bl-h-[70px] lg:bl-h-[100px] bl-flex bl-justify-center bl-border-x bl-border-t bl-border-secondary bl-items-center bl-gap-1 bl-relative">
      <img src={icon} alt="point" className="bl-size-[45px] lg:bl-size-16" />
      <div className="bl-text-[30px] lg:bl-text-[50px] bl-text-white bl-font-bold">
        {value === undefined ? '--' : <span ref={counter} />}
      </div>
    </div>
  );
};

export const BitlayerPoints = ({ points, gems, loaderData, btr }: BitlayerPointsProps) => {
  const { t } = useTranslation();
  const [current, setCurrent] = useState(0);
  const handleTabChange = (value: number) => setCurrent(value);

  const TabItem = ({ label, value }: { label: string; value: number }) => {
    return (
      <button
        className={cn(
          'bl-h-8 bl-text-black bl-text-xs md:bl-text-base/8 bl-text-center bl-font-bold bl-duration-200 hover:bl-text-white focus-visible:bl-outline-none bl-relative bl-z-10',
          {
            'bl-text-white': current === value,
          },
        )}
        onClick={handleTabChange.bind(null, value)}
      >
        {t(label)}
      </button>
    );
  };

  return (
    <div className="bl-w-full bl-relative">
      {current === 0 && (
        <TabContent
          value={points}
          icon={staticAsset('/images/user-center/bitlayer-golden.10071acb9b.gif')}
        />
      )}
      {current === 1 && <TabContent isBtr value={btr} icon="/images/user-center/banner/btr.gif" />}
      {current === 2 && (
        <TabContent
          value={gems}
          icon={staticAsset('/images/user-center/bitlayer-diamond.dbb884b0cf.gif')}
        />
      )}
      <div className="bl-grid bl-grid-cols-3 bl-border-2 bl-border-secondary bl-relative bl-bg-secondary">
        <div
          className={cn(
            'bl-absolute bl-h-full bl-w-1/3 bl-bg-black bl-duration-200 bl-ease-in-out',
            {
              'bl-translate-x-full': current === 1,
              'bl-translate-x-[200%]': current === 2,
            },
          )}
        ></div>
        <TabItem label="pages.userCenter.bitlayerPoints" value={0} />
        <TabItem label="pages.userCenter.btr" value={1} />
        <TabItem label="pages.userCenter.bitlayerGems" value={2} />
      </div>
      {loaderData && (
        <RacersCard
          data={loaderData.rankList}
          meInfo={loaderData.meInfo}
          rankRecords={loaderData.rankRecords}
        />
      )}
    </div>
  );
};
