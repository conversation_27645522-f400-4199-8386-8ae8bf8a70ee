import { useCallback } from 'react';
import {
  ConnectorAlreadyConnectedError,
  ProviderNotFoundError,
  useConnect as useWagmiConnect,
} from 'wagmi';
import { WalletDriverName, walletDrivers } from './common';
import { BaseChainType, NetworkType } from '@/wallets/config/type';
import { UseEnsureOnChain } from './chains';
import { useDialog } from '../dialog';
import { Text } from '@/components/ui/text';
import { SmallWalletButton } from '@/components/featured/wallet';
import { useConnect as useBtcConnect } from '@/wallets/bitcoin/hooks';
import { getConnectors as getWagmiConnectors } from '@/wallets/evm/core/getConnectors';
import { cn } from '@/lib/utils';

export const WalletNotInstallDialog = ({
  id,
  close,
}: {
  id: WalletDriverName;
  close: () => void;
}) => {
  const name = walletDrivers[id].name;
  const Icon = walletDrivers[id].icon;
  const handleClickLink = () => {
    close();
  };
  return (
    <div className="bl-w-72 bl-space-y-6">
      <Text variant="secondary" size="sm" className="bl-text-center bl-text-primary-foreground">
        Looks like you don&apos;t have {name} installed!
      </Text>
      <div className="bl-flex bl-justify-center">
        <SmallWalletButton onClick={handleClickLink} asChild>
          <a target="_blank" rel="noreferrer" href={walletDrivers[id].website}>
            <div className="bl-flex bl-gap-2 bl-items-center">
              <Icon
                className={cn(
                  'bl-size-4 bl-rounded-full bl-shrink-0',
                  name === 'MetaMask' ? 'bl-w-[14px] bl-h-5 bl-ml-[1px]' : '',
                )}
              />
              <span>Get {name}</span>
            </div>
          </a>
        </SmallWalletButton>
      </div>
    </div>
  );
};

interface ConnectOptions {
  onSuccess?: () => void;
  onError?: () => void;
  onSettled?: () => void;
  chain: BaseChainType;
}

export function useConnect() {
  const { connect: wagmiConnect } = useWagmiConnect();
  const { connectors: btcConnectors, connect: btcConnect } = useBtcConnect();
  const { ensure } = UseEnsureOnChain();
  const { open } = useDialog();
  const openNotInstallDialog = useCallback(
    (id: WalletDriverName) => {
      open({
        content: ({ close }) => <WalletNotInstallDialog id={id} close={close} />,
      });
    },
    [open],
  );

  const handleConnect = useCallback(
    async (id: WalletDriverName, chainType: NetworkType, options: ConnectOptions) => {
      if (chainType === NetworkType.btc) {
        const connector = btcConnectors.find((item) => item.id.startsWith(id));
        if (!connector?.installed) {
          return openNotInstallDialog(id);
        }
        await btcConnect(id, {
          onSuccess: async () => {
            options.onSuccess?.();
            await ensure({ chain: options.chain });
          },
          onError: (err: any) => {
            console.warn('connect error', err);
            options.onError?.();
          },
          onSettled: () => {
            options.onSettled?.();
          },
        });
      } else {
        const connectors = getWagmiConnectors();
        const connector = connectors.find((item) => item.id.startsWith(id));
        console.log('connectors', {
          connector,
          connectors,
          connectorId: id,
        });
        if (!connector) {
          return openNotInstallDialog(id);
        }

        wagmiConnect(
          { chainId: options.chain.chain.id, connector },
          {
            onSuccess: async () => {
              options.onSuccess?.();
              if (options.chain) {
                await ensure({ chain: options.chain });
              }
            },
            onError: async (error) => {
              if (error instanceof ConnectorAlreadyConnectedError) {
                console.warn('wallet already connected');
                options.onSuccess?.();
                if (options.chain) {
                  await ensure({ chain: options.chain });
                }
              } else if (error.message.includes('Provider not found')) {
                // TODO: not sure why error instanceof ProviderNotFoundError not working
                openNotInstallDialog(id);
              } else {
                console.warn('connect error', error);
                options.onError?.();
              }
            },
            onSettled: () => {
              options.onSettled?.();
            },
          },
        );
      }
    },
    [btcConnect, btcConnectors, wagmiConnect, ensure, openNotInstallDialog],
  );

  return {
    wallets: walletDrivers,
    connect: handleConnect,
  };
}
