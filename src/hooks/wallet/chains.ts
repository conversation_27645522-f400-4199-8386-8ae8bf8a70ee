import { BaseChainType, NetworkType } from '@/wallets/config/type';
import { chainMap } from '@/wallets/config/chains';
import { useCallback, useMemo } from 'react';
import { useConfig } from 'wagmi';
import { useWallet as useBtcWallet } from '@/wallets/bitcoin/hooks';
import { ResolvedRegister, getAccount, switchChain } from '@wagmi/core';
import { switchChain as btcSwitchChain } from '@/wallets/bitcoin/core';
interface UseChainsOptions {
  network: NetworkType;
  testnet?: boolean;
}

export const useChains = ({ network, testnet }: UseChainsOptions) => {
  const chains = useMemo(() => {
    return Object.values(chainMap).filter((chain) => {
      if (testnet) {
        return chain.networkType === network && chain.testnet;
      }
      return chain.networkType === network && !chain.testnet;
    });
  }, [network, testnet]);
  return chains;
};

interface UseEnsureOnChainOptions {
  chain?: BaseChainType;
}

export const UseEnsureOnChain = () => {
  const { network: btcNetwork } = useBtcWallet();
  const config = useConfig() as unknown as ResolvedRegister['config'];

  const ensure = useCallback(
    async ({ chain }: UseEnsureOnChainOptions) => {
      if (!chain) {
        return;
      }
      if (chain.networkType === NetworkType.btc) {
        const expect = chain.testnet ? 'testnet' : 'livenet';
        if (btcNetwork !== expect) {
          await btcSwitchChain(expect);
        }
      } else if (chain.networkType === NetworkType.evm) {
        const { address, chainId } = getAccount(config);
        if (!address) {
          return;
        }
        if (chainId !== chain.chain.id) {
          // @ts-expect-error ignore this type error
          await switchChain(config, { chainId: chain.chain.id });
        }
      }
    },
    [btcNetwork, config],
  );

  return {
    ensure,
  };
};
