import { NetworkType } from '@/wallets/config/type';
import MetaMaskIcon from '@/components/icons/MetaMaskIcon';
import OKXIcon from '@/components/icons/OKXIcon';
import BitgetIcon from '@/components/icons/BitGetIcon';
import BybitIcon from '@/components/icons/BybitIcon';
import WalletConnectIcon from '@/components/icons/WalletConnectIcon';
import ImTokenIcon from '@/components/icons/ImTokenIcon';
import TokenPocketIcon from '@/components/icons/TokenPocketIcon';
import GateIcon from '@/components/icons/GateIcon';
import DeBoxIcon from '@/components/icons/DeBoxIcon';
import UxuyIcon from '@/components/icons/UxuyIcon';
import FordefiIcon from '@/components/icons/FordefiIcon';
import XverseIcon from '@/components/icons/XverseIcon';
import FoxIcon from '@/components/icons/FoxIcon';
import TomoIcon from '@/components/icons/TomoIcon';
import BinanceIcon from '@/components/icons/BinanceIcon';
import RabbyIcon from '@/components/icons/RabbyIcon';
import SafepalIcon from '@/components/icons/SafepalIcon';
import { isMobile } from 'react-device-detect';
import { useMemo } from 'react';
import { useConnectors as useBtcConnectors } from '@/wallets/bitcoin/hooks';
import UniSatIcon from '@/components/icons/UniSatIcon';
import { inDeBoxApp } from '@/wallets/evm/connectors/deBox';
import { isInTelegram } from '@/wallets/utils';
import { getConnectors as getWagmiConnectors } from '@/wallets/evm/core/getConnectors';

export enum WalletDriverName {
  MetaMask = 'io.metamask',
  MetaMaskMobile = 'io.metamask.mobile',
  OKX = 'com.okex.wallet',
  Bitget = 'com.bitget.web3',
  WalletConnect = 'walletConnect',
  ImToken = 'im.token.app',
  TP = 'pro.tokenpocket',
  Gate = 'io.gate.wallet',
  GateMobile = 'com.gate.wallet',
  Bybit = 'com.bybit',
  DeBox = 'im.deBox.app',
  Uxuy = 'uxuyWallet',
  Fordefi = 'com.fordefi',
  Fox = 'com.foxwallet',
  Tomo = 'inc.tomo',
  TomoTG = 'wallet.tomo.tg',
  Binance = 'BinanceW3WSDK',
  BinanceMobile = 'wallet.binance.com',
  BinanceMobileNew = 'com.binance.wallet',
  Rabby = 'io.rabby',
  RabbyMobile = 'com.debank.rabbymobile',
  Safepal = 'https://www.safepal.com/download',
  SafepalMobile = 'com.safepal.wallet',

  UniSat = 'bitcoin.unisat.wallet',
  OKXBitcoin = 'bitcoin.okex.wallet',
  BitgetBitcoin = 'bitcoin.bitget.wallet',
  TPBitcoin = 'bitcoin.tokenpocket.wallet',
  GateBitcoin = 'bitcoin.gate.wallet',
  BybitBitcoin = 'bitcoin.com.bybit',
  BinanceBitcoin = 'bitcoin.binance.wallet',
  Xverse = 'bitcoin.com.xverse',
  SafepalBitcoin = 'bitcoin.safepal.wallet',
}

type IsVisibleCheckerOptions = {
  isInTelegram: boolean;
  isMobile: boolean;
  isInDebox: boolean;
};
type IsVisibleChecker = (options: IsVisibleCheckerOptions) => boolean;

export interface WalletDriver {
  id: WalletDriverName;
  icon:
    | React.ComponentType<React.SVGProps<SVGSVGElement>>
    | React.ComponentType<React.ImgHTMLAttributes<HTMLImageElement>>;
  name: string;
  network: NetworkType;
  website: string;
  order?: number;
  wallectConnectQRName?: string;
  isVisible?: boolean | IsVisibleChecker;
}

const metamask: WalletDriver = {
  id: WalletDriverName.MetaMask,
  icon: MetaMaskIcon,
  name: 'MetaMask',
  network: NetworkType.evm,
  website: 'https://metamask.io',
  order: 1.5,
  isVisible: ({ isInTelegram, isInDebox }) => !isInDebox && !isInTelegram,
};

const okx: WalletDriver = {
  id: WalletDriverName.OKX,
  icon: OKXIcon,
  name: 'OKX EVM Wallet',
  network: NetworkType.evm,
  website: 'https://www.okx.com/web3',
  order: 2,
  isVisible: ({ isInTelegram }) => !isInTelegram,
};

const bitget: WalletDriver = {
  id: WalletDriverName.Bitget,
  icon: BitgetIcon,
  name: 'Bitget EVM Wallet',
  network: NetworkType.evm,
  website: 'https://web3.bitget.com/',
  order: 5,
  isVisible: ({ isInTelegram }) => !isInTelegram,
};

const walletConnect: WalletDriver = {
  id: WalletDriverName.WalletConnect,
  icon: WalletConnectIcon,
  name: 'WalletConnect',
  network: NetworkType.evm,
  website: 'https://walletconnect.com/',
  order: 99,
  isVisible: true,
};

const imtoken: WalletDriver = {
  id: WalletDriverName.ImToken,
  icon: ImTokenIcon,
  name: 'imToken',
  network: NetworkType.evm,
  website: 'https://token.im/',
  order: 8,
  isVisible: ({ isMobile }) => isMobile,
};

const imtokenPC: WalletDriver = {
  id: WalletDriverName.WalletConnect,
  icon: ImTokenIcon,
  name: 'imToken',
  network: NetworkType.evm,
  website: 'https://token.im/',
  wallectConnectQRName: 'imToken',
  order: 8,
  isVisible: ({ isMobile, isInTelegram }) => !isMobile && !isInTelegram,
};

const tokenPocket: WalletDriver = {
  id: WalletDriverName.TP,
  icon: TokenPocketIcon,
  name: 'TP EVM Wallet',
  network: NetworkType.evm,
  website: 'https://extension.tokenpocket.pro/',
  order: 6,
  isVisible: ({ isInTelegram }) => !isInTelegram,
};

const gate: WalletDriver = {
  id: WalletDriverName.Gate,
  icon: GateIcon,
  name: 'Gate EVM Wallet',
  network: NetworkType.evm,
  website: 'https://www.gate.io/web3/',
  order: 7,
  isVisible: ({ isInTelegram }) => !isInTelegram,
};

const bybit: WalletDriver = {
  id: WalletDriverName.Bybit,
  icon: BybitIcon,
  name: 'Bybit EVM Wallet',
  network: NetworkType.evm,
  website: 'https://www.bybit.com/web3/home/',
  order: 3,
  isVisible: ({ isInTelegram }) => !isInTelegram,
};

const deBox: WalletDriver = {
  id: WalletDriverName.DeBox,
  icon: DeBoxIcon,
  name: 'Debox Wallet',
  network: NetworkType.evm,
  website: 'https://debox.pro/download/',
  order: 11,
  isVisible: ({ isMobile, isInTelegram }) => isMobile && !isInTelegram,
};

const uxuy: WalletDriver = {
  id: WalletDriverName.Uxuy,
  icon: UxuyIcon,
  name: 'UXUY Wallet(TG)',
  network: NetworkType.evm,
  website: 'https://uxuy.com/wallet/',
  order: 4,
  isVisible: false,
};

const fordefi: WalletDriver = {
  id: WalletDriverName.Fordefi,
  icon: FordefiIcon,
  name: 'Fordefi Wallet',
  network: NetworkType.evm,
  website: 'https://chromewebstore.google.com/detail/fordefi/hcmehenccjdmfbojapcbcofkgdpbnlle',
  order: 10,
  isVisible: ({ isMobile, isInTelegram }) => !isMobile && !isInTelegram,
};

const tomo: WalletDriver = {
  id: WalletDriverName.Tomo,
  icon: TomoIcon,
  name: 'Tomo Wallet',
  network: NetworkType.evm,
  website: 'https://tomo.inc',
  order: 9,
  isVisible: ({ isMobile, isInTelegram }) => !isMobile && !isInTelegram,
};

const tomoTG: WalletDriver = {
  ...tomo,
  id: WalletDriverName.TomoTG,
  name: 'Tomo Wallet(TG)',
  isVisible: false,
};

const fox: WalletDriver = {
  id: WalletDriverName.Fox,
  icon: FoxIcon,
  name: 'Fox Wallet',
  network: NetworkType.evm,
  website: 'https://foxwallet.com/download',
  order: 10,
  isVisible: ({ isMobile, isInTelegram }) => isMobile && !isInTelegram,
};

const binance: WalletDriver = {
  id: WalletDriverName.Binance,
  icon: BinanceIcon,
  name: 'Binance Wallet',
  network: NetworkType.evm,
  website: 'https://www.binance.com/en/download',
  order: 1,
  isVisible: ({ isMobile, isInTelegram }) => !isMobile && !isInTelegram,
};

const binanceMobile: WalletDriver = {
  ...binance,
  id: WalletDriverName.BinanceMobile,
  isVisible: ({ isMobile }) => {
    const injected = getWagmiConnectors().find((connector) =>
      connector.id.startsWith(WalletDriverName.BinanceMobile),
    );
    return isMobile && !!injected;
  },
};

const binanceMobileNew: WalletDriver = {
  ...binance,
  id: WalletDriverName.BinanceMobileNew,
  isVisible: ({ isMobile }) => {
    const injected = getWagmiConnectors().find((connector) =>
      connector.id.startsWith(WalletDriverName.BinanceMobile),
    );
    return isMobile && !injected;
  },
};

const rabby: WalletDriver = {
  id: WalletDriverName.Rabby,
  icon: RabbyIcon,
  name: 'Rabby Wallet',
  network: NetworkType.evm,
  website: 'https://rabby.io/',
  order: 5.5,
  isVisible: ({ isMobile, isInTelegram }) => !isMobile && !isInTelegram,
};

const rabbyMobile: WalletDriver = {
  ...rabby,
  id: WalletDriverName.RabbyMobile,
  isVisible: ({ isMobile, isInTelegram }) => isMobile && !isInTelegram,
};

const safepal: WalletDriver = {
  id: WalletDriverName.SafepalMobile,
  icon: SafepalIcon,
  name: 'SafePal Wallet',
  network: NetworkType.evm,
  website: 'https://www.safepal.com/download',
  order: 2.5,
  isVisible: true,
};

/* btc */
const unisat: WalletDriver = {
  id: WalletDriverName.UniSat,
  icon: UniSatIcon,
  name: 'UniSat Wallet',
  network: NetworkType.btc,
  website: 'https://unisat.io',
  order: 1,
};

const okxBitcoin: WalletDriver = {
  id: WalletDriverName.OKXBitcoin,
  icon: OKXIcon,
  name: 'OKX BTC Wallet',
  network: NetworkType.btc,
  website: 'https://www.okx.com/web3',
  order: 2,
};

const bybitBitcoin: WalletDriver = {
  id: WalletDriverName.BybitBitcoin,
  icon: BybitIcon,
  name: 'Bybit BTC Wallet',
  network: NetworkType.btc,
  website: 'https://www.bybit.com/web3/home/',
  order: 3,
};

const bitgetBitcoin: WalletDriver = {
  id: WalletDriverName.BitgetBitcoin,
  icon: BitgetIcon,
  name: 'Bitget BTC Wallet',
  network: NetworkType.btc,
  website: 'https://web3.bitget.com/',
  order: 4,
};

const tokenPocketBitcoin: WalletDriver = {
  id: WalletDriverName.TPBitcoin,
  icon: TokenPocketIcon,
  name: 'TP BTC Wallet',
  network: NetworkType.btc,
  website: 'https://extension.tokenpocket.pro/',
  order: 5,
};

const xverse: WalletDriver = {
  id: WalletDriverName.Xverse,
  icon: XverseIcon,
  name: 'Xverse Wallet',
  network: NetworkType.btc,
  website: 'https://www.xverse.app/download/',
  order: 5,
};

const binanceBitcoin: WalletDriver = {
  id: WalletDriverName.BinanceBitcoin,
  icon: BinanceIcon,
  name: 'Binance BTC Wallet',
  network: NetworkType.btc,
  website: 'https://www.binance.com/web3wallet',
  order: 3,
};

const safepalBitcoin: WalletDriver = {
  id: WalletDriverName.SafepalBitcoin,
  icon: SafepalIcon,
  name: 'SafePal Wallet',
  network: NetworkType.btc,
  website: 'https://www.safepal.com/download',
  order: 3,
};

export const walletDrivers: Record<WalletDriverName | string, WalletDriver> = {
  [WalletDriverName.MetaMask]: metamask,
  [WalletDriverName.MetaMaskMobile]: metamask,
  [WalletDriverName.OKX]: okx,
  [WalletDriverName.Bitget]: bitget,
  [WalletDriverName.WalletConnect]: walletConnect,
  [WalletDriverName.ImToken]: imtoken,
  [WalletDriverName.TP]: tokenPocket,
  [WalletDriverName.Gate]: gate,
  [WalletDriverName.GateMobile]: gate,
  [WalletDriverName.Bybit]: bybit,
  [WalletDriverName.DeBox]: deBox,
  [WalletDriverName.Uxuy]: uxuy,
  [WalletDriverName.Fordefi]: fordefi,
  [WalletDriverName.Fox]: fox,
  [WalletDriverName.Tomo]: tomo,
  [WalletDriverName.TomoTG]: tomoTG,
  [WalletDriverName.Binance]: binance,
  [WalletDriverName.BinanceMobile]: binanceMobile,
  [WalletDriverName.BinanceMobileNew]: binanceMobileNew,
  [WalletDriverName.Rabby]: rabby,
  [WalletDriverName.RabbyMobile]: rabbyMobile,
  [WalletDriverName.Safepal]: safepal,
  [WalletDriverName.SafepalMobile]: safepal,

  // btc connector
  [WalletDriverName.UniSat]: unisat,
  [WalletDriverName.OKXBitcoin]: okxBitcoin,
  [WalletDriverName.BitgetBitcoin]: bitgetBitcoin,
  [WalletDriverName.TPBitcoin]: tokenPocketBitcoin,
  [WalletDriverName.BybitBitcoin]: bybitBitcoin,
  [WalletDriverName.BinanceBitcoin]: binanceBitcoin,
  [WalletDriverName.Xverse]: xverse,
  [WalletDriverName.SafepalBitcoin]: safepalBitcoin,
};
const binanceMobiles = [binanceMobile, binanceMobileNew];
const mobileWallets = [imtoken, tomoTG, fox, deBox, rabbyMobile];
const webWallets = [binance, imtokenPC, tomo, fordefi, rabby, safepal];
const popularEVMWallets = [metamask, okx, bybit, uxuy, bitget];
const otherEVMWallets = [tokenPocket, gate];
// const tgEVMWallets = [uxuy, tomoTG];

export function useChainWallets(network?: NetworkType) {
  const btcConnectors = useBtcConnectors() as WalletDriver[];

  const evmWallets = useMemo(() => {
    const wallets = [
      ...popularEVMWallets,
      ...otherEVMWallets,
      ...mobileWallets,
      ...webWallets,
      ...binanceMobiles,
      walletConnect,
    ];
    const visibleOptions = {
      isInDebox: inDeBoxApp(),
      isMobile,
      isInTelegram: isInTelegram(),
    };
    return wallets
      .filter((wallet) => {
        if (typeof wallet.isVisible === 'function') {
          return wallet.isVisible(visibleOptions);
        }
        return wallet.isVisible !== false;
      })
      .sort((a, b) => (a.order ?? Infinity) - (b.order ?? Infinity));
  }, []);

  const btcWallets = useMemo(() => {
    const visibleOptions = {
      isInDebox: inDeBoxApp(),
      isMobile,
      isInTelegram: isInTelegram(),
    };
    return btcConnectors.filter((wallet) => {
      if (typeof wallet.isVisible === 'function') {
        return wallet.isVisible(visibleOptions);
      }
      return wallet.isVisible !== false;
    });
  }, [btcConnectors]);

  return useMemo(() => {
    if (network) {
      if (network === NetworkType.btc) {
        return btcWallets;
      } else {
        return evmWallets;
      }
    } else {
      return [...evmWallets, ...btcWallets];
    }
  }, [btcWallets, evmWallets, network]);
}
