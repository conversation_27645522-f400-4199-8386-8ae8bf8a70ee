import { Client } from './client';
import { FaucetAPI, StrAPI, GasAPI } from './api';
import { AppLoadContext } from '@remix-run/cloudflare';

class API {
  public readonly faucet: FaucetAPI;
  public readonly strapi: StrAPI;
  public readonly gasapi: Gas<PERSON>I;

  constructor(client: Client, strapiClient: Client, gasClient: Client) {
    this.faucet = new FaucetAPI(client);
    this.strapi = new StrAPI(strapiClient);
    this.gasapi = new GasAPI(gasClient);
  }
}

export function getEnvValue(context: AppLoadContext, name: string): string {
  if (context.cloudflare?.env) {
    return context.cloudflare.env[name] || '';
  }
  if (typeof process !== 'undefined') {
    return process.env[name] || '';
  }
  return '';
}

export const createAPI = (context: AppLoadContext) => {
  const client = new Client(getEnvValue(context, 'API_TEST_NET_URL'));
  const strapiClient = new Client(getEnvValue(context, 'API_STRAPI_URL'));
  const gasClient = new Client(getEnvValue(context, 'API_GAS_URL'));
  strapiClient.setToken(getEnvValue(context, 'API_STRAPI_TOKEN'));
  return new API(client, strapiClient, gasClient);
};

export { APIClientError, APIServerError } from './client';
