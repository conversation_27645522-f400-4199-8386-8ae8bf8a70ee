import en from '@/locales/en';
import zhCN from '@/locales/zh-CN';
import vi from '@/locales/vi-VN';
import zhHC from '@/locales/zh-TW';
import ja from '@/locales/ja-JP';
// import ko from '@/locales/ko-KR';
import id from '@/locales/id-ID';
import ru from '@/locales/ru-RU';

// This is the list of languages your application supports
export const supportedLngs = ['en', 'zh-CN', 'vi', 'zh-HC', 'ja', 'ko', 'id', 'ru'];

// This is the language you want to use in case
// if the user language is not in the supportedLngs
export const fallbackLng = 'en';

// The default namespace of i18next is "translation", but you can customize it
// here
export const defaultNS = 'translation';
export const interpolation = {
  escapeValue: false, // not needed for react!!
};

export const resources = {
  en: { translation: en },
  vi: { translation: vi },
  'zh-CN': { translation: zhCN },
  'zh-HC': { translation: zhHC },
  ja: { translation: ja },
  // ko: { translation: ko },
  id: { translation: id },
  ru: { translation: ru },
};
