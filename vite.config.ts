import { defineConfig } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';
import Unfonts from 'unplugin-fonts/vite';
import {
  vitePlugin as remix,
  cloudflareDevProxyVitePlugin as remixCloudflareDevProxy,
} from '@remix-run/dev';
import { flatRoutes } from 'remix-flat-routes';
import { nodePolyfills } from 'vite-plugin-node-polyfills';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    remixCloudflareDevProxy(),
    remix({
      appDirectory: 'src',
      ignoredRouteFiles: ['**/*'],
      routes: async (defineRoutes) => {
        return flatRoutes('routes', defineRoutes, {
          appDir: 'src',
        });
      },
    }),
    nodePolyfills({
      include: ['path', 'crypto', 'stream', 'util'],
      exclude: ['http'],
      globals: {
        Buffer: true,
        global: true,
        process: true,
      },
      overrides: {
        fs: 'memfs',
      },
      protocolImports: true,
    }),
    Unfonts({
      custom: {
        families: [
          {
            name: 'Hammer Tongs',
            local: 'Hammer Tongs',
            src: './src/assets/fonts/Hammer & Tongs.*',
          },
        ],
        preload: true,
      },
    }),
    tsconfigPaths(),
  ],
  // server: {
  //   host: '0.0.0.0',
  //   proxy: {
  //     '/api/pub/v1': {
  //       target: 'http://*********:6000/',
  //       changeOrigin: true,
  //       cookiePathRewrite: {
  //         '*': '/',
  //       },
  //     },
  //   },
  // },
});
